<?php

namespace Webkul\RestApi\Docs\Shop\Controllers;

/**
 * @OA\Info(
 *      version="2.2.0",
 *      title="Bagisto Store Front Rest API Documentation",
 *      description="Bagisto Store Front Rest API Documentation",
 *
 *      @OA\Contact(
 *          email="<EMAIL>"
 *      )
 * )
 *
 * @OA\Server(
 *      url=APP_URL,
 *      description="Bagisto Rest API"
 * )
 *
 * @OA\Tag(
 *     name="Configs",
 *     description="API Endpoints of Core Config"
 * )
 * @OA\Tag(
 *     name="Locales",
 *     description="API Endpoints of Locale"
 * )
 * @OA\Tag(
 *     name="Currencies",
 *     description="API Endpoints of Currency"
 * )
 * @OA\Tag(
 *     name="Channels",
 *     description="API Endpoints of Channel"
 * )
 * @OA\Tag(
 *     name="Countries",
 *     description="API Endpoints of Country"
 * )
 * @OA\Tag(
 *     name="AttributeFamilies",
 *     description="API Endpoints of AttributeFamily"
 * )
 * @OA\Tag(
 *     name="Attributes",
 *     description="API Endpoints of Attribute"
 * )
 * @OA\Tag(
 *     name="Categories",
 *     description="API Endpoints of Category"
 * )
 * @OA\Tag(
 *     name="Products",
 *     description="API Endpoints of Product"
 * )
 * @OA\Tag(
 *     name="Customers",
 *     description="API Endpoints of Customer"
 * )
 * @OA\Tag(
 *     name="Addresses",
 *     description="API Endpoints of Address"
 * )
 * @OA\Tag(
 *     name="GDPR",
 *     description="API Endpoints of GDPR"
 * )
 * @OA\Tag(
 *     name="Orders",
 *     description="API Endpoints of Order"
 * )
 * @OA\Tag(
 *     name="ReOrder",
 *     description="API Endpoints of Re Order"
 * )
 * @OA\Tag(
 *     name="Invoices",
 *     description="API Endpoints of Invoice"
 * )
 * @OA\Tag(
 *     name="Shipments",
 *     description="API Endpoints of Shipment"
 * )
 * @OA\Tag(
 *     name="Transactions",
 *     description="API Endpoints of Transaction"
 * )
 * @OA\Tag(
 *     name="Themes",
 *     description="API Endpoints of Themes"
 * )
 * @OA\Tag(
 *     name="Wishlists",
 *     description="API Endpoints of Wishlist"
 * )
 * @OA\Tag(
 *     name="Cart",
 *     description="API Endpoints of Cart"
 * )
 * @OA\Tag(
 *     name="Checkout",
 *     description="API Endpoints of Checkout"
 * )
 * @OA\Tag(
 *     name="Newsletter",
 *     description="API Endpoints of Newsletters"
 * )
 */
class Controller
{
}
