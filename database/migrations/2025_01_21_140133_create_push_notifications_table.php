<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {

        Schema::create('push_notifications', function (Blueprint $table) {
            $table->id();
            $table->string('notification_title')->nullable();
            $table->string('notification_content')->nullable();
            $table->tinyInteger('notification_status')->nullable();
            $table->json('customer_group');
            $table->json('notification_cron_data');
            $table->timestamps();
        });

    } /** end up() **/

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {

        Schema::dropIfExists('push_notifications');

    } /** end down() **/

};  /** end return new class extends Migration **/
