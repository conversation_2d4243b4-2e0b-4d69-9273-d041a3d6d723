<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('funding_rewards', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id');
            $table->integer('customer_id');
            $table->integer('funding_time'); // 12
            $table->integer('funding_period'); // 3
            $table->integer('funding_period_current_step'); // 1
            $table->integer('funding_period_max_step'); // 4
            $table->decimal('funding_reward_amount', 12, 4)->default(0)->nullable();
            $table->string('funding_reward_currency');
            $table->timestamp('funding_period_reward_status')->nullable();
            $table->timestamp('funding_period_reward_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('funding_rewards');
    }
};
