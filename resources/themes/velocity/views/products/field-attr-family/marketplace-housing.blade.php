@if($product->getAttributeFamilyAttribute()->id === 4)
    @php
        /** Attribute value **/
        $attributes = \Webkul\Product\Models\ProductAttributeValue::where(['product_id' => $product->product_id])->get()->mapWithKeys(function ($item) {
            return [$item->attribute()->where('id', $item->attribute_id)->first()->code => [
                    'text'     => $item->text_value,
                ]
            ];
        });
    @endphp
    <product-view>
        <div class="form-container">
            @csrf()
            <input type="hidden" name="product_id" value="{{ $product->product_id }}" />
            @php
                $currencySymbol = core()->getCurrentCurrency()->symbol;
            @endphp
            <div class="flex space-x-2 items-center ml-15 mb-3">
                <div class="font-terramirum font-semibold text-black text-sm lg:text-lg tracking-normal leading-none">
                    <a class="unset" href="/">
                        {{ __('velocity::app-static.products.breadcrumb-part1') }}
                    </a>
                </div>
                <div>
                    <img src="/deneme/svg/arrow.svg" alt="" />
                </div>
                @php
                    $breadcrumbCagetories = breadcrumbCagetories( $product );
                @endphp
                @if(isset($breadcrumbCagetories))
                    @foreach($breadcrumbCagetories AS $value)
                        <a class="unset" href="{!! $value->route !!}">
                            <div class="font-terramirum font-semibold text-black text-sm lg:text-lg tracking-normal leading-none">
                                {{ $value->text ?? 'Yok' }}
                            </div>
                        </a>
                        <div>
                            <img src="/deneme/svg/arrow.svg" alt="" />
                        </div>
                    @endforeach
                @endif
                <div class="font-terramirum font-semibold text-terra-khaki-green text-sm lg:text-lg tracking-normal leading-none">
                    {{ $attributes['name']['text'] ?? null }}
                </div>
            </div>
            <div class="row">
                <div class="left col-lg-5 col-md-6">
                    @include ('shop::products.view.gallery')
                </div>
                <div class="px-0 lg:px-4 right col-lg-7 col-md-6">
                    <div class="px-0 lg:px-4 col-lg-12 mb-2">
                        <div class="flex items-center font-terramirum font-semibold text-black text-sm tracking-normal leading-none">
                            <div class="text-terra-khaki-green rounded-full bg-terra-light-wheat px-2 py-1 flex space-x-2 hidden">
                                <span>
                                    {{ __('velocity::app-static.products.product-cat-text') }}
                                </span>
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="7.885" height="14.259" viewBox="0 0 7.885 14.259">
                                    <g id="Group_1801" data-name="Group 1801" transform="translate(0 14.259) rotate(-90)">
                                        <g id="Group_142" data-name="Group 142">
                                            <path id="Path_262" data-name="Path 262" d="M7.126,6.39,13.516,0l.743.754L7.128,7.885,0,.757.75.014,7.126,6.39" transform="translate(0 0)" fill="#79914e" />
                                        </g>
                                    </g>
                                </svg>
                            </div>
                        </div>
                        <h2 class="font-terramirum font-semibold text-black text-xl tracking-normal mt-1">
                            {{ $attributes['name']['text'] ?? null }}
                        </h2>
                        <div class="bg-terra-light-wheat py-3 rounded-2xl flex flex-wrap justify-around my-2">
                            <div class="w-full lg:w-1/2 px-2 lg:px-6 mb-2">
                                <div class="bg-white rounded-2xl flex flex-col items-center justify-center py-1" style="box-shadow: 0 0px 6px rgb(0 0 0 / 16%) inset">
                                    <div class="text-terra-khaki-green text-sm font-terramirum font-semibold mb-0.5 uppercase">
                                        {{ __('velocity::app-static.products.property-value') }}
                                    </div>
                                    <div class="text-black text-base font-terramirum font-bold px-2 leading-none bg-terra-yellow py-1 rounded-lg">
                                        {{ $currencySymbol }} {{ $attributes['deger']['text'] ?? null }}
                                    </div>
                                </div>
                            </div>
                            <div class="w-full lg:w-1/2 px-2 lg:px-6 mb-2">
                                <div class="bg-white rounded-2xl flex flex-col items-center justify-center py-1" style="box-shadow: 0 0px 6px rgb(0 0 0 / 16%) inset">
                                    <div class="text-terra-khaki-green text-sm font-terramirum font-semibold mb-0.5 uppercase">
                                        m<sup>2</sup> {{ __('velocity::app-static.products.price') }}
                                    </div>
                                    <div class="text-black text-base font-terramirum font-bold px-2 leading-none bg-terra-yellow py-1 rounded-lg">
                                        {{ $currencySymbol }} {{ $attributes['birimFiyat']['text'] ?? null }}
                                    </div>
                                </div>
                            </div>
                            <div class="w-full lg:w-1/2 px-2 lg:px-6 mb-2">
                                <div class="bg-white rounded-2xl flex flex-col items-center justify-center py-1" style="box-shadow: 0 0px 6px rgb(0 0 0 / 16%) inset">
                                    <div class="text-terra-khaki-green text-sm font-terramirum font-semibold mb-0.5 uppercase">
                                        {{ __('velocity::app-static.products.bath-color') }}
                                    </div>
                                    <div class="text-black text-base font-terramirum font-bold px-2 leading-none py-1">
                                        {{ $attributes['toplamPay']['text'] ?? null }}
                                    </div>
                                </div>
                            </div>
                            <div class="w-full lg:w-1/2 px-2 lg:px-6 mb-2">
                                <div class="bg-white rounded-2xl flex flex-col items-center justify-center py-1" style="box-shadow: 0 0px 6px rgb(0 0 0 / 16%) inset">
                                    <div class="text-terra-khaki-green text-sm font-terramirum font-semibold mb-0.5 uppercase">
                                        {{ __('velocity::app-static.products.parking') }}
                                    </div>
                                    <div class="text-black text-base font-terramirum font-bold px-2 leading-none py-1">
                                        {{ $attributes['dpgs']['text'] ?? null }}
                                    </div>
                                </div>
                            </div>
                            <div class="w-full lg:w-1/2 px-2 lg:px-6 mb-2">
                                <div class="bg-white rounded-2xl flex flex-col items-center justify-center py-1" style="box-shadow: 0 0px 6px rgb(0 0 0 / 16%) inset">
                                    <div class="text-terra-khaki-green text-sm font-terramirum font-semibold mb-0.5 uppercase">
                                        {{ __('velocity::app-static.products.room') }}
                                    </div>
                                    <div class="text-black text-base font-terramirum font-bold px-2 leading-none py-1">
                                        {{ $attributes['konutKullanim']['text'] ?? null }}
                                    </div>
                                </div>
                            </div>
                            <div class="w-full lg:w-1/2 px-2 lg:px-6 mb-2">
                                <div class="bg-white rounded-2xl flex flex-col items-center justify-center py-1" style="box-shadow: 0 0px 6px rgb(0 0 0 / 16%) inset">
                                    <div class="text-terra-khaki-green text-sm font-terramirum font-semibold mb-0.5 uppercase">
                                        {{ __('velocity::app-static.products.kitchen-color') }}
                                    </div>
                                    <div class="text-black text-base font-terramirum font-bold px-2 leading-none py-1">
                                        {{ $attributes['binaTipi']['text'] ?? null }}
                                    </div>
                                </div>
                            </div>
                            <div class="w-full lg:w-1/2 px-2 lg:px-6 mb-2">
                                <div class="bg-white rounded-2xl flex flex-col items-center justify-center py-1" style="box-shadow: 0 0px 6px rgb(0 0 0 / 16%) inset">
                                    <div class="text-terra-khaki-green text-sm font-terramirum font-semibold mb-0.5 uppercase">
                                        {{ __('velocity::app-static.products.usage-area-sqfit') }}
                                    </div>
                                    <div class="text-black text-base font-terramirum font-bold px-2 leading-none py-1">
                                        {{ $attributes['kullanimAlani']['text'] ?? null }}
                                    </div>
                                </div>
                            </div>
                            <div class="w-full lg:w-1/2 px-2 lg:px-6 mb-2">
                                <div class="bg-white rounded-2xl flex flex-col items-center justify-center py-1" style="box-shadow: 0 0px 6px rgb(0 0 0 / 16%) inset">
                                    <div class="text-terra-khaki-green text-sm font-terramirum font-semibold mb-0.5 uppercase">
                                        {{ __('velocity::app-static.products.using-status') }}
                                    </div>
                                    <div class="text-black text-base font-terramirum font-bold px-2 leading-none py-1">
                                        {{ $attributes['kullanimDurumu']['text'] ?? null }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="my-2 w-full flex justify-around" id="product-star">
                            <quantity-changer quantity-text="Quantity"></quantity-changer>
                            @include ('shop::products.add-to-cart', [
                                'form' => false,
                                'product' => $product,
                                'showCartIcon' => false,
                                'showWishlist' => false,
                                'showCompare' => core()->getConfigData('general.content.shop.compare_option') == "1" ? true : false,
                            ])
                        </div>
                        <div class="w-full rounded-27p">
                            @php
                                $iframex = str_replace(['width="600"', 'height="450"', 'style="border:0;"'], ['width="100%"', 'height="225"', 'style="border:0; border-radius: 27px;"'], $attributes['iframe']['text']);
                            @endphp
                            {!! $iframex !!}
                        </div>
                        @if (count($product->getTypeInstance()->getCustomerGroupPricingOffers()) > 0)
                            <div class="col-12">
                                @foreach ($product->getTypeInstance()->getCustomerGroupPricingOffers() as $offers)
                                    {{ $offers }} <br>
                                @endforeach
                            </div>
                        @endif
                        <div class="col-12 product-actions">
                            @if (core()->getConfigData('catalog.products.storefront.buy_now_button_display'))
                                @include ('shop::products.buy-now', [
                                    'product' => $product,
                                ])
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </product-view>

    <section class="py-4 bg-white">
        <div class="row lg:ml-3">
            <div class="rounded-34p p-3 lg:p-6 px-5 lg:px-18" style=" box-shadow: 0 0px 10px rgb(0 0 0 / 16%)">
                <div class="font-terramirum font-bold text-2xl mb-6">
                    {{ __('velocity::app-static.products.details')}}
                </div>
                <div class="flex flex-wrap lg:!flex-nowrap justify-center lg:space-x-12 -mx-5">
                    <div class="w-1/3 lg:w-1/6 flex flex-col justify-start items-center px-5">
                        <img src="/deneme/svg/detay1.svg" alt="" />
                    </div>
                    <div class="w-1/3 lg:w-1/6 flex flex-col justify-start items-center px-5">
                        <img src="/deneme/svg/detay2.svg" alt="" />
                    </div>
                    <div class="w-1/3 lg:w-1/6 flex flex-col justify-start items-center px-5">
                        <img src="/deneme/svg/detay3.svg" alt="" />
                    </div>
                    <div class="w-1/3 lg:w-1/6 flex lg:hidden flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-sm lg:text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.deed') }}
                        </div>
                    </div>
                    <div class="w-1/3 lg:w-1/6 flex lg:hidden flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-sm lg:text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.parcel-inquiry') }}
                        </div>
                    </div>
                    <div class="w-1/3 lg:w-1/6 flex lg:hidden flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-sm lg:text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.land-img') }}
                        </div>
                    </div>
                    <div class="w-1/3 lg:w-1/6 flex flex-col justify-start items-center px-5">
                        <img src="/deneme/svg/detay4.svg" alt="" />
                    </div>
                    <div class="w-1/3 lg:w-1/6 flex flex-col justify-start items-center px-5">
                        <img src="/deneme/svg/detay5.svg" alt="" />
                    </div>
                    <div class="w-1/3 lg:w-1/6 flex flex-col justify-start items-center px-5">
                        <img src="/deneme/svg/detay6.svg" alt="" />
                    </div>
                    <div class="w-1/3 lg:w-1/6 flex lg:hidden flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-sm lg:text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.zoning-info') }}
                        </div>
                    </div>
                    <div class="w-1/3 lg:w-1/6 flex lg:hidden flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-sm lg:text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.valuation') }}
                        </div>
                    </div>
                    <div class="w-1/3 lg:w-1/6 flex lg:hidden flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-sm lg:text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.confirm-letter') }}
                        </div>
                    </div>
                </div>
                <div class="hidden lg:flex justify-center space-x-12 -mx-5">
                    <div class="w-1/6 flex flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.deed') }}
                        </div>
                    </div>
                    <div class="w-1/6 flex flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.parcel-inquiry') }}
                        </div>
                    </div>
                    <div class="w-1/6 flex flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.land-img') }}
                        </div>
                    </div>
                    <div class="w-1/6 flex flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.zoning-info') }}
                        </div>
                    </div>
                    <div class="w-1/6 flex flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.valuation') }}
                        </div>
                    </div>
                    <div class="w-1/6 flex flex-col justify-start items-center px-5">
                        <div class="font-terramirum font-semibold text-lg text-center tracking-wide my-4">
                            {{ __('velocity::app.products.confirm-letter') }}
                        </div>
                    </div>
                </div>
                <div class="hidden border-t-2 border-t-bordergray pt-4 font-terramirum font-semibold text-lg text-justify tracking-wide my-4">
                    {{ $product->description }}
                </div>
            </div>
        </div>
    </section>
@endif