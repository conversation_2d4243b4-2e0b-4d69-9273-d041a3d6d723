@extends('shop::layouts.master')

@section('page_title')
    {{ __('shop::app.checkout.success.title') }}
@stop

@section('content-wrapper')
    <section class="mt-6 bg-cover bg-center " style="background-image: url('/logo-bg.png')">
        <div class="container py-15">
            <div class="flex mx-auto w-full py-8 rounded-2lg bg-[#FFF8D9] shadow-searchshadow lg:px-15">
                <div class="w-1/6">
                    <svg xmlns="http://www.w3.org/2000/svg" width="99.72" height="91.5" viewBox="0 0 99.72 91.5">
                        <g id="Group_4300" data-name="Group 4300" transform="translate(-118.28 -176.5)">
                            <ellipse id="Ellipse_97" data-name="Ellipse 97" cx="45.5" cy="42" rx="45.5" ry="42" transform="translate(127 184)" fill="rgba(121,145,78,0.31)" />
                            <g id="Group_3441" data-name="Group 3441" transform="translate(118.061 176.281)">
                                <g id="Group_3442" data-name="Group 3442" transform="translate(0 0)">
                                    <path id="Path_1963" data-name="Path 1963"
                                          d="M25.743,34.105a6.958,6.958,0,0,1,.57-.756q9.083-9.1,18.178-18.189L44.664,15a3.277,3.277,0,0,1,4.86.037,3.343,3.343,0,0,1,.026,4.851C47.8,21.7,46,23.457,44.22,25.24Q36.255,33.209,28.3,41.179a3.769,3.769,0,0,1-1.911,1.166,3.12,3.12,0,0,1-2.975-.834c-1.588-1.549-3.139-3.135-4.708-4.7q-2.886-2.889-5.787-5.776a4.321,4.321,0,0,1-1.358-2.182,3.511,3.511,0,0,1,1.718-3.709,3.317,3.317,0,0,1,4.075.5c1.748,1.7,3.458,3.443,5.176,5.174,1.038,1.044,2.06,2.1,3.217,3.289"
                                          transform="translate(13.405 16.257)" fill="#758b4a" />
                                    <path id="Path_1964" data-name="Path 1964" d="M42.721,0A42.721,42.721,0,1,1,0,42.721,42.721,42.721,0,0,1,42.721,0Z" transform="translate(1.719 1.719)" fill="none" stroke="#758b4a" stroke-width="3" />
                                </g>
                            </g>
                        </g>
                    </svg>
                </div>
                <div class="w-5/6">
                    <div class="font-terramirum font-bold text-2xl tracking-wide pb-3">
                        {{-- Siparişiniz alındı, Hayırlı olsun. --}}
                        {{ __('velocity::app-static.orders.title') }}
                    </div>
                    <div class="font-terramirum font-bold text-[#707070] text-base tracking-wide pb-3">
                        @if(env('APP_LOCALE') == "tr")
                             Siparişiniz ile ilgili detaylar <b>{{ auth('customer')->user()->email }} </b> adresine gönderildi.
                        @endif

                        @if(env('APP_LOCALE') == "en")
                            {{ __('velocity::app-static.orders.content') }} <b>{{ auth('customer')->user()->email }}</b> .
                        @endif

                    </div>
                    <div class="flex self-center">
                        <a href="{{ route('customer.orders.index') }}" class="font-terramirum bg-terra-orange text-white rounded-2xl py-2 px-10 self-center text-xl tracking" style="box-shadow: 0 3px 6px rgb(0 0 0 /16%);">
                            {{-- Satın aldıklarıma git --}}
                            {{ __('velocity::app-static.orders.btn-text') }}
                        </a>
                    </div>
                    <div class="flex mt-12">
                        <div class="w-1/3">
                            <div class="font-terramirum font-bold text-lg tracking-wide text-terra-khaki-green pb-2">
                                {{-- Sipariş Numarası --}}
                                {{ __('velocity::app-static.orders.order-no') }}
                            </div>
                            <div class="font-terramirum font-bold text-base tracking-wide pb-2">#{{ $order->increment_id }}</div>
                        </div>
                        <div class="w-1/3">
                            <div class="font-terramirum font-bold text-lg tracking-wide text-terra-khaki-green pb-2">
                                {{-- Fatura Bilgileri --}}
                                {{ __('velocity::app-static.orders.billing') }}
                            </div>
                            {{-- <div class="font-terramirum font-bold text-xs tracking-wide text-[#707070] pb-2">Acıbadem Mah. Gömeç Sok. <br>
                                Akgün İş Merkezi No:37/4 <br>
                                Kadıköy/İstanbul <br>
                                <EMAIL>
                            </div> --}}
                            <div class="font-terramirum font-bold text-xs tracking-wide text-[#707070] pb-2">@include ('admin::sales.address', ['address' => $order->billing_address])
                            </div>
                        </div>
                        <div class="w-1/3">
                            <div class="font-terramirum font-bold text-lg tracking-wide text-terra-khaki-green pb-2">
                                {{-- Ödenen Tutar --}}
                                {{ __('velocity::app-static.orders.amount') }}
                            </div>
                            <div class="font-terramirum font-bold text-base tracking-wide pb-2">{{ core()->formatPrice($order->grand_total, $order->order_currency_code) }}</div>
                        </div>
                    </div>

                    @if ($order->payment->method == "moneytransfer")
                        <div class="font-terramirum font-bold text-lg tracking-wide text-terra-khaki-green pb-2">
                            {{ __('velocity::app-static.orders.bank-info') }}
                        </div>
                        <div class="flex mt-12">
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.bank-name') }}:
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> Akbank T.A.Ş. </div>    
                            </div>
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.account-name') }}:
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> THORNE BILISIM A.S. </div>    
                            </div>
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.iban') }}(TRY): 
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> TR81 0004 6006 4588 8013 4353 39 </div>
                            </div>
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.branch-code') }}: 
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> 0046 </div>
                            </div>

                        </div>
                        <div class="flex mt-12">
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.bank-name') }}:
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> Finansbank </div>    
                            </div>
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.account-name') }}: 
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> THORNE BILISIM A.S. </div>    
                            </div>
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.iban') }}(EUR): 
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> TR60 0011 1000 0000 0126 8147 06 </div>
                            </div>
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.branch-code') }}: 
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> 03663 </div>
                            </div>

                        </div>
                        <div class="flex mt-12">
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.bank-name') }}:
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> Finansbank </div>    
                            </div>
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.account-name') }}: 
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> THORNE BILISIM A.S. </div>    
                            </div>
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.iban') }}(USD): 
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> TR83 0011 1000 0000 0126 8146 80 </div>
                            </div>
                            <div class="w-1/3">
                                <div class="font-terramirum font-bold text-base tracking-wide pb-2">
                                    {{ __('velocity::app-static.orders.branch-code') }}: 
                                </div>
                                <div class="font-terramirum font-bold text-md tracking-wide text-[#707070] pb-2"> 03663 </div>
                            </div>

                        </div>

                    @endif

                </div>

            </div>
        </div>
    </section>

    {{-- <div class="container">
        <div class="order-success-content row col-12 offset-1">
            <h1 class="row col-12">{{ __('shop::app.checkout.success.thanks') }}</h1>

            <p class="row col-12">
                @if (auth()->guard('customer')->user())
                    {!!
                        __('shop::app.checkout.success.order-id-info', [
                            'order_id' => '<a href="' . route('customer.orders.view', $order->id) . '">' . $order->increment_id . '</a>'
                        ])
                    !!}
                @else
                    {{ __('shop::app.checkout.success.order-id-info', ['order_id' => $order->increment_id]) }}
                @endif
            </p>

            <p class="row col-12">
                {{ __('shop::app.checkout.success.info') }}
            </p>

            {{ view_render_event('bagisto.shop.checkout.continue-shopping.before', ['order' => $order]) }}
                <div class="row col-12 mt15">
                    <span class="mb30 mr10">
                        <a href="{{ route('shop.home.index') }}" class="theme-btn remove-decoration">
                            {{ __('shop::app.checkout.cart.continue-shopping') }}
                        </a>
                    </span>

                    @guest('customer')
                        <span class="">
                            <a href="{{ route('customer.register.index') }}" class="theme-btn remove-decoration">
                                {{ __('shop::app.checkout.cart.continue-registration') }}
                            </a>
                        </span>
                    @endguest
                </div>
            {{ view_render_event('bagisto.shop.checkout.continue-shopping.after', ['order' => $order]) }}
        </div>
    </div> --}}

@endsection
