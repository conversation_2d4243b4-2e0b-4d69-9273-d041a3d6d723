@extends('shop::customers.account.index')

@section('page_title')
    {{ __('shop::app.customer.account.order.view.page-tile', ['order_id' => $order->increment_id]) }}
@endsection

@push('css')
    <style type="text/css">
        .account-content .account-layout .account-head {
            margin-bottom: 0px;
        }
        .sale-summary .dash-icon {
            margin-right: 30px;
            float: right;
        }
    </style>
@endpush

@section('page-detail-wrapper')
    <div class="account-head !mb-8">
        <div class="account-heading ml-5 rounded-14p flex space-x-4 items-center">
            <span class="text-terra-dark-gray font-medium">            {{ __('shop::app.customer.account.order.view.page-tile', ['order_id' => '']) }}</span>
            <span class="text-terra-khaki-green">            {{ $order->increment_id }}</span>
            <svg id="Group_6762" data-name="Group 6762" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="17.617" height="21.997" viewBox="0 0 17.617 21.997">
                <g id="Group_6761" data-name="Group 6761">
                    <path id="Path_3360" data-name="Path 3360" d="M1.505,86.671A2.018,2.018,0,0,1,.44,86.078,1.879,1.879,0,0,1,0,84.79q0-4.832,0-9.663c0-1.6,0-3.193,0-4.789a1.725,1.725,0,0,1,1.346-1.76,2.208,2.208,0,0,1,.532-.052q4.972,0,9.944,0a1.733,1.733,0,0,1,1.814,1.3,2.188,2.188,0,0,1,.065.573c0,4.8,0,9.592.007,14.388a1.788,1.788,0,0,1-1.474,1.857c-.012,0-.022.016-.034.024Zm10.982-9.058q0-3.576,0-7.152c0-.548-.172-.72-.717-.72H1.954c-.574,0-.744.168-.744.737q0,7.12,0,14.239a1.7,1.7,0,0,0,.029.319.461.461,0,0,0,.4.391,1.681,1.681,0,0,0,.3.025q4.908,0,9.816,0a1.711,1.711,0,0,0,.3-.024.471.471,0,0,0,.409-.408,1.915,1.915,0,0,0,.024-.32q0-3.544,0-7.087" transform="translate(0 -64.673)" fill="#f5993d"/>
                    <path id="Path_3361" data-name="Path 3361" d="M83.409,9.089q0,3.608,0,7.216a1.691,1.691,0,0,1-.98,1.652,2.23,2.23,0,0,1-.783.178.557.557,0,0,1-.575-.525.592.592,0,0,1,.42-.644.924.924,0,0,1,.125-.029c.442-.072.58-.227.58-.669q0-7.194,0-14.389c0-.462-.2-.664-.671-.664q-4.972,0-9.945,0a.566.566,0,0,0-.665.564.6.6,0,0,1-.676.56.61.61,0,0,1-.524-.73,1.72,1.72,0,0,1,1.754-1.6q5.091-.012,10.181,0A1.73,1.73,0,0,1,83.408,1.83q0,3.629,0,7.259" transform="translate(-65.792 0)" fill="#f5993d"/>
                </g>
            </svg>


        </div>

        @if ($order->canCancel())
            <span class="account-action">
                <form id="cancelOrderForm" action="{{ route('customer.orders.cancel', $order->id) }}" method="post">
                    @csrf
                </form>
                <div class="bg-terra-soft-khaki-green px-4 py-2 rounded-xl hover:opacity-90 mt-3" onclick="cancelOrder('{{ __('shop::app.customer.account.order.view.cancel-confirm-msg') }}')" style="box-shadow: 0 1px 2px rgb(50 123 5 /67%)">
                    <a href="javascript:void(0);" class="unset">
                        <div class="cursor-pointer font-terramirum text-white font-bold text-md text-truncate text-center">
                            {{ __('shop::app.customer.account.order.view.cancel-btn-title') }}
                        </div>
                    </a>
                </div>

                {{-- <a href="javascript:void(0);" class="theme-btn light unset float-right" onclick="cancelOrder('{{ __('shop::app.customer.account.order.view.cancel-confirm-msg') }}')" style="float: right">
                    {{ __('shop::app.customer.account.order.view.cancel-btn-title') }}
                </a> --}}
            </span>
        @endif
    </div>

    {!! view_render_event('bagisto.shop.customers.account.orders.view.before', ['order' => $order]) !!}

    <div class="sale-container mt10 ml-5 p-5 rounded-14p drop-shadow-couponshadow bg-white">
        <tabs>
            <tab name="{{ __('shop::app.customer.account.order.view.info') }}" :selected="true">

                <div class="sale-section !px-5 bg-[#E9EFEB] mt-5 !border-b-none">
                    <div class="section-content">
                        <div class="row col-12">
                            <label class="mr20 font-bold">
                                {{ __('shop::app.customer.account.order.view.placed-on') }}
                            </label>
                            <div class="flex items-center space-x-2">
                                <svg id="Group_6760" data-name="Group 6760" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="17.016" height="15.878" viewBox="0 0 17.016 15.878">
                                    <g id="Group_6756" data-name="Group 6756">
                                        <path id="Path_3358" data-name="Path 3358" d="M.01,98.33H17.025v.248q0,4.62,0,9.24a1.718,1.718,0,0,1-1.506,1.744,2.7,2.7,0,0,1-.4.031q-6.785,0-13.57,0A1.43,1.43,0,0,1,.013,108.12c0-.07,0-.141,0-.211q0-4.69,0-9.38Zm12.548,1.953c-.217,0-.433,0-.65,0a.41.41,0,0,0-.447.433q-.014.648,0,1.3a.4.4,0,0,0,.414.431q.684.018,1.369,0a.364.364,0,0,0,.386-.407q.008-.666,0-1.332a.4.4,0,0,0-.423-.419c-.217-.005-.433,0-.65,0m-5.336,5.852q0,.307,0,.614c0,.012,0,.023,0,.035a.4.4,0,0,0,.42.423c.445.006.89.005,1.335,0a.363.363,0,0,0,.409-.383q.02-.692,0-1.384a.38.38,0,0,0-.393-.392q-.684-.019-1.37,0a.4.4,0,0,0-.4.423c-.005.222,0,.444,0,.666m6.41.009h0c0-.216,0-.432,0-.649a.41.41,0,0,0-.428-.452q-.658-.014-1.317,0a.4.4,0,0,0-.422.4c-.012.455-.013.912,0,1.367a.4.4,0,0,0,.43.393q.659.007,1.317,0a.38.38,0,0,0,.418-.414c.005-.216,0-.432,0-.649m-6.41-4.786c0,.216,0,.432,0,.649a.4.4,0,0,0,.4.437c.456.014.913.012,1.369,0a.354.354,0,0,0,.39-.384q.02-.683,0-1.367a.4.4,0,0,0-.433-.409q-.65-.006-1.3,0a.4.4,0,0,0-.433.426c-.005.216,0,.432,0,.649m-4.188,4.774c0,.2,0,.409,0,.614a.416.416,0,0,0,.453.462q.615.005,1.23,0a.454.454,0,0,0,.487-.467q.016-.64,0-1.28a.4.4,0,0,0-.428-.417c-.439-.009-.879-.009-1.318,0a.4.4,0,0,0-.423.439c0,.216,0,.433,0,.649m1.093-5.849c-.217,0-.433,0-.65,0a.408.408,0,0,0-.442.436c0,.427,0,.854,0,1.28a.4.4,0,0,0,.415.446q.65.019,1.3,0a.429.429,0,0,0,.449-.428c.017-.449.014-.9,0-1.35a.39.39,0,0,0-.42-.383c-.217,0-.433,0-.65,0" transform="translate(-0.009 -93.715)" fill="#79914e"/>
                                        <path id="Path_3359" data-name="Path 3359" d="M16.965,3.488H0C0,2.87,0,2.262,0,1.654a2.857,2.857,0,0,1,.03-.384A1.409,1.409,0,0,1,1.5,0c.479.016.959,0,1.463,0v.2c0,.445,0,.89,0,1.335a.532.532,0,1,0,1.06,0c0-.5,0-1.006,0-1.523H12.5c0,.059.01.12.01.182,0,.457,0,.913,0,1.37A.531.531,0,0,0,12.9,2.1a.506.506,0,0,0,.572-.205.825.825,0,0,0,.1-.38c.012-.433,0-.866,0-1.3V.039c.024-.018.029-.024.033-.024.626.007,1.257-.029,1.878.034A1.723,1.723,0,0,1,16.948,1.4a1.355,1.355,0,0,1,.016.227c0,.614,0,1.229,0,1.857" transform="translate(0 0)" fill="#79914e"/>
                                    </g>
                                </svg>

                                <span class="value">
                                {{ core()->formatDate($order->created_at, 'd M Y') }}
                            </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="sale-section">
                    <div class="section-title !text-black !text-2xl">
                        <span>{{ __('shop::app.customer.account.order.view.products-ordered') }}</span>
                    </div>

                    <div class="section-content">
                    <div class="table-responsive" style="border-radius: 0px;">
                            <table class="table">
                                <thead class="head-bg-lightgreen">
                                    <tr>
                                        <th class="bg-greens">{{ __('shop::app.customer.account.order.view.SKU') }}</th>
                                        <th class="bg-greens">{{ __('shop::app.customer.account.order.view.product-name') }}</th>
                                        <th class="bg-greens">{{ __('shop::app.customer.account.order.view.price') }}</th>
                                        <th class="bg-greens">{{ __('shop::app.customer.account.order.view.item-status') }}</th>
                                        <th class="bg-greens">{{ __('shop::app.customer.account.order.view.subtotal') }}</th>
                                        <th class="bg-greens">{{ __('shop::app.customer.account.order.view.tax-percent') }}</th>
                                        <th class="bg-greens">{{ __('shop::app.customer.account.order.view.tax-amount') }}</th>
                                        <th class="bg-greens">{{ __('shop::app.customer.account.order.view.grand-total') }}</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    @foreach ($order->items as $item)
                                        <tr>
                                            <td  class="bg-greens" data-value="{{ __('shop::app.customer.account.order.view.SKU') }}">
                                                {{ $item->getTypeInstance()->getOrderedItem($item)->sku }}
                                            </td>

                                            <td  class="bg-greens max-w-224p" data-value="{{ __('shop::app.customer.account.order.view.product-name') }}">
                                                {{ $item->name }}

                                                @if (isset($item->additional['attributes']))
                                                    <div class="item-options">

                                                        @foreach ($item->additional['attributes'] as $attribute)
                                                            <b>{{ $attribute['attribute_name'] }} : </b>{{ $attribute['option_label'] }}</br>
                                                        @endforeach

                                                    </div>
                                                @endif
                                            </td>

                                            <td class="bg-greens" data-value="{{ __('shop::app.customer.account.order.view.price') }}">
                                                {{ core()->formatPrice($item->price, $order->order_currency_code) }}
                                            </td>

                                            <td class="bg-greens" data-value="{{ __('shop::app.customer.account.order.view.item-status') }}">
                                                <span class="qty-row">
                                                    {{ __('shop::app.customer.account.order.view.item-ordered', ['qty_ordered' => $item->qty_ordered]) }}
                                                </span>

                                                <span class="qty-row">
                                                    {{ $item->qty_invoiced ? __('shop::app.customer.account.order.view.item-invoice', ['qty_invoiced' => $item->qty_invoiced]) : '' }}
                                                </span>

                                                <span class="qty-row">
                                                    {{ $item->qty_shipped ? __('shop::app.customer.account.order.view.item-minted', ['qty_shipped' => $item->qty_shipped]) : '' }}
                                                </span>

                                                <span class="qty-row">
                                                    {{ $item->qty_refunded ? __('shop::app.customer.account.order.view.item-refunded', ['qty_refunded' => $item->qty_refunded]) : '' }}
                                                </span>

                                                <span class="qty-row">
                                                    {{ $item->qty_canceled ? __('shop::app.customer.account.order.view.item-canceled', ['qty_canceled' => $item->qty_canceled]) : '' }}
                                                </span>
                                            </td>

                                            <td class="bg-greens" data-value="{{ __('shop::app.customer.account.order.view.subtotal') }}">
                                                {{ core()->formatPrice($item->total, $order->order_currency_code) }}
                                            </td>

                                            <td class="bg-greens" data-value="{{ __('shop::app.customer.account.order.view.tax-percent') }}">
                                                {{ number_format($item->tax_percent, 2) }}%
                                            </td>

                                            <td class="bg-greens" data-value="{{ __('shop::app.customer.account.order.view.tax-amount') }}">
                                                {{ core()->formatPrice($item->tax_amount, $order->order_currency_code) }}
                                            </td>

                                            <td class="bg-greens" data-value="{{ __('shop::app.customer.account.order.view.grand-total') }}">
                                                {{ core()->formatPrice($item->total + $item->tax_amount - $item->discount_amount, $order->order_currency_code) }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>

                            </table>
                        </div>

                        <div class="totals mt-8">
                            <div class="sale-summary">
                                <div class="p-4 bg-[#E9EFEB] rounded-14p">
                                    <table>
                                <tbody>
                                    <tr>
                                        <td>{{ __('shop::app.customer.account.order.view.subtotal') }}
                                            <span class="dash-icon">-</span>
                                        </td>
                                        <td>{{ core()->formatPrice($order->sub_total, $order->order_currency_code) }}</td>
                                    </tr>

                                    {{-- @if ($order->haveStockableItems())
                                        <tr>
                                            <td>{{ __('shop::app.customer.account.order.view.shipping-handling') }}
                                                <span class="dash-icon">-</span>
                                            </td>
                                            <td>{{ core()->formatPrice($order->shipping_amount, $order->order_currency_code) }}</td>
                                        </tr>
                                    @endif --}}

                                    @if ($order->base_discount_amount > 0)
                                        <tr>
                                            <td>{{ __('shop::app.customer.account.order.view.discount') }}
                                                @if ($order->coupon_code)
                                                    ({{ $order->coupon_code }})
                                                @endif
                                                <span class="dash-icon">-</span>
                                            </td>
                                            <td>{{ core()->formatPrice($order->discount_amount, $order->order_currency_code) }}</td>
                                        </tr>
                                    @endif

                                    <tr class="border-bottom">
                                        <td>{{ __('shop::app.customer.account.order.view.tax') }}
                                            <span class="dash-icon">-</span>
                                        </td>
                                        <td>{{ core()->formatPrice($order->tax_amount, $order->order_currency_code) }}</td>
                                    </tr>

                                    <tr class="fw6">
                                        <td>{{ __('shop::app.customer.account.order.view.grand-total') }}
                                            <span class="dash-icon">-</span>
                                        </td>
                                        <td>{{ core()->formatPrice($order->grand_total, $order->order_currency_code) }}</td>
                                    </tr>

                                    <tr class="fw6">
                                        <td>{{ __('shop::app.customer.account.order.view.total-paid') }}
                                            <span class="dash-icon">-</span>
                                        </td>
                                        <td>{{ core()->formatPrice($order->grand_total_invoiced, $order->order_currency_code) }}</td>
                                    </tr>

                                    <tr class="fw6">
                                        <td>{{ __('shop::app.customer.account.order.view.total-refunded') }}
                                            <span class="dash-icon">-</span>
                                        </td>
                                        <td>{{ core()->formatPrice($order->grand_total_refunded, $order->order_currency_code) }}</td>
                                    </tr>

                                    <tr class="fw6">
                                        <td>{{ __('shop::app.customer.account.order.view.total-due') }}
                                            <span class="dash-icon">-</span>
                                        </td>

                                        @if($order->status !== 'canceled')
                                            <td>{{ core()->formatPrice($order->total_due, $order->order_currency_code) }}</td>
                                        @else
                                            <td>{{ core()->formatPrice(0.00, $order->order_currency_code) }}</td>
                                        @endif
                                    </tr>
                                <tbody>
                            </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </tab>

            @if ($order->invoices->count())
                <tab name="{{ __('shop::app.customer.account.order.view.invoices') }}">

                    @foreach ($order->invoices as $invoice)

                        <div class="sale-section">
                            <div class="section-title">
                                <span>{{ __('shop::app.customer.account.order.view.individual-invoice', ['invoice_id' => $invoice->increment_id ?? $invoice->id]) }}</span>

                                <a href="{{ route('customer.orders.print', $invoice->id) }}" class="float-right">
                                    {{ __('shop::app.customer.account.order.view.print') }}
                                </a>
                            </div>

                            <div class="section-content">
                                <div class="table">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>{{ __('shop::app.customer.account.order.view.SKU') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.product-name') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.price') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.qty') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.subtotal') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.tax-amount') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.grand-total') }}</th>
                                            </tr>
                                        </thead>

                                        <tbody>

                                            @foreach ($invoice->items as $item)
                                                <tr>
                                                    <td data-value="{{ __('shop::app.customer.account.order.view.SKU') }}">
                                                        {{ $item->getTypeInstance()->getOrderedItem($item)->sku }}
                                                    </td>

                                                    <td data-value="{{ __('shop::app.customer.account.order.view.product-name') }}">
                                                        {{ $item->name }}
                                                    </td>

                                                    <td data-value="{{ __('shop::app.customer.account.order.view.price') }}">
                                                        {{ core()->formatPrice($item->price, $order->order_currency_code) }}
                                                    </td>

                                                    <td data-value="{{ __('shop::app.customer.account.order.view.qty') }}">
                                                        {{ $item->qty }}
                                                    </td>

                                                    <td data-value="{{ __('shop::app.customer.account.order.view.subtotal') }}">
                                                        {{ core()->formatPrice($item->total, $order->order_currency_code) }}
                                                    </td>

                                                    <td data-value="{{ __('shop::app.customer.account.order.view.tax-amount') }}">
                                                        {{ core()->formatPrice($item->tax_amount, $order->order_currency_code) }}
                                                    </td>

                                                    <td data-value="{{ __('shop::app.customer.account.order.view.grand-total') }}">
                                                        {{ core()->formatPrice($item->total + $item->tax_amount, $order->order_currency_code) }}
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <div class="totals">
                                    <table class="sale-summary">
                                        <tr>
                                            <td>{{ __('shop::app.customer.account.order.view.subtotal') }}
                                                <span class="dash-icon">-</span>
                                            </td>
                                            <td>{{ core()->formatPrice($invoice->sub_total, $order->order_currency_code) }}</td>
                                        </tr>

                                        <tr>
                                            <td>{{ __('shop::app.customer.account.order.view.shipping-handling') }}
                                                <span class="dash-icon">-</span>
                                            </td>
                                            <td>{{ core()->formatPrice($invoice->shipping_amount, $order->order_currency_code) }}</td>
                                        </tr>

                                        @if ($invoice->base_discount_amount > 0)
                                            <tr>
                                                <td>{{ __('shop::app.customer.account.order.view.discount') }}
                                                    <span class="dash-icon">-</span>
                                                </td>
                                                <td>{{ core()->formatPrice($invoice->discount_amount, $order->order_currency_code) }}</td>
                                            </tr>
                                        @endif

                                        <tr>
                                            <td>{{ __('shop::app.customer.account.order.view.tax') }}
                                                <span class="dash-icon">-</span>
                                            </td>
                                            <td>{{ core()->formatPrice($invoice->tax_amount, $order->order_currency_code) }}</td>
                                        </tr>

                                        <tr class="fw6">
                                            <td>{{ __('shop::app.customer.account.order.view.grand-total') }}
                                                <span class="dash-icon">-</span>
                                            </td>
                                            <td>{{ core()->formatPrice($invoice->grand_total, $order->order_currency_code) }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                    @endforeach

                </tab>
            @endif

            @if ($order->shipments->count())
                <tab name="{{ __('shop::app.customer.account.order.view.shipments') }}">

                    @foreach ($order->shipments as $shipment)

                        <div class="sale-section">
                            <div class="section-content">
                                <div class="row col-12">
                                    <label class="mr20">
                                    {{ __('shop::app.customer.account.order.view.tracking-number') }}
                                    </label>

                                    <span class="value">
                                        {{  $shipment->track_number }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="sale-section">
                            <div class="section-title">
                                <span>{{ __('shop::app.customer.account.order.view.individual-shipment', ['shipment_id' => $shipment->id]) }}</span>
                            </div>

                            <div class="section-content">

                                <div class="table">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>{{ __('shop::app.customer.account.order.view.SKU') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.product-name') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.qty') }}</th>
                                            </tr>
                                        </thead>

                                        <tbody>

                                            @foreach ($shipment->items as $item)

                                                <tr>
                                                    <td data-value="{{  __('shop::app.customer.account.order.view.SKU') }}">{{ $item->sku }}</td>
                                                    <td data-value="{{  __('shop::app.customer.account.order.view.product-name') }}">{{ $item->name }}</td>
                                                    <td data-value="{{  __('shop::app.customer.account.order.view.qty') }}">{{ $item->qty }}</td>
                                                </tr>

                                            @endforeach

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                    @endforeach

                </tab>
            @endif

            @if ($order->refunds->count())
                <tab name="{{ __('shop::app.customer.account.order.view.refunds') }}">

                    @foreach ($order->refunds as $refund)

                        <div class="sale-section">
                            <div class="section-title">
                                <span>{{ __('shop::app.customer.account.order.view.individual-refund', ['refund_id' => $refund->id]) }}</span>
                            </div>

                            <div class="section-content">
                                <div class="table">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>{{ __('shop::app.customer.account.order.view.SKU') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.product-name') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.price') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.qty') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.subtotal') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.tax-amount') }}</th>
                                                <th>{{ __('shop::app.customer.account.order.view.grand-total') }}</th>
                                            </tr>
                                        </thead>

                                        <tbody>

                                            @foreach ($refund->items as $item)
                                                <tr>
                                                    <td data-value="{{ __('shop::app.customer.account.order.view.SKU') }}">{{ $item->child ? $item->child->sku : $item->sku }}</td>
                                                    <td data-value="{{ __('shop::app.customer.account.order.view.product-name') }}">{{ $item->name }}</td>
                                                    <td data-value="{{ __('shop::app.customer.account.order.view.price') }}">{{ core()->formatPrice($item->price, $order->order_currency_code) }}</td>
                                                    <td data-value="{{ __('shop::app.customer.account.order.view.qty') }}">{{ $item->qty }}</td>
                                                    <td data-value="{{ __('shop::app.customer.account.order.view.subtotal') }}">{{ core()->formatPrice($item->total, $order->order_currency_code) }}</td>
                                                    <td data-value="{{ __('shop::app.customer.account.order.view.tax-amount') }}">{{ core()->formatPrice($item->tax_amount, $order->order_currency_code) }}</td>
                                                    <td data-value="{{ __('shop::app.customer.account.order.view.grand-total') }}">{{ core()->formatPrice($item->total + $item->tax_amount, $order->order_currency_code) }}</td>
                                                </tr>
                                            @endforeach

                                            @if (! $refund->items->count())
                                                <tr>
                                                    <td class="empty" colspan="7">{{ __('shop::app.common.no-result-found') }}</td>
                                                <tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>

                                <div class="totals">
                                    <table class="sale-summary">
                                        <tr>
                                            <td>{{ __('shop::app.customer.account.order.view.subtotal') }}
                                                <span class="dash-icon">-</span>
                                            </td>
                                            <td>{{ core()->formatPrice($refund->sub_total, $order->order_currency_code) }}</td>
                                        </tr>

                                        @if ($refund->shipping_amount > 0)
                                            <tr>
                                                <td>{{ __('shop::app.customer.account.order.view.shipping-handling') }}
                                                    <span class="dash-icon">-</span>
                                                </td>
                                                <td>{{ core()->formatPrice($refund->shipping_amount, $order->order_currency_code) }}</td>
                                            </tr>
                                        @endif

                                        @if ($refund->discount_amount > 0)
                                            <tr>
                                                <td>{{ __('shop::app.customer.account.order.view.discount') }}
                                                    <span class="dash-icon">-</span>
                                                </td>
                                                <td>{{ core()->formatPrice($order->discount_amount, $order->order_currency_code) }}</td>
                                            </tr>
                                        @endif

                                        @if ($refund->tax_amount > 0)
                                            <tr>
                                                <td>{{ __('shop::app.customer.account.order.view.tax') }}
                                                    <span class="dash-icon">-</span>
                                                </td>
                                                <td>{{ core()->formatPrice($refund->tax_amount, $order->order_currency_code) }}</td>
                                            </tr>
                                        @endif

                                        <tr>
                                            <td>{{ __('shop::app.customer.account.order.view.adjustment-refund') }}
                                                <span class="dash-icon">-</span>
                                            </td>
                                            <td>{{ core()->formatPrice($refund->adjustment_refund, $order->order_currency_code) }}</td>
                                        </tr>

                                        <tr>
                                            <td>{{ __('shop::app.customer.account.order.view.adjustment-fee') }}
                                                <span class="dash-icon">-</span>
                                            </td>
                                            <td>{{ core()->formatPrice($refund->adjustment_fee, $order->order_currency_code) }}</td>
                                        </tr>

                                        <tr class="fw6">
                                            <td>{{ __('shop::app.customer.account.order.view.grand-total') }}
                                                <span class="dash-icon">-</span>
                                            </td>
                                            <td>{{ core()->formatPrice($refund->grand_total, $order->order_currency_code) }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                    @endforeach

                </tab>
            @endif
        </tabs>

        <div class="sale-section">
            <div class="section-content bg-[#E9EFEB]" style="border-bottom: 0">
                <div class="order-box-container ml-5">
                    @if ($order->billing_address)
                        <div class="box">
                            <div class="box-title !text-black">
                                {{ __('shop::app.customer.account.order.view.billing-address') }}
                            </div>
                        </div>
                    @endif
                        <div class="box">
                            <div class="box-title !text-black">
                                {{ __('shop::app.customer.account.order.view.payment-method') }}
                            </div>
                        </div>
                </div>
            </div>
            <div class="section-content ml-5" style="border-bottom: 0">
                <div class="order-box-container">
                    @if ($order->billing_address)
                        <div class="box">
                            <div class="box-content name-color">
                                @include ('admin::sales.address', ['address' => $order->billing_address])

                                {!! view_render_event('bagisto.shop.customers.account.orders.view.billing-address.after', ['order' => $order]) !!}
                            </div>
                        </div>

                    @endif

                    {{-- @if ($order->shipping_address)
                        <div class="box">
                            <div class="box-title">
                                {{ __('shop::app.customer.account.order.view.shipping-address') }}
                            </div>

                            <div class="box-content">
                                @include ('admin::sales.address', ['address' => $order->shipping_address])

                                {!! view_render_event('bagisto.shop.customers.account.orders.view.shipping-address.after', ['order' => $order]) !!}
                            </div>
                        </div>

                        <div class="box">
                            <div class="box-title">
                                {{ __('shop::app.customer.account.order.view.shipping-method') }}
                            </div>

                            <div class="box-content">
                                {{ $order->shipping_title }}

                                {!! view_render_event('bagisto.shop.customers.account.orders.view.shipping-method.after', ['order' => $order]) !!}
                            </div>
                        </div>
                    @endif --}}

                    <div class="box">
                        <div class="box-content">
                            {{ core()->getConfigData('sales.paymentmethods.' . $order->payment->method . '.title') }}

                            @php $additionalDetails = \Webkul\Payment\Payment::getAdditionalDetails($order->payment->method); @endphp

                            @if (! empty($additionalDetails))
                                <div class="instructions">
                                    <label>{{ $additionalDetails['title'] }}</label>
                                    <p>{{ $additionalDetails['value'] }}</p>
                                </div>
                            @endif

                            {!! view_render_event('bagisto.shop.customers.account.orders.view.payment-method.after', ['order' => $order]) !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {!! view_render_event('bagisto.shop.customers.account.orders.view.after', ['order' => $order]) !!}
@endsection

@push('scripts')
    <script>
        function cancelOrder(message) {
            if (! confirm(message)) {
                return;
            }

            $('#cancelOrderForm').submit();
        }
    </script>
@endpush