@extends('shop::customers.account.index')

@section('page_title')
    {{ __('velocity::app-static.user-profile.account-info') }}
@endsection

@push('css')
    <style>
        .account-head {
            height: 50px;
            margin-left: 1.25rem;
        }
        .popup {
            display: none;
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #79914e;
            padding: 10px;
            border: 1px solid #79914e;
            border-radius: 20px;
            box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        .popup-content {
            font-size: 14px;
            color: #fff;
        }
        span.control-error {
            display: block;
        }
    </style>
@endpush

@section('page-detail-wrapper')
    <div class="account-head mb-0">
        <span class="account-heading">
            {{ __('velocity::app-static.user-profile.account-info') }}
        </span>
    </div>
    <div class="relative w-full mt-[40px]">
        <div class="flex flex-col lg:flex-row space-y-3 lg:space-y-0 justify-around w-full relative z-10">
            <button
                class="transition-all duration-300 font-bold text-xl text-center pb-4 tab-button active text-terra-khaki-green border-b-[5px] border-terra-khaki-green"
                onclick="showTab(1)" id="show-tab-1"
            >
                {{ __('velocity::app-static.user-profile.user-info') }}
            </button>
            <button
                class="transition-all duration-300 font-bold text-xl text-center pb-4 tab-button"
                onclick="showTab(2)" id="show-tab-2"
            >
                {{ __('velocity::app-static.user-profile.pass-change') }}
            </button>
            <button
                class="transition-all duration-300 font-bold text-xl text-center pb-4 tab-button"
                onclick="showTab(3)" id="show-tab-3"
            >
                {{ __('velocity::app-static.user-profile.preferences') }}
            </button>
        </div>
        <div class=" mx-auto border-t-2 border-terra-bg-light-gray-2 absolute bottom-[1px] w-full left-0"></div>
    </div>
    {!! view_render_event('bagisto.shop.customers.account.profile.view.before', ['customer' => $customer]) !!}
    <div class="w-full lg:w-7/12 mt-8 lg:ml-[170px]">
        <div class="" id="tab-content">
            <div class="w-full hidden" id="tab-1">
                <div class=" text-xl font-bold relative z-40">
                    {{ __('velocity::app-static.user-profile.profile') }}
                </div>
                <div class="flex justify-end">
                    <form
                        method="POST"
                        @submit.prevent="onSubmit"
                        action="{{ route('customer.profile.update') }}"
                        enctype="multipart/form-data" class="w-full"
                    >
                        @csrf
                        <div class="flex w-full ">
                            {!! view_render_event('bagisto.shop.customers.account.profile.edit_form_controls.before', [
                                'customer' => $customer,
                            ]) !!}
                            <div class="w-full md:w-1/2 pr-3 md:pr-5 mt-6">
                                <div :class="`${errors.has('first_name') ? 'has-error' : ''}`">
                                    <input
                                        value="{{ $customer?->first_name }}"
                                        id="first_name"
                                        name="first_name"
                                        placeholder="Ad" type="text"
                                        class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 rounded-[10px] w-full border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                        v-validate="'required'"
                                        data-vv-as="&quot;{{ __('shop::app.customer.account.profile.fname') }}&quot;"
                                    />
                                    <span
                                        class="control-error"
                                        v-if="errors.has('first_name')"
                                        v-text="errors.first('first_name')"
                                    ></span>
                                </div>
                            </div>
                            {!! view_render_event('bagisto.shop.customers.account.profile.edit.first_name.after', ['customer' => $customer]) !!}
                            <div class="w-full md:w-1/2 mt-6">
                                <div :class="`${errors.has('last_name') ? 'has-error' : ''}`">
                                    <input
                                        value="{{ $customer?->last_name }}"
                                        id="last_name"
                                        name="last_name"
                                        type="text"
                                        class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                        v-validate="'required'"
                                        data-vv-as="&quot;{{ __('shop::app.customer.account.profile.lname') }}&quot;"
                                    />
                                    <span
                                        class="control-error"
                                        v-if="errors.has('last_name')"
                                        v-text="errors.first('last_name')"
                                    ></span>
                                </div>
                            </div>
                        </div>
                        {!! view_render_event('bagisto.shop.customers.account.profile.edit.last_name.after', ['customer' => $customer]) !!}
                        <div class="w-full mt-6">
                            <input
                                value="{{ $customer?->user_name }}"
                                type="text"
                                id="user_name"
                                name="user_name"
                                placeholder="User name"
                                class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                            />
                            <p class="text-xs text-terra-orange pl-3 mt-2">
                                *{{ __('velocity::app-static.user-profile.info') }}
                            </p>
                        </div>
                        <button
                            type="submit"
                            class="bg-terra-orange text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90 mt-6 float-right"
                        >
                            {{ __('velocity::app.shop.general.update') }}
                        </button>
                    </form>
                </div>
                <div class="flex justify-end">
                    <form
                        method="POST"
                        @submit.prevent="onSubmit"
                        action="{{ route('customer.profile.updateProfile.store') }}"
                        enctype="multipart/form-data"
                        class="w-full"
                    >
                        @csrf
                        <div class="row mt-6">
                            <div class="col-12">
                                <input
                                    value="{{ old('phone') ?? $customer?->phone }}"
                                    placeholder="+90 (___) ___ __ __"
                                    class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                    name="phone" id="phone" type="text"
                                />
                                <span
                                    class="control-error"
                                    v-if="errors.has('phone')"
                                    v-text="errors.first('phone')"
                                ></span>
                            </div>
                        </div>
                        {!! view_render_event('bagisto.shop.customers.account.profile.edit.phone.after', ['customer' => $customer]) !!}
                        <div class="row mt-6">
                            <div class="col-12">
                                <input
                                    value="{{ old('email') ?? $customer?->email }}"
                                    id="email"
                                    name="email"
                                    type="text"
                                    class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                    v-validate="'required'"
                                />
                                <span
                                    class="control-error"
                                    v-if="errors.has('email')"
                                    v-text="errors.first('email')"
                                ></span>
                            </div>
                        </div>
                        {!! view_render_event('bagisto.shop.customers.account.profile.edit.email.after', [
                            'customer' => $customer
                        ]) !!}
                        {!! view_render_event('bagisto.shop.customers.account.profile.edit.image.after', [
                            'customer' => $customer
                        ]) !!}
                        {!! view_render_event('bagisto.shop.customers.account.profile.edit.oldpassword.after', [
                            'customer' => $customer,
                        ]) !!}
                        {!! view_render_event('bagisto.shop.customers.account.profile.edit.password.after', [
                            'customer' => $customer
                        ]) !!}
                        @if(core()->getConfigData('customer.settings.newsletter.subscription'))
                            <div class="flex items-center justify-start mt-6">
                                <input
                                    type="checkbox"
                                    id="checkbox2"
                                    name="subscribed_to_news_letter"
                                    @if (isset($customer?->subscription))
                                        value="{{ $customer?->subscription?->is_subscribed }}"
                                        {{ $customer?->subscription?->is_subscribed ? 'checked' : '' }}
                                    @endif
                                    style="width: 20px; height: 20px"
                                />
                                <span class="text-lg font-semibold">{{ __('shop::app.customer.signup-form.subscribe-to-newsletter') }}</span>
                            </div>
                        @endif
                        {!! view_render_event('bagisto.shop.customers.account.profile.edit_form_controls.after', [
                            'customer' => $customer,
                        ]) !!}
                        <button
                            type="submit"
                            class="bg-terra-orange text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90 mt-6 float-right"
                        >
                            {{ __('velocity::app.shop.general.update') }}
                        </button>
                    </form>
                </div>
                
                <div class="container mt-5">
                    <div class="row justify-content-center">
                        <div class="col-md-12 no-padding">
                            <div class="card">
                                <div class="card-header flex items-center justify-center">
                                    <img src="/deneme/google-auth.png" style="max-height: 40px; margin-right:10px;" alt=""/>
                                    <span class=" font-bold text-xl text-center text-terra-khaki-green">
                                        {{ __('Two Factor Authentication') }}
                                    </span>
                                </div>
                                @if(auth()->user()->google2fa_enabled)
                                    <div class="card-body">
                                        <div class="flex items-center justify-center">
                                            <img src="/deneme/tick.png" style="max-height: 40px; margin-right:10px;" alt="Okey"/>
                                            <p class="text-lg font-bold relative text-center text-terra-khaki-green my-10">
                                                {{ __('Google Two Factor Authentication is Verified') }}
                                            </p>
                                        </div>
                                    </div>
                                @else
                                    <div class="card-body">
                                        <form method="POST" action="{{ route('2fa.activate') }}">
                                            @csrf
                                            <input type="text" class="hidden" name="key" value="{{ $key }}"/>
                                            <div class="form-group row w-full mx-auto">
                                                <div class="col-md-6 flex justify-center">
                                                    <div class="flex justify-center items mx-auto p-1 border-1p border-terra-khaki-green rounded">
                                                        {!! Google2FA::getQRCodeInline( 'Terramirum', auth()->user()->email, $key ); !!}
                                                    </div>
                                                </div>
                                                <div class="col-md-6 flex justify-center flex-col">
                                                    <label for="one_time_password" class="text-lg font-bold relative text-center">
                                                        {{ __('One Time Password') }}
                                                    </label>
                                                    <input
                                                        id="one_time_password"
                                                        type="number"
                                                        maxlength="6"
                                                        minlength="6"
                                                        class="tracking-widest text-xl font-bold text-center mt-5 bg-terra-searchbar-green rounded-xl px-4 py-2 w-full focus:outline-none focus:shadow-outline @error('one_time_password') border-1p border-terra-red @enderror"
                                                        name="one_time_password"
                                                        required
                                                    />
                                                    @foreach($errors->all() as $message)
                                                        <div class=" text-center invalid-feedback @error('one_time_password'){{'block'}}@enderror" role="alert">
                                                            <strong>{{ $message }}</strong>
                                                        </div>
                                                    @endforeach
                                                    <div class="flex justify-center mt-5">
                                                        <button
                                                            type="submit"
                                                            class="bg-terra-orange text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90"
                                                        >
                                                            {{ __('Verify') }}
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <profile-tab2-component></profile-tab2-component>
            <script type="text/x-template" id="profile-tab2-template">
                <div id="tab-2" class="hidden">
                    <div>
                        <div v-if="passwordStatus" :class="alertClass" role="alert">
                            <p class="font-bold">{{ __('velocity::wallet-panel.profile-password-tabs-system-information') }}</p>
                            <p>@{{ passwordText }}</p>
                        </div>
                    </div>
                    <div class=" text-xl font-bold relative z-40">
                        {{ __('velocity::app-static.user-profile.pass-change') }}
                    </div>
                    <div class="w-full text-left text-terra-dark-gray font-terramirum text-base mb-4 mt-3 flex items-start">
                        <img class="mr-2" src="/../deneme/svg/info.svg" alt=""/>
                        <div>
                            {{ __('velocity::app-static.user-profile.pass-change-msg') }}
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <form
                            method="POST"
                            @submit.prevent="onSubmitPassword"
                            enctype="multipart/form-data"
                            class="w-full"
                        >
                            @csrf
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="mandatory">
                                        {{ __('velocity::app-static.user-profile.current-pass') }}
                                    </div>
                                    <input
                                        value=""
                                        id="password_old"
                                        name="oldpassword"
                                        type="password"
                                        class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                    />
                                    <a
                                        class="absolute mt-3 right-5"
                                        onclick="passToggle('password_old', 'pass_old')"
                                        id="shoPassword"
                                        :class="show-password"
                                    >
                                        <img id="pass_old" src="/../deneme/svg/eyes.svg" alt=""/>
                                    </a>
                                </div>
                            </div>
                            <div class="row mt-6">
                                <div class="col-12">
                                    <div class="mandatory">
                                        {{ __('velocity::app-static.user-profile.new-pass') }}
                                    </div>
                                    <input
                                        value=""
                                        id="password_new"
                                        ref="password"
                                        type="password"
                                        name="password"
                                        autocomplete="off"
                                        v-validate="'required|min:8'"
                                        class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                    />
                                    <a
                                        class="absolute mt-3 right-5"
                                        onclick="passToggle('password_new', 'pass_new')"
                                        id="shoPassword"
                                        :class="show-password"
                                    >
                                        <img id="pass_new" src="/../deneme/svg/eyes.svg" alt=""/>
                                    </a>
                                    <span
                                        class="control-error"
                                        v-if="errors.has('password')"
                                        v-text="errors.first('password')"
                                    ></span>
                                </div>
                            </div>
                            <div class="row mt-6">
                                <div class="col-12">
                                    <div class="mandatory">
                                        {{ __('velocity::app-static.user-profile.confirm-pass') }}
                                    </div>
                                    <input
                                        id="password_conf"
                                        value=""
                                        name="password_confirmation"
                                        type="password"
                                        v-validate="'min:6|confirmed:password'"
                                        data-vv-as="confirm password"
                                        class="text-base placeholder:text-terra-dark-gray placeholder:text-base px-4 py-3 w-full rounded-[10px] border-0 bg-[#E8E8E8] focus:ring-terra-khaki-green"
                                    />
                                    <a
                                        class="absolute mt-3 right-5"
                                        onclick="passToggle('password_conf', 'pass_conf')"
                                        id="shoPassword"
                                        :class="show-password"
                                    >
                                        <img id="pass_conf" src="/../deneme/svg/eyes.svg" alt=""/>
                                    </a>
                                    <span
                                        class="control-error"
                                        v-if="errors.has('password_confirmation')"
                                        v-text="errors.first('password_confirmation')"
                                    ></span>
                                </div>
                            </div>
                            <button
                                type="submit"
                                class="bg-terra-orange text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90 mt-6 float-right"
                            >
                                {{ __('velocity::app.shop.general.update') }}
                            </button>
                        </form>
                    </div>
                </div>
            </script>
            <profile-tab3-component></profile-tab3-component>
            <script type="text/x-template" id="profile-tab3-template">
                <div id="tab-3" class="hidden">
                    <div class=" text-xl font-bold relative z-40">
                        {{ __('velocity::app-static.user-profile.preferences') }}
                    </div>
                    <div class="w-full text-left text-terra-dark-gray font-terramirum text-base mb-4 mt-3 flex items-start">
                        <div>
                            @if(env('APP_LOCALE') == "tr")
                                <span class="text-terra-khaki-green">Rıza Metni</span>
                                kapsamında önemli kampanyalardan haberdar olmak için tercih
                                ettiğiniz yöntemleri belirtebilirsiniz.
                            @endif
                            @if(env('APP_LOCALE') == "en")
                                {{ __('velocity::app-static.user-profile.info-part') }}
                                <span class="text-terra-khaki-green">
                                    {{ __('velocity::app-static.user-profile.consent-text') }}
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="border-1 border-borderflat-gray2 px-10 py-8 rounded-md mt-8 max-w-full mx-auto" style="border:1px solid rgb(207, 207, 207);">
                        <div class="font-terramirum font-semibold text-lg text-left text-terra-dark-gray">
                            {{ __('velocity::app-static.user-profile.email') }}
                        </div>
                        <div class="flex items-center justify-content-between w-full">
                            <div class="font-terramirum font-semibold text-m text-left text-black">
                                {{ __('velocity::app-static.user-profile.info-email') }}
                            </div>
                            <label class="relative inline-flex items-center ml-6 cursor-pointer">
                                <input
                                    type="checkbox"
                                    class="sr-only peer"
                                    @click="functionEmailSend"
                                    :checked="emailSend"
                                />
                                <div class="w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-focus:ring-4 peer-focus:terra-soft-khaki-green dark:peer-focus:ring-green-800 peer-checked:after:translate-x-full peer-checked:after:border-terra-soft-khaki-green after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-terra-soft-khaki-green after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-[#D0E3BD]"></div>
                            </label>
                        </div>
                        <div class="font-terramirum font-semibold text-lg text-left text-terra-dark-gray mt-5">
                            {{ __('velocity::app-static.user-profile.sms') }}
                        </div>
                        <div class="flex items-center justify-content-between w-full">
                            <div class="font-terramirum font-semibold text-m text-left text-black">
                                {{ __('velocity::app-static.user-profile.info-sms') }}
                            </div>
                            <label class="relative inline-flex items-center ml-6 cursor-pointer">
                                <input
                                    type="checkbox"
                                    @click="functionSmsSend"
                                    class="sr-only peer"
                                    :checked="smsSend"
                                />
                                <div class="w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-focus:ring-4 peer-focus:terra-soft-khaki-green dark:peer-focus:ring-green-800 peer-checked:after:translate-x-full peer-checked:after:border-terra-soft-khaki-green after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-terra-soft-khaki-green after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-[#D0E3BD]"></div>
                            </label>
                        </div>
                        <div class="font-terramirum font-semibold text-lg text-left text-terra-dark-gray mt-5">
                            {{ __('velocity::app-static.user-profile.call') }}
                        </div>
                        <div class="flex items-center justify-content-between w-full">
                            <div class="font-terramirum font-semibold text-m text-left text-black">
                                {{ __('velocity::app-static.user-profile.info-tel') }}
                            </div>
                            <label class="relative inline-flex items-center ml-6 cursor-pointer">
                                <input
                                    type="checkbox"
                                    @click="functionPhoneCall"
                                    class="sr-only peer"
                                    :checked="phoneCall"
                                    />
                                <div class="w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-focus:ring-4 peer-focus:terra-soft-khaki-green dark:peer-focus:ring-green-800 peer-checked:after:translate-x-full peer-checked:after:border-terra-soft-khaki-green after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-terra-soft-khaki-green after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-[#D0E3BD]"></div>
                            </label>
                        </div>
                        <p class="text-xs text-terra-orange pl-3 mt-5">
                            *{{ __('velocity::app-static.user-profile.info-text') }}
                        </p>
                    </div>
                </div>
            </script>
        </div>
    </div>
    {!! view_render_event('bagisto.shop.customers.account.profile.view.after', ['customer' => $customer]) !!}
@endsection

@push('scripts')
    <script type="text/x-template" id="sumsub_verification_template">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-12 no-padding">
                    <div class="card">
                        <div class="card-header flex items-center justify-center">
                            <span class="font-bold text-xl text-center text-terra-khaki-green">
                                {{ __('sumsub-kyc::app.ui.pages.verification.title') }}
                            </span>
                        </div>
                        <div class="card-body">
                            <div v-if="isLoading" class="text-center p-5">
                                <div class="spinner-border text-terra-khaki-green" role="status"></div>
                            </div>
                            <div v-else>
                                <div class="flex flex-col" v-if="!error">
                                    <div class="flex flex-col md:flex-row gap-6 justify-center items-stretch">
                                        <div class="flex-auto flex flex-col p-4 min-h-[220px]">
                                            <div v-if="qrCodeImage">
                                                <img :src="qrCodeImage" alt="Verification QR Code" class="img-fluid" style="max-width: 180px; border: 1px solid #ddd; padding: 5px;">
                                            </div>
                                        </div>
                                        <div class="flex-1 flex flex-col items-center justify-center p-4 min-h-[220px]">
                                            <div class="w-full">
                                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                                    {{ __('sumsub-kyc::app.ui.pages.verification.verification_url') }}
                                                </label>
                                                <div class="flex relative">
                                                    <input
                                                        v-if="verificationUrl"
                                                        :value="verificationUrl"
                                                        id="verification-url"
                                                        type="text"
                                                        readonly
                                                        class="mb-3 pe-0 w-100 font-bold bg-terra-searchbar-green rounded-xl px-4 py-4 w-full focus:outline-none focus:shadow-outline"
                                                        style="min-width:0"
                                                        @focus="$event.target.select()"/>
                                                    
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="position: absolute; right: 16px; top: 0; transform: translateY(50%); cursor: pointer;" width="24" height="24" onclick="copyToClipboard()">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75" />
                                                    </svg>
                                                </div>
                                                <button
                                                    v-if="canStartVerification"
                                                    :href="verificationUrl"
                                                    @click="openVerification"
                                                    class="bg-terra-orange w-100 text-lg text-white text-center font-bold rounded-xl py-4 px-8 transition-all hover:opacity-90 mt-3 text-decoration-none"
                                                    type="button">
                                                    {{ __('sumsub-kyc::app.ui.pages.verification.start_verification_link') }}
                                                </button>
                                                <div v-else class="text-gray-500 mt-4">
                                                    {{ __('sumsub-kyc::app.ui.pages.verification.verification_in_progress') }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex-1 flex flex-col items-center justify-center p-4">
                                        <div v-if="error" class="w-full border border-danger rounded-xl p-4 text-center">
                                            <span class="text-danger font-bold text-xl">{{ __('sumsub-kyc::app.ui.pages.verification.error_title') }}</span>
                                            <p class="text-danger text-lg">@{{ error }}</p>
                                            <button @click="fetchStatus" class="btn btn-danger w-100 text-lg text-white text-center font-bold rounded-xl py-4 px-8 transition-all hover:opacity-90 mt-3 text-decoration-none">
                                                {{ __('sumsub-kyc::app.ui.pages.verification.retry_fetch') }}
                                            </button>
                                        </div>
                                        <!-- DURUM MESAJLARI -->
                                        <div v-if="isVerified" class="w-full border border-terra-khaki-green rounded-xl p-4 text-center">
                                            <span class="text-terra-khaki-green font-bold text-xl">{{ __('sumsub-kyc::app.ui.pages.verification.status_verified_title') }}</span>
                                            <div class="text-terra-khaki-green text-lg">@{{ verificationData.status.result.message || __('sumsub-kyc::app.ui.pages.verification.status_verified_message') }}</div>
                                        </div>
                                        <div v-else-if="isPending" class="w-full border border-terra-khaki-green rounded-xl p-4 text-center">
                                            <span class="text-terra-khaki-green font-bold text-xl">{{ __('sumsub-kyc::app.ui.pages.verification.status_pending_title') }}</span>
                                            <div class="text-terra-khaki-green text-lg">@{{ __('sumsub-kyc::app.ui.pages.verification.status_pending_message') }}</div>
                                        </div>
                                        <div v-else-if="isRejected" class="w-full border border-danger rounded-xl p-4 text-center">
                                            <span class="text-danger font-bold text-xl">{{ __('sumsub-kyc::app.ui.pages.verification.status_rejected_title') }}</span>
                                            <div class="text-danger text-lg">@{{ verificationData.status.result.message || __('sumsub-kyc::app.ui.pages.verification.status_rejected_message') }}</div>
                                        </div>
                                        <div v-else-if="isFinalRejected" class="w-full border border-danger rounded-xl p-4 text-center">
                                            <span class="text-danger font-bold text-xl">{{ __('sumsub-kyc::app.ui.pages.verification.status_final_rejected_title') }}</span>
                                            <div class="text-danger text-lg">@{{ verificationData.status.result.message || __('sumsub-kyc::app.ui.pages.verification.status_final_rejected_message') }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <script>
        Vue.component('sumsub_verification', {
            template: '#sumsub_verification_template',
            data() {
                return {
                    isLoading: true,
                    error: null,
                    customerData: null,
                    verificationData: null,
                    apiEndpoint: '/sumsub/status',
                };
            },
            computed: {
                currentResultStatus() {
                    return this.verificationData?.status?.result?.result;
                },
                isVerified() {
                    return this.currentResultStatus === '{{ \Thorne\SumsubKyc\Http\Controllers\SumsubController::REVIEW_RESULTS['VERIFIED'] }}';
                },
                isPending() {
                    // This covers 'init', 'pending', 'prechecked', 'queued', 'onHold', and 'completed' with 'GRAY' answer
                    // as per controller's handleReviewResult logic
                    return this.currentResultStatus === '{{ \Thorne\SumsubKyc\Http\Controllers\SumsubController::REVIEW_RESULTS['PENDING'] }}';
                },
                isRejected() {
                    return this.currentResultStatus === '{{ \Thorne\SumsubKyc\Http\Controllers\SumsubController::REVIEW_RESULTS['REJECTED'] }}';
                },
                isFinalRejected() {
                    return this.currentResultStatus === '{{ \Thorne\SumsubKyc\Http\Controllers\SumsubController::REVIEW_RESULTS['FINAL_REJECTED'] }}';
                },
                verificationUrl() {
                    return this.verificationData?.url?.data?.url;
                },
                qrCodeImage() {
                    return this.verificationData?.qrCode?.data?.qrCode;
                },
                canStartVerification() {
                    // User can start/retry if URL is available and status is not verified or final rejected
                    return !!this.verificationUrl && (this.isPending || this.isRejected);
                }
            },
            methods: {
                async fetchStatus() {
                    this.isLoading = true;
                    this.error = null;
                    try {
                        const response = await fetch(this.apiEndpoint);
                        if (!response.ok) {
                            if (response.status === 401) {
                                throw new Error("{{ __('sumsub-kyc::app.ui.pages.verification.error_unauthorized') }}");
                            }
                            const errorData = await response.json().catch(() => ({ message: response.statusText }));
                            throw new Error(errorData.message || `{{ __('sumsub-kyc::app.ui.pages.verification.error_fetching') }} ${response.status}`);
                        }
                        const responseData = await response.json();
                        if (responseData.status === 200 && responseData.data) {
                            this.customerData = responseData.data.customer;
                            this.verificationData = responseData.data.verification;

                            // Handle cases where URL/QR might be null from API even if status code is 200
                            if (this.verificationData.url && this.verificationData.url.status !== 200) {
                                this.verificationData.url = null; // Clear it if not successful
                            }
                            if (this.verificationData.qrCode && this.verificationData.qrCode.status !== 200) {
                                this.verificationData.qrCode = null; // Clear it if not successful
                            }

                        } else {
                            throw new Error(responseData.message || "{{ __('sumsub-kyc::app.ui.pages.verification.error_invalid_response') }}");
                        }
                    } catch (err) {
                        console.error("Sumsub status fetch error:", err);
                        this.error = err.message;
                        this.verificationData = null; // Clear data on error
                        this.customerData = null;
                    } finally {
                        this.isLoading = false;
                    }
                },
                formatDate(dateString) {
                    if (!dateString) return '';
                    try {
                        const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
                        return new Date(dateString).toLocaleDateString(undefined, options);
                    } catch (e) {
                        return dateString; // fallback to original string if parsing fails
                    }
                },
                copyToClipboard() {
                    const el = document.getElementById('verification-url');
                    if (el) {
                        el.select();
                        el.setSelectionRange(0, 99999);
                        document.execCommand('copy');
                    }
                },
                openVerification() {
                    if (this.verificationUrl) {
                        window.open(this.verificationUrl, '_blank');
                    }
                },
            },
            mounted() {
                this.fetchStatus();
            }
        });
    </script>
@endpush

@push('scripts')
    <script>
        /**
         * Show delete profile modal.
         */
        function showDeleteProfileModal() {
            document.getElementById('deleteProfileForm').classList.remove('d-none');
            window.app.showModal('deleteProfile');
        }
        function passToggle(inputId, iconId) {
            var input = document.getElementById(inputId);
            var icon = document.getElementById(iconId);
            if (input.type === "password") {
                input.type = "text";
                icon.src = "/../deneme/svg/eyes-open.svg";
            } else {
                input.type = "password";
                icon.src = "/../deneme/svg/eyes.svg";
            }
        }
        function copyToClipboard() {
            let copyText = document.getElementById('verification-url');
            copyText.select();
            copyText.setSelectionRange(0, 99999);
            document.execCommand('copy');
        }
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            showTab(1);
        });
        function showTab(tabIndex) {
            document.querySelectorAll('#tab-content > div').forEach(tabContent => {
                tabContent.classList.add('hidden');
            });
            const selectedTabContent = document.querySelector(`#tab-${tabIndex}`);
            if (selectedTabContent) {
                selectedTabContent.classList.remove('hidden');
            }
            document.querySelectorAll('.tab-button').forEach(tabButton => {
                tabButton.classList.remove('active', 'text-terra-khaki-green', 'border-b-[5px]', 'border-terra-khaki-green');
            });
            const selectedTabButton = document.getElementById(`show-tab-${tabIndex}`);
            if (selectedTabButton) {
                selectedTabButton.classList.add('active','text-terra-khaki-green', 'border-b-[5px]', 'border-terra-khaki-green');
            }
        }
        function copyValue() {
            var input = document.getElementById("wallet");
            var value = input.value.split(":")[1].trim();
            var tempInput = document.createElement("input");
            tempInput.value = value;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand("copy");
            document.body.removeChild(tempInput);
            var popup = document.getElementById("myPopup");
            var popupMessage = document.getElementById("popupMessage");
            popupMessage.textContent = "Wallet Address copied: " + value;
            popup.style.display = "block";
            setTimeout(function() {
                popup.style.display = "none";
            }, 3000);
        }

        /** **/
        Vue.component('profile-tab2-component', {
            template: '#profile-tab2-template',
            data: function() {
                return {
                    password: '',
                    oldpassword: '',
                    password_confirmation: '',
                    passwordText: '',
                    passwordStatus: false,
                    alertClass: '',
                }
            },
            mounted: function() {},
            created: function() {},
            methods: {
                toggleButtonDisability({ event, actionType }) {
                    let button = event.target.querySelector('button[type=submit]');
                    button ? (button.disabled = actionType) : '';
                },
                onSubmitPassword: function(event) {
                    this.toggleButtonDisability({ event, actionType: true });
                    this.$validator.validateAll().then(result => {
                        if(result) {
                            this.$root.showLoader();
                            event.preventDefault();
                            axios.post('{{ route('customer.profile.updatePassword.store') }}', {
                                oldpassword: event?.target?.oldpassword?.value,
                                password: event?.target?.password?.value,
                                password_confirmation: event?.target?.password_confirmation?.value,
                            }).then(response => {
                                this.passwordStatus = true;
                                if(response?.data?.warning === true) {
                                    this.alertClass = 'bg-orange-100 border-l-4 border-orange-500 text-orange-700 p-4';
                                    this.passwordText = response?.data?.message;
                                    this.toggleButtonDisability({
                                        event,
                                        actionType: false
                                    });
                                } else if(response?.data?.success === true) {
                                    this.alertClass = 'bg-terra-other-green-100 border-l-4 fund-bordergreen-500 text-terra-flat-gray-700 p-4';
                                    this.passwordText = response?.data?.message;
                                } else if(response?.data?.success === false) {
                                    this.alertClass = 'bg-red-100 border-l-4 border-red-500 text-red-700 p-4';
                                    this.passwordText = response?.data?.message;
                                    this.toggleButtonDisability({
                                        event,
                                        actionType: false
                                    });
                                }
                                this.$root.hideLoader();
                            }).catch(error => {

                            });
                        } else {
                            this.toggleButtonDisability({
                                event,
                                actionType: false
                            });
                            eventBus.$emit('onFormError');
                        }
                    });
                }
            }
        });

        /** **/
        Vue.component('profile-tab3-component', {
            template: '#profile-tab3-template',
            data: function() {
                return {
                    smsSend: false,
                    emailSend: false,
                    phoneCall: false,
                }
            },
            mounted: function() {},
            created: function() {
                this.functionComminicationPreferences();
            },
            methods: {
                functionComminicationPreferences: function() {
                    this.$root.showLoader();
                    axios.get('{{ route('customer.preferences.index') }}', {
                    }).then(response => {
                        if(response?.data !== null) {
                            this.emailSend = response?.data?.data?.emailSend ;
                            this.phoneCall = response?.data?.data?.phoneCall;
                            this.smsSend   = response?.data?.data?.smsSend;
                        }
                        this.$root.hideLoader();
                    })
                        .catch(error => {

                        });
                },
                functionEmailSend: function() {
                    this.$root.showLoader();
                    this.emailSend = !this.emailSend;
                    axios.post('{{ route('customer.preferences.customerPreferences') }}', {
                        emailSend: this.emailSend,
                        phoneCall: this.phoneCall,
                        smsSend: this.smsSend,
                    }).then(response => {
                        this.$root.hideLoader();
                    })
                      .catch(error => {

                      });
                },
                functionSmsSend: function() {
                    this.$root.showLoader();
                    this.smsSend = !this.smsSend;
                    axios.post('{{ route('customer.preferences.customerPreferences') }}', {
                        emailSend: this.emailSend,
                        phoneCall: this.phoneCall,
                        smsSend: this.smsSend,
                    }).then(response => {
                        this.$root.hideLoader();
                    })
                        .catch(error => {

                        });
                },
                functionPhoneCall: function() {
                    this.$root.showLoader();
                    this.phoneCall = !this.phoneCall;
                    axios.post('{{ route('customer.preferences.customerPreferences') }}', {
                        emailSend: this.emailSend,
                        phoneCall: this.phoneCall,
                        smsSend: this.smsSend,
                    }).then(response => {
                        this.$root.hideLoader();
                    })
                        .catch(error => {

                        });
                },
            }
        });
    </script>
@endpush
