@inject('toolbarHelper', 'Webkul\Product\Helpers\Toolbar')

@extends('shop::customers.account.index')

@section('page_title')
    {{ __('velocity::app-static.user-profile.advantage') }}
@endsection

@section('page-detail-wrapper')

@push('css')

<style>

    .account-head {
        height: 50px;
        margin-left: 1.25rem;
    }

    .popup {
            display: none;
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #79914e;
            padding: 10px;
            border: 1px solid #79914e;
            border-radius: 20px;
            box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .popup-content {
            font-size: 14px;
            color: #fff;
        }
    .disabled-link {
        pointer-events: none;
        text-decoration: none;
        cursor: not-allowed;
    }
</style>

@endpush

    <div class="wishlist-container ml-5">
        <div class=" text-2xl font-bold relative z-40"><div>
            <div class="account-head">
                <span class="account-heading">
                    {{__('velocity::app-static.advantages.title')}}
                </span>
            </div>
        <div class=" text-xl font-bold relative z-40 mt-8 ml-3">{{__('velocity::app-static.advantages.coupons')}}</div>
        @if (($CatalogRuleItems->count()) > 0 || ($CartRuleItems->count())>0 )
            @if (($CartRuleItems->count()))
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6 ml-3">
            @foreach ($CartRuleItems as $item)
                @php
                    $current_date = new \DateTime();
                    $expiry_date = new \DateTime($item->ends_till);
                    $interval = $current_date->diff($expiry_date);
                    $remaining_days = $interval->days;
                    $bg_color = '[#F5993D]';
                    // $disabled = '';
                    $expired = true;
                @endphp
                @if ($remaining_days > 0 && $current_date < $expiry_date)
                    @php
                        $expired = false;
                        // $disabled = 'disabled-link';
                        // $bg_color = '[#E8E8E8]';
                    @endphp

                <div class="group">
                    <div class="bg-white border-terra-orange border-1p rounded-10p px-4 py-2 drop-shadow-couponshadow group-hover:drop-shadow-orangeshadow transition-all ease-in-out duration-300">
                        <div class="flex justify-between items-center">
                            <div class="text-base font-bold">{{$item->name}} </div>
                            <div class="flex">

                                    @if ($remaining_days <= 7 && $remaining_days > 0 && $expired == false)
                                        <img src="/deneme/svg/attention-orange.svg" alt="" />
                                        <div class=" ml-1 text-base text-terra-orange font-bold">{{__('velocity::app-static.advantages.last')}} {{$remaining_days}} {{__('velocity::app-static.advantages.days')}}</div>
                                    @endif


                            </div>
                        </div>
                        <div class="flex flex-wrap justify-between mt-3">
                            <div class="flex space-x-2 w-1/3">
                                <img class="rounded-10p max-h-[85px]" src="/deneme/coupon-1-2.png" alt="" />
                                <img class="rounded-10p max-h-[85px]" src="/deneme/coupon-1-1.png" alt="" />
                            </div>
                            <div class="flex w-1/3 flex-col justify-between h-full items-stretch">
                                <div class="text-2xl text-center"> {{(int)$item->discount_amount}} {{$item->action_type === "by_percent" ? "%" : "$"}}</div>

                                <a onclick="copyCouponCode({{$item->id}})" class="bg-{{$bg_color}} text-white text-xl font-bold rounded-[14px] py-2 px-8 transition-all hover:opacity-90">{{__('velocity::app-static.advantages.use')}}</a>
                            </div>
                        </div>
                       <div class="flex justify-between items-center mt-3 pb-2 border-b-1p border-terra-orange">
                           @if($item->condition_type == 2)
                           <div class="text-xs text-terra-dark-gray">{{$item->conditions[0]['operator'] ==='>' ? 'Min.' : 'Max.' }} {{__('velocity::app-static.advantages.purchase-amount')}}: {{$item->conditions[0]['value']}} {{$item->conditions[0]['attribute_type'] === 'price' ? '$' : '%'}}</div>
                           @endif
                           <div class="text-sm text-terra-dark-gray float-right" id="use-{{$item->id}}" data-value="{{$item->getCouponCodeAttribute()}}">{{$item->getCouponCodeAttribute()}}</div>
                       </div>
                        <div class="flex justify-between mt-3">
                            <div class="flex space-x-2">
                                <div onclick="toggleProps({{$item->id}})" class="font-semibold text-sm text-terra-dark-gray hover:text-terra-khaki-green cursor-default">{{__('velocity::app-static.advantages.conditions')}}</div>
                                <img src="/deneme/svg/arrow-bottom.svg" alt="" />
                            </div>

                            @php
                                $item->ends_till = new \DateTime($item->ends_till);
                            @endphp

                            <div class="text-xs text-terra-dark-gray">{{__('velocity::app-static.advantages.expire-date')}}: {{ $item->ends_till->format('d.m.Y')}}</div>
                        </div>
                        <div id="toggleProp-{{$item->id}}" class="transition-all ease-in-out delay-100 duration-500 text-xs text-terra-dark-gray mt-3 h-0 overflow-hidden">{{$item->description}}</div>
                    </div>

                    <div id="myPopup" class="popup">
                        <span class="popup-content" id="popupMessage"></span>
                    </div>

                </div>
                @endif

                   @section('couponscript')
                        <script>
                            function toggleProps(id) {
                                var divElement = document.getElementById('toggleProp-' + id);
                                divElement.classList.toggle('h-0');
                            }
                        </script>
                        <script>
                            function copyCouponCode(itemId) {
                                var div = document.getElementById('use-' + itemId);
                                var value = div.getAttribute('data-value');

                                var tempInput = document.createElement('input');
                                tempInput.value = value;
                                document.body.appendChild(tempInput);
                                tempInput.select();

                                document.execCommand('copy');
                                document.body.removeChild(tempInput);

                                var popup = document.getElementById('myPopup');
                                var popupMessage = document.getElementById('popupMessage');
                                popupMessage.textContent = 'Coupon code copied: ' + value;
                                popup.style.display = 'block';

                                setTimeout(function() {
                                    popup.style.display = 'none';
                                }, 3000);
                            }
                        </script>

                    @endsection
            @endforeach
            </div>
            @endif

            @if ($CatalogRuleItems->count() > 0 )
                    <div class=" text-xl font-bold relative z-40 mt-8 ml-3">{{__('velocity::app-static.advantages.campaigns')}}</div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6 ml-3">
                        @foreach ($CatalogRuleItems as $item)
                            <div class="group cursor-default">
                                <div class="bg-coupon-gray rounded-10p px-4 py-2 drop-shadow-couponshadow group-hover:drop-shadow-orangeshadow transition-all ease-in-out duration-300">
                                    <div class="flex flex-wrap justify-between ">
                                        <div class="flex space-x-2 w-1/2 relative">
                                            <img class="rounded-10p " src="/deneme/campaign.png" alt="" />
                                            <div class="absolute w-full h-full flex justify-center items-center -rotate-6">
                                                @php

                                                    $start_date = new \DateTime($item->starts_from);
                                                    $end_date = new \DateTime($item->ends_till);

                                                    $start_day_month = $start_date->format('d F');
                                                    $end_day_month = $end_date->format('d F');

                                                    list($start_day, $start_month) = explode(' ', $start_day_month);
                                                    list($end_day, $end_month) = explode(' ', $end_day_month);

                                                    $date = '';
                                                    if ($start_month == $end_month) {
                                                        $date = $start_day . ' - ' . $end_day . ' ' . $start_month;
                                                    }else{
                                                        $date = $start_day . ' ' . $start_month . ' - ' . $end_day . ' ' . $end_month;
                                                    }


                                                @endphp

                                                <div class="text-sm lg:text-base text-white mr-3 pr-3 ml-5">
                                                    {{$date}}
                                                    <br>
                                                    <span class="text-base lg:text-lg font-bold text-coupon-darkgreen">{{__('velocity::app-static.advantages.discount')}}</span> {{__('velocity::app-static.advantages.opportunity')}}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2 w-1/2 relative">

                                            <div class=" w-full h-full flex justify-end items-center">
                                                <div class="text-xl font-bold relative z-40 text-center">{{ $item->name }}</div>

                                            </div>
                                            <div class="absolute w-full h-full flex justify-end items-center -rotate-6">
                                                <img class="rounded-10p max-h-[85px] " src="/deneme/campaign-star.png" alt="" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class=" mt-3 pb-2 border-b-1p border-terra-bg-light-gray-2"></div>
                                    <div class="mt-2">
                                        @if(!is_null($item->action_target))
                                            <a href="{{ $item->action_target }}" class="font-semibold text-lg hover:text-terra-khaki-green cursor-pointer">{{ $item->title }}</a>
                                        @else
                                            <div class="font-semibold text-lg hover:text-terra-khaki-green cursor-default">{{ $item->title }}</div>
                                        @endif
                                        <div class="text-sm text-terra-dark-gray">
                                            {{ $item->description }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
            @endif
        @else
        <div class="empty">
            <div class="pt-10 pb-14 bg-off-white rounded-50p mt-5 flex flex-col justify-center items-center space-y-7">
                <div class="p-5 bg-terra-khaki-green rounded-2xl">
                    <img src="/deneme/svg/green-star.svg" alt="">
                </div>
                <div class="font-terramirum font-bold text-2xl text-center">{{ __('velocity::app-static.advantages.empty-msg') }}</div>
                <div class="font-terramirum font-bold text-lg text-center text-terra-via-gray">{{ __('velocity::app-static.wishlist.empty-content') }}</div>
                <div class="font-terramirum font-bold text-2xl text-center text-terra-khaki-green">
                    <a href="/" class="flex unset">
                        <u>{{ __('velocity::app-static.wishlist.btn-text') }}</u>
                        <img src="/deneme/svg/arrow.svg" class="ml-2" alt="">
                    </a>
                </div>
            </div>
        </div>
        @endif


</div>
@endsection

@push('script')

@endpush