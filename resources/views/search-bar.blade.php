<div class="relative">
    <input type="text" placeholder="Arama.." class="bg-[#E9EFEB] rounded-2xl pl-10 pr-4 py-2 w-2/12 focus:outline-none focus:shadow-outline" />
    <div class="absolute top-0 left-0 flex items-center h-full ml-3">
        <svg id="Group_2831" data-name="Group 2831" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="15.745" height="15.745" viewBox="0 0 15.745 15.745">
            <defs>
              <clipPath id="clip-path">
                <rect id="Rectangle_11" data-name="Rectangle 11" width="15.745" height="15.745" transform="translate(0 0)" fill="none"/>
              </clipPath>
            </defs>
            <g id="Group_15" data-name="Group 15" transform="translate(0 0)" clip-path="url(#clip-path)">
              <path id="Path_36" data-name="Path 36" d="M12.4,11.88a1.157,1.157,0,0,1,.153.108q1.493,1.488,2.984,2.979a1.368,1.368,0,0,1,.***********,0,0,1-.031.511.408.408,0,0,1-.524.051,1.366,1.366,0,0,1-.158-.143q-1.486-1.484-2.969-2.97a1.16,1.16,0,0,1-.114-.158,7.082,7.082,0,1,1,.526-.522M.8,7.105A6.306,6.306,0,1,0,7.126.807,6.31,6.31,0,0,0,.8,7.105" transform="translate(0 0)"/>
            </g>
        </svg>                    
    </div>
</div>
