{"name": "flynsarmy/db-blade-compiler", "description": "Render Blade templates from Eloquent Model Fields", "keywords": ["laravel", "blade", "compiler", "eloquent", "model"], "license": "MIT", "authors": [{"name": "Flyn San", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://me2bits.com"}], "require": {"php": ">=7.1"}, "autoload": {"psr-0": {"Flynsarmy\\DbBladeCompiler\\": "src/"}}, "minimum-stability": "stable", "extra": {"laravel": {"providers": ["Flynsarmy\\DbBladeCompiler\\DbBladeCompilerServiceProvider"], "aliases": {"DbView": "Flynsarmy\\DbBladeCompiler\\Facades\\DbView"}}}}