<div class="layered-filter-wrapper left">
    {!! view_render_event('bagisto.shop.products.list.layered-nagigation.before') !!}

    <layered-navigation
        attribute-src="{{ route('catalog.categories.filterable-attributes', $category->id ?? null) }}"
        max-price-src="{{ route('catalog.categories.maximum-price', $category->id ?? null) }}">
    </layered-navigation>

    {!! view_render_event('bagisto.shop.products.list.layered-nagigation.after') !!}
</div>


@push('scripts')

<script type="text/x-template" id="layered-navigation-template-test">
   <div>

        <section class="container bg-white my-6 mb-3">
            <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:!max-w-7xl mx-auto border-b-2 border-terra-dark-gray pb-3 pt-6">
               <div class="filter-content">
                     <div class="filter-attributes">
                        <filter-attribute-item
                            v-for='(attribute, index) in attributes'
                            :key="index"
                            :index="index"
                            :attribute="attribute"
                            :appliedFilterValues="appliedFilters[attribute.code]"
                            :max-price-src="maxPriceSrc"
                            @onFilterAdded="addFilters(attribute.code, $event)">
                        </filter-attribute-item>
                    </div>
                </div>

                <div class="flex justify-between w-full items-start">
                    <div
                    class="flex w-7/12 items-start item"
                    v-for='(option, index) in attribute.options'
                    v-if='option.attribute_id === 40'
                    >
                        <input
                        type="button"
                        :id="option.id"
                        style="box-shadow: 0 0px 15px rgb(121 145 78 / 75%) inset"
                        class=" font-terramirum font-bold bg-white text-terra-khaki-green text-lg leading-none rounded-full py-2.5 px-4 w-full whitespace-nowrap mr-8 hover:border-1 border-terra-khaki-green"
                        @click="optionClicked(option.id, $event)"
                        />@{{ option.label }}
                        <!--<button style="box-shadow: 0 0px 15px rgb(121 145 78 / 75%) inset" class=" font-terramirum font-bold bg-white text-terra-khaki-green text-lg leading-none rounded-full py-2.5 px-4 w-full whitespace-nowrap mr-8 hover:border-1 border-terra-khaki-green">Konut</button>
                        <button style="box-shadow: 0 0px 10px rgb(121 145 78 / 75%)" class=" font-terramirum font-bold bg-white text-terra-khaki-green text-sm leading-none rounded-full py-3 px-4 w-full whitespace-nowrap mr-8 hover:border-1 border-terra-khaki-green">Dükkan ve Ofis</button>
                        <button style="box-shadow: 0 0px 10px rgb(121 145 78 / 75%)" class=" font-terramirum font-bold bg-white text-terra-khaki-green text-sm leading-none rounded-full py-3 px-4 w-full whitespace-nowrap mr-8 hover:border-1 border-terra-khaki-green">Endüstriyel</button>
                        <button style="box-shadow: 0 0px 10px rgb(121 145 78 / 75%)" class=" font-terramirum font-bold bg-white text-terra-khaki-green text-sm leading-none rounded-full py-3 px-4 w-full whitespace-nowrap hover:border-1 border-terra-khaki-green">Arsa</button> -->
                    </div>

                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="186" height="42" viewBox="0 0 234.743 52.825">
                            <defs>
                                <clipPath id="clip-path">
                                    <rect id="Rectangle_966" data-name="Rectangle 966" width="234.743" height="52.825" fill="none" />
                                </clipPath>
                            </defs>
                            <g id="Group_1623" data-name="Group 1623" clip-path="url(#clip-path)">
                                <path id="Path_1562" data-name="Path 1562" d="M52.825,26.412A26.412,26.412,0,1,1,26.412,0,26.417,26.417,0,0,1,52.825,26.412" transform="translate(0 -0.001)" fill="#afafaf" />
                                <path id="Path_1563" data-name="Path 1563" d="M14.968,6.613c.962-.033,1.917-.008,2.887-.008h.395v-5c-.518-.049-1.053-.123-1.588-.148-.987-.041-1.974-.09-2.961-.066A7.242,7.242,0,0,0,9.523,2.673,6.307,6.307,0,0,0,7.014,6.852,14.241,14.241,0,0,0,6.841,8.99c-.025,1.127,0,2.246,0,3.364v.42H2.054v5.585H6.808V32.392h5.816V18.384h4.738c.239-1.859.477-3.693.724-5.61H17.025c-1.349.008-4.442,0-4.442,0s.016-2.764.049-3.965c.041-1.645,1.02-2.147,2.336-2.2"
                                    transform="translate(14.841 9.994)" fill="#fff" fill-rule="evenodd" />
                                <path id="Path_1564" data-name="Path 1564" d="M60.2,26.412A26.412,26.412,0,1,1,33.784,0,26.417,26.417,0,0,1,60.2,26.412" transform="translate(53.267 -0.001)" fill="#afafaf" />
                                <path id="Path_1565" data-name="Path 1565"
                                    d="M18.046,21.071a6.217,6.217,0,0,1-5.774-4.31,6.034,6.034,0,0,0,2.657-.082l.09-.041a6.18,6.18,0,0,1-4.031-2.846,6.021,6.021,0,0,1-.913-3.307,6.079,6.079,0,0,0,2.764.757,6.251,6.251,0,0,1-2.558-3.767,6.187,6.187,0,0,1,.666-4.5,17.74,17.74,0,0,0,12.807,6.49c-.033-.247-.074-.461-.1-.683a6.017,6.017,0,0,1,.913-4.055,5.953,5.953,0,0,1,4.212-2.813,6.066,6.066,0,0,1,5.437,1.736.307.307,0,0,0,.329.1,12.687,12.687,0,0,0,3.611-1.39l.082-.041.041-.008a6.385,6.385,0,0,1-2.657,3.381,11.755,11.755,0,0,0,3.455-.929l.025.033c-.239.3-.469.625-.716.929a12.385,12.385,0,0,1-2.254,2.147.2.2,0,0,0-.107.189,16.364,16.364,0,0,1-.115,2.764,18.3,18.3,0,0,1-1.6,5.528,17.989,17.989,0,0,1-3.356,4.894A16.618,16.618,0,0,1,22.422,26a18.1,18.1,0,0,1-3.455.411,17.529,17.529,0,0,1-9.945-2.665l-.14-.1a12.469,12.469,0,0,0,6.21-.855,12.142,12.142,0,0,0,2.953-1.719"
                                    transform="translate(64.185 13.163)" fill="#fff" fill-rule="evenodd" />
                                <path id="Path_1566" data-name="Path 1566" d="M67.569,26.412A26.412,26.412,0,1,1,41.156,0,26.417,26.417,0,0,1,67.569,26.412" transform="translate(106.535 -0.001)" fill="#afafaf" />
                                <path id="Path_1567" data-name="Path 1567"
                                    d="M44.037,8.536a7.376,7.376,0,0,0-2.262-4.779A7.773,7.773,0,0,0,36.65,1.676c-3.29-.181-11.1-.3-13.844.156a7.073,7.073,0,0,0-6.054,5.429c-.666,2.385-.526,13.358-.14,15.711a7.075,7.075,0,0,0,5.618,6.1c2.2.568,13.054.494,15.489.1a7.086,7.086,0,0,0,6.128-5.593c.642-2.443.419-12.84.189-15.045M41.462,22.52a4.718,4.718,0,0,1-4.45,4.22c-2.254.247-12.338.387-14.428-.2A4.582,4.582,0,0,1,19.1,22.586,93.856,93.856,0,0,1,19.1,8.471,4.729,4.729,0,0,1,23.53,4.259a98.675,98.675,0,0,1,13.778.058A4.726,4.726,0,0,1,41.52,8.775a99.654,99.654,0,0,1-.058,13.745M30.283,8.331a7.173,7.173,0,1,0,7.165,7.181,7.175,7.175,0,0,0-7.165-7.181m-.049,11.812a4.639,4.639,0,1,1,4.68-4.6,4.639,4.639,0,0,1-4.68,4.6m9.18-12.083a1.678,1.678,0,1,1-1.678-1.686,1.682,1.682,0,0,1,1.678,1.686"
                                    transform="translate(117.687 10.958)" fill="#fff" />
                                <path id="Path_1568" data-name="Path 1568"
                                    d="M44.037,8.536a7.376,7.376,0,0,0-2.262-4.779A7.773,7.773,0,0,0,36.65,1.676c-3.29-.181-11.1-.3-13.844.156a7.073,7.073,0,0,0-6.054,5.429c-.666,2.385-.526,13.358-.14,15.711a7.075,7.075,0,0,0,5.618,6.1c2.2.568,13.054.494,15.489.1a7.086,7.086,0,0,0,6.128-5.593c.642-2.443.419-12.84.189-15.045M41.462,22.52a4.718,4.718,0,0,1-4.45,4.22c-2.254.247-12.338.387-14.428-.2A4.582,4.582,0,0,1,19.1,22.586,93.856,93.856,0,0,1,19.1,8.471,4.729,4.729,0,0,1,23.53,4.259a98.675,98.675,0,0,1,13.778.058A4.726,4.726,0,0,1,41.52,8.775a99.654,99.654,0,0,1-.058,13.745M30.283,8.331a7.173,7.173,0,1,0,7.165,7.181,7.175,7.175,0,0,0-7.165-7.181m-.049,11.812a4.639,4.639,0,1,1,4.68-4.6,4.639,4.639,0,0,1-4.68,4.6m9.18-12.083a1.678,1.678,0,1,1-1.678-1.686,1.682,1.682,0,0,1,1.678,1.686"
                                    transform="translate(117.687 10.958)" fill="#fff" />
                                <path id="Path_1569" data-name="Path 1569" d="M74.941,26.412A26.412,26.412,0,1,1,48.528,0,26.417,26.417,0,0,1,74.941,26.412" transform="translate(159.802 -0.001)" fill="#afafaf" />
                                <path id="Path_1570" data-name="Path 1570" d="M24.33,10.054h5.363V27.278H24.33ZM27.011,1.5A3.105,3.105,0,1,1,23.9,4.6a3.1,3.1,0,0,1,3.109-3.1" transform="translate(172.707 10.831)" fill="#fff" />
                                <path id="Path_1571" data-name="Path 1571" d="M25.014,2.915h5.133V5.267h.074a5.634,5.634,0,0,1,5.067-2.78c5.421,0,6.416,3.562,6.416,8.2v9.451H36.357V11.766c0-2-.041-4.565-2.78-4.565-2.788,0-3.216,2.172-3.216,4.417v8.522H25.014Z" transform="translate(180.742 17.97)" fill="#fff" />
                            </g>
                        </svg>

                    </div>

                </div>
            </div>
        </section>

    </div>
</script>

<script>
    Vue.component('layered-navigation', {
        template: '#layered-navigation-template-test',

        props: [
            'attributeSrc',
            'maxPriceSrc',
        ],

        data: function() {
            return {
                appliedFilters: {},
                attributes: [],
            }
        },

        created: function () {
            this.setFilterAttributes();

            this.setAppliedFilters();
        },

        methods: {
            setFilterAttributes: function () {
                axios
                    .get(this.attributeSrc)
                    .then((response) => {
                        this.attributes = response.data.filter_attributes;
                    });
            },

            setAppliedFilters: function () {
                let urlParams = new URLSearchParams(window.location.search);

                urlParams.forEach((value, index) => {
                    this.appliedFilters[index] = value.split(',');
                });
            },

            addFilters: function (attributeCode, filters) {
                if (filters.length) {
                    this.appliedFilters[attributeCode] = filters;
                } else {
                    delete this.appliedFilters[attributeCode];
                }

                this.applyFilter();
            },

            applyFilter: function () {
                let params = [];

                for (key in this.appliedFilters) {
                    if (key != 'page') {
                        params.push(key + '=' + this.appliedFilters[key].join(','));
                    }
                }

                window.location.href = "?" + params.join('&');
            },
        }
    });

</script>


@endpush



@push('scripts')

    <script type="text/x-template" id="layered-navigation-template">
        <div v-if="attributes.length > 0">

            <div class="font-terramirum font-bold text-xl tracking-wide mt-10">
               <!-- Tüm Kategoriler -->
               {{ __('velocity::app-static.category.all-categories')}}

            </div>

            <div class="filter-content">
                <div class="filter-attributes">
                    <filter-attribute-item
                        v-for='(attribute, index) in attributes'
                        :key="index"
                        :index="index"
                        :attribute="attribute"
                        :appliedFilterValues="appliedFilters[attribute.code]"
                        :max-price-src="maxPriceSrc"
                        @onFilterAdded="addFilters(attribute.code, $event)">
                    </filter-attribute-item>
                </div>
            </div>
        </div>
    </script>

    <script type="text/x-template" id="filter-attribute-item-template">
        <div :class="`cursor-pointer filter-attributes-item active`">
           <!-- <div class="filter-attributes-title" @click="active = ! active">
                <h6 class="fw6 display-inbl">@{{ attribute.name ? attribute.name : attribute.admin_name }}</h6>

                <div class="float-right display-table">
                    <span class="link-color cursor-pointer" v-if="appliedFilters.length" @click.stop="clearFilters()">
                        {{ __('shop::app.products.remove-filter-link-title') }}
                    </span>

                    <i :class="`icon fs16 cell ${active ? 'rango-arrow-up' : 'rango-arrow-down'}`"></i>
                </div>
            </div> -->

            <div class="filter-attributes-content w-full flex justify-start items-center mb-2">

                <ul type="none" class="items ml15" v-if="attribute.type != 'price'">
                    <li
                        class="item"
                        v-for='(option, index) in attribute.options'
                        v-if='option.attribute_id != 40'
                        >
                        <div
                            class="checkbox self-center mt-6 relative group w-full"
                            @click="optionClicked(option.id, $event)">
                            <input
                                type="checkbox"
                                :id="option.id"
                                v-bind:value="option.id"
                                v-model="appliedFilters"
                                class="w-6 h-6 border-1 border-borderflat-gray rounded-md focus:outline-none checked:bg-borderflat-gray"
                                @change="addFilter($event)" />
                            <span class="font-terramirum font-semibold text-sm text-black pl-2">
                                @{{ option.label ? option.label : option.admin_name }}
                            </span>
                        </div>
                    </li>
                </ul>
                <ul type="none" class="items ml15" v-if="attribute.type != 'price'">
                    <li
                        class="item"
                        v-for='(option, index) in attribute.options'
                        v-if='option.attribute_id === 40'
                        >
                        <div
                            class="checkbox self-center mt-6 relative group w-full"
                            @click="optionClicked(option.id, $event)">
                            <input
                                type="checkbox"
                                :id="option.id"
                                v-bind:value="option.id"
                                v-model="appliedFilters"
                                class="w-6 h-6 border-1 border-borderflat-gray rounded-md focus:outline-none checked:bg-borderflat-gray"
                                @change="addFilter($event)" />
                            <span class="font-terramirum font-semibold text-sm text-black pl-2">
                                @{{ option.label ? option.label : option.admin_name }}
                            </span>
                        </div>
                    </li>
                </ul>

                 <!-- <div class="price-range-wrapper mt-3">
                    <div class="font-terramirum font-bold text-xl tracking-wide mb-2  ">Konum</div>


                    <div class="col-12 no-padding flex w-full items-center">
                        <input
                            type="text"
                            name="ulke"
                            class="peer h-8 placeholder:text-placeholdergray text-sm font-terramirum bg-off-via-white border-1 rounded-xl border-terra-flat-gray-2 w-full focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white mt-2"
                            :placeholder="'Ülke'"
                            >
                    </div>
                    <div class="col-12 no-padding flex w-full items-center">
                        <input
                            type="text"
                            name="sehir"
                            class="peer h-8 placeholder:text-placeholdergray text-sm font-terramirum bg-off-via-white border-1 rounded-xl border-terra-flat-gray-2 w-full focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white mt-2"
                            :placeholder="'Şehir'"
                            >
                    </div>
                    <div class="col-12 no-padding flex w-full items-center">
                        <input
                            type="text"
                            name="semt"
                            class="peer h-8 placeholder:text-placeholdergray text-sm font-terramirum bg-off-via-white border-1 rounded-xl border-terra-flat-gray-2 w-full focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white mt-2"
                            :placeholder="'Semt,Mahalle'"
                            >
                    </div>
                </div> -->


                <div class="price-range-wrapper mt-3" v-if="attribute.type == 'price'">
                    <div class="font-terramirum font-bold text-xl tracking-wide mb-3">
                       <!-- Fiyat aralığı  -->
                        {{ __('velocity::app-static.category.price-range')}}
                    </div>

                    <vue-slider
                        ref="slider"
                        v-model="sliderConfig.value"
                        :process-style="sliderConfig.processStyle"
                        :tooltip-style="sliderConfig.tooltipStyle"
                        :max="sliderConfig.max"
                        :lazy="true"
                        @change="priceRangeUpdated($event)"
                    ></vue-slider>

                    <!-- <div class="filter-input row col-12 no-padding flex w-full items-center"> -->
                    <div class="col-12 no-padding flex w-full items-center">
                        <input
                            type="text"
                            name="price_from"
                            class="p-2 mr-1 px-1 w-1/2 placeholder:text-terra-text-gray text-xs font-terramirum bg-off-via-white border-1 rounded-xl border-terra-flat-gray-2 focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white"
                            :value="sliderConfig.priceFrom"
                            {{-- :placeholder="'Min.'" --}}
                            id="price_from"
                            >

                        <label class="col text-center" for="to"> / </label>

                        <input
                            type="text"
                            name="price_to"
                            class="p-2 mr-1 px-1 w-1/2 placeholder:text-terra-text-gray text-xs font-terramirum bg-off-via-white border-1 rounded-xl border-terra-flat-gray-2 focus:outline-none focus:ring-transparent focus:border-terra-flat-gray-2 group-hover:placeholder-white"
                            :value="sliderConfig.priceTo"
                            {{-- :placeholder="'Max.'" --}}
                            id="price_to"
                             >

                       <!-- <button
                            class="mt-2 font-terramirum w-1/2 items-center bg-white text-terra-khaki-green rounded-lg py-2 px-1 self-center text-xs font-semibold"
                            style="box-shadow: 0 0px 6px rgb(0 0 0 / 16%)"
                            >
                            Güncelle
                        </button> -->

                    </div>
                </div>



            </div>
        </div>
    </script>

    <script>
        Vue.component('layered-navigation', {
            template: '#layered-navigation-template',

            props: [
                'attributeSrc',
                'maxPriceSrc',
            ],

            data: function() {
                return {
                    appliedFilters: {},
                    attributes: [],
                }
            },

            created: function () {
                this.setFilterAttributes();

                this.setAppliedFilters();
            },

            methods: {
                setFilterAttributes: function () {
                    axios
                        .get(this.attributeSrc)
                        .then((response) => {
                            this.attributes = response.data.filter_attributes;
                        });
                },

                setAppliedFilters: function () {
                    let urlParams = new URLSearchParams(window.location.search);

                    urlParams.forEach((value, index) => {
                        this.appliedFilters[index] = value.split(',');
                    });
                },

                addFilters: function (attributeCode, filters) {
                    if (filters.length) {
                        this.appliedFilters[attributeCode] = filters;
                    } else {
                        delete this.appliedFilters[attributeCode];
                    }

                    this.applyFilter();
                },

                applyFilter: function () {
                    let params = [];

                    for (key in this.appliedFilters) {
                        if (key != 'page') {
                            params.push(key + '=' + this.appliedFilters[key].join(','));
                        }
                    }

                    window.location.href = "?" + params.join('&');
                },
            }
        });

        Vue.component('filter-attribute-item', {
            template: '#filter-attribute-item-template',

            props: [
                'index',
                'attribute',
                'addFilters',
                'appliedFilterValues',
                'maxPriceSrc',
            ],

            data: function() {
                return {
                    active: false,

                    appliedFilters: [],

                    sliderConfig: {
                        max: 500,

                        value: [0, 0],

                        processStyle: {
                            "backgroundColor": "#FF6472"
                        },

                        tooltipStyle: {
                            "borderColor": "#FF6472",
                            "backgroundColor": "#FF6472",
                        },

                        priceTo: 0,

                        priceFrom: 0,
                    }
                }
            },

            created: function () {
                if (! this.index) this.active = false;

                if (this.appliedFilterValues && this.appliedFilterValues.length) {
                    this.appliedFilters = this.appliedFilterValues;

                    if (this.attribute.type == 'price') {
                        this.sliderConfig.value = this.appliedFilterValues;
                        this.sliderConfig.priceFrom = this.appliedFilterValues[0];
                        this.sliderConfig.priceTo = this.appliedFilterValues[1];
                    }

                    this.active = true;
                }

                this.setMaxPrice();
            },

            methods: {
                setMaxPrice: function () {
                    if (this.attribute['code'] != 'price') {
                        return;
                    }

                    axios
                        .get(this.maxPriceSrc)
                        .then((response) => {
                            let maxPrice  = response.data.max_price;
                            this.sliderConfig.max = maxPrice ? ((parseInt(maxPrice) !== 0 || maxPrice) ? parseInt(maxPrice) : 500) : 500;

                            if (! this.appliedFilterValues) {
                                this.sliderConfig.value = [0, this.sliderConfig.max];
                                this.sliderConfig.priceTo = this.sliderConfig.max;
                            }
                        });
                },

                addFilter: function (e) {
                    this.$emit('onFilterAdded', this.appliedFilters);
                },

                priceRangeUpdated: function (value) {
                    this.appliedFilters = value;
                    this.$emit('onFilterAdded', this.appliedFilters);
                },

                clearFilters: function () {
                    if (this.attribute.type == 'price') {
                        this.sliderConfig.value = [0, 0];
                    }

                    this.appliedFilters = [];

                    this.$emit('onFilterAdded', this.appliedFilters);
                },

                optionClicked: function (id, {target}) {
                    let checkbox = $(`#${id}`);

                    if (checkbox && checkbox.length > 0 && target.type != "checkbox") {
                        checkbox = checkbox[0];
                        checkbox.checked = ! checkbox.checked;

                        if (checkbox.checked) {
                            this.appliedFilters.push(id);
                        } else {
                            let idPosition = this.appliedFilters.indexOf(id);

                            if (idPosition == -1)
                                idPosition = this.appliedFilters.indexOf(id.toString());

                            this.appliedFilters.splice(idPosition, 1);
                        }

                        this.addFilter(event);
                    }
                }
            }
        });
    </script>
@endpush
