{!! view_render_event('bagisto.shop.products.add_to_cart.before', ['product' => $product]) !!}

    <a class="close-when-detail-2 close-when-wishlist flex {{ Route::currentRouteName() === 'customer.wishlist.index' ? 'items-left px-10 ml-5' : 'ml-2 items-center justify-center'}} w-full">
        {{-- @if (
            isset($showCompare)
            && $showCompare
        )
            <compare-component
                @auth('customer')
                    customer="true"
                @endif

                @guest('customer')
                    customer="false"
                @endif

                slug="{{ $product->url_key }}"
                product-id="{{ $product->id }}"
                add-tooltip="{{ __('velocity::app.customer.compare.add-tooltip') }}"
            ></compare-component>
        @endif --}}

        @if(Route::currentRouteName() === 'customer.wishlist.index')
            <a class="close-when-detail-2" href="https://www.facebook.com/share.php?u=https://terramirum.com" target="_blank">
                <img src="/deneme/svg/share-icon.svg" style="width: 20px;"/>
            </a>
        @else
            <a class="close-when-detail-2" href="https://www.facebook.com/share.php?u=https://terramirum.com" target="_blank">
            <img class="close-when-detail" src="/deneme/svg/share-icon.svg"/>
            </a>
        @endif

        {{-- <img class="close-when-detail" src="/deneme/svg/share-icon.svg"  /> --}}

        @if (
            ! (
                isset($showWishlist)
                && ! $showWishlist
            )
            && core()->getConfigData('general.content.shop.wishlist_option')
        )
            @include('paywithcryptoformarket::products.wishlist', [
                'addClass' => $addWishlistClass ?? ''
            ])
        @endif

        @if (
            isset($form)
            && ! $form
        )
            @if ($product->contractAddress)
                <div class=" bg-terra-orange px-10 py-2 rounded-2xl hover:opacity-90 mr-3 h-full flex justify-center items-center">
                    <a href="https://sepolia.etherscan.io/address/{{$product->contractAddress}}" target="_blank" class="cursor-pointer font-terramirum text-white font-bold text-md text-truncate text-center">
                            Smart Contract
                    </a>
                </div>
            @endif
        @endif

        <div class="add-to-cart-btn pl0">
            @if (
                isset($form)
                && ! $form
            )
                <input type="hidden" name="quantity" value="1">
                <button2
                    type="submit"
                    {{ ! $product->isSaleable() ? 'disabled' : '' }}
                    class="{{ $addToCartBtnClass ?? '' }} w-full lg:w-auto flex justify-center items-center font-terramirum font-bold bg-terra-orange  hover:opacity-90 text-white rounded-2xl py-2 px-10 self-center text-xl tracking">

                    @if (
                        ! (isset($showCartIcon)
                        && ! $showCartIcon)
                    )
                        <img src="/deneme/svg/cart-icon.svg"/>
                    @endif

                    {{ ($product->type == 'booking') ?  __('shop::app.products.book-now') :  __('BUY') }}
                </button2>
                @elseif(isset($addToCartForm) && ! $addToCartForm)
                <form
                    method="POST"
                    action="{{ route('cart.add', $product->product_id) }}">

                    @csrf

                    <input type="hidden" name="product_id" value="{{ $product->product_id }}">
                    <input type="hidden" name="quantity" value="1">
                    <button
                        type="submit"
                        {{ ! $product->isSaleable() ? 'disabled' : '' }}
                        class="btn btn-add-to-cart {{ $addToCartBtnClass ?? '' }}">

                        @if (
                            ! (isset($showCartIcon)
                            && ! $showCartIcon)
                        )
                        <img src="/deneme/svg/cart-icon.svg"/>
                        @endif

                        <span class="fs14 fw6 text-uppercase text-up-4">
                            {{ ($product->type == 'booking') ?  __('shop::app.products.book-now') : $btnText ?? __('shop::app.products.add-to-cart') }}
                        </span>
                    </button>
                </form>
            @else
                <add-to-cart
                    form="true"
                    csrf-token='{{ csrf_token() }}'
                    product-flat-id="{{ $product->id }}"
                    product-id="{{ $product->product_id }}"
                    reload-page="{{ $reloadPage ?? false }}"
                    move-to-cart="{{ $moveToCart ?? false }}"
                    wishlist-move-route="{{ $wishlistMoveRoute ?? false }}"
                    {{-- add-class-to-btn="{{ $addToCartBtnClass ?? '' }}" --}}
                    is-enable={{ ! $product->isSaleable() ? 'false' : 'true' }}
                    show-cart-icon={{ ! (isset($showCartIcon) && ! $showCartIcon) }}
                    {{-- btn-text="{{ (! isset($moveToCart) && $product->type == 'booking') ?  __('shop::app.products.book-now') : $btnText ?? __('shop::app.products.add-to-cart') }}"> --}} >
                </add-to-cart>
            @endif
        </div>
    </a>

{!! view_render_event('bagisto.shop.products.add_to_cart.after', ['product' => $product]) !!}
