@component('shop::emails.layouts.terramirum-master')
    @section('content')
        <div style="display: flex;justify-content: start;align-items: center;margin-top: 15px;">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 48.341 44.95" style="width: 45px; height: 45px;">
                <g id="Group_4736" data-name="Group 4736" transform="translate(-1262.099 -77.684)">
                    <g id="Group_4729" data-name="Group 4729" transform="translate(1262.099 77.684)">
                        <g id="Group_4709" data-name="Group 4709" transform="translate(0)">
                            <path id="Path_2514" data-name="Path 2514" d="M23.863,0c.871.126,1.753.2,2.613.385,8.386,1.768,14.217,6.636,17.08,14.7A22.146,22.146,0,0,1,34.22,41.463,21.535,21.535,0,0,1,19.1,44.571a23.249,23.249,0,0,1-11.76-5.538A22.676,22.676,0,0,1,.379,18.31,22.968,22.968,0,0,1,17.78.491C18.833.284,19.9.162,20.963,0ZM22.457,1.546A20.874,20.874,0,1,0,43.287,22.4,20.882,20.882,0,0,0,22.457,1.546" transform="translate(0)" fill="#d9e5d2"></path>
                            <path id="Path_2515" data-name="Path 2515" d="M37.629,15.966A21.455,21.455,0,1,1,15.892,37.312,21.591,21.591,0,0,1,37.629,15.966" transform="translate(-15.249 -15.086)" fill="#d9e5d2"></path>
                        </g>
                    </g>
                    <g id="Group_4730" data-name="Group 4730" transform="translate(1270.076 81.63)">
                        <g id="Group_4726" data-name="Group 4726" transform="translate(0 0)">
                            <g id="Ellipse_1" data-name="Ellipse 1" transform="translate(0)" fill="none" stroke="#407d38" stroke-width="3">
                                <ellipse cx="20.182" cy="20.502" rx="20.182" ry="20.502" stroke="none"></ellipse>
                                <ellipse cx="20.182" cy="20.502" rx="18.682" ry="19.002" fill="none"></ellipse>
                            </g>
                        </g>
                    </g>
                    <g id="Group_4728" data-name="Group 4728" transform="translate(1279.755 92.96)">
                        <g id="Group_4727" data-name="Group 4727">
                            <path id="Path_2525" data-name="Path 2525" d="M0,15.957V.018C.067.012.135,0,.2,0H18.091c.072,0,.144.007.234.012V5.006h2.66v.229q0,5.593,0,11.185a2.036,2.036,0,0,1-.274,1.1,2.012,2.012,0,0,1-1.866.966q-7.889-.008-15.778,0c-.13,0-.26-.005-.389-.014A2.863,2.863,0,0,1,.118,16.407c-.043-.149-.079-.3-.118-.45M1.24,1.239c0,.069-.01.128-.01.188q0,7.067,0,14.133a1.858,1.858,0,0,0,.07.5A1.656,1.656,0,0,0,3.036,17.25H16.989c.072,0,.143-.007.172-.008-.024-.393-.066-.764-.067-1.135q-.007-7.3,0-14.6V1.239Zm17.08,5v.24q0,4.966,0,9.931c0,.055,0,.109,0,.164a.719.719,0,1,0,1.433-.121q0-5.007,0-10.013v-.2Z" transform="translate(0 0)" fill="#407d38"></path>
                            <path id="Path_2526" data-name="Path 2526" d="M69.016,126.172H60.181v-6.114h8.835Zm-7.611-1.231h6.374v-3.652H61.405Z" transform="translate(-57.71 -115.128)" fill="#407d38"></path>
                            <rect id="Rectangle_400" data-name="Rectangle 400" width="8.835" height="1.197" transform="translate(2.465 2.472)" fill="#407d38"></rect>
                            <rect id="Rectangle_401" data-name="Rectangle 401" width="8.835" height="1.197" transform="translate(2.471 12.306)" fill="#407d38"></rect>
                            <rect id="Rectangle_402" data-name="Rectangle 402" width="8.835" height="1.198" transform="translate(2.465 14.764)" fill="#407d38"></rect>
                            <rect id="Rectangle_403" data-name="Rectangle 403" width="3.302" height="1.197" transform="translate(12.547 2.472)" fill="#407d38"></rect>
                            <rect id="Rectangle_404" data-name="Rectangle 404" width="3.302" height="1.197" transform="translate(12.547 4.93)" fill="#407d38"></rect>
                            <rect id="Rectangle_405" data-name="Rectangle 405" width="3.302" height="1.197" transform="translate(12.554 7.389)" fill="#407d38"></rect>
                            <rect id="Rectangle_406" data-name="Rectangle 406" width="3.302" height="1.197" transform="translate(12.554 9.847)" fill="#407d38"></rect>
                            <rect id="Rectangle_407" data-name="Rectangle 407" width="3.304" height="1.205" transform="translate(12.55 12.302)" fill="#407d38"></rect>
                            <rect id="Rectangle_408" data-name="Rectangle 408" width="3.302" height="1.197" transform="translate(12.547 14.764)" fill="#407d38"></rect>
                        </g>
                    </g>
                </g>
            </svg>
            <div style="font-weight: bold;color: #407D38;font-size: 18px;margin-left: 6px;">
                {!! __('mail.sales.order.new.greeting', ['order_id' => $order->order_id]) !!}
            </div>
        </div>
        <div style="padding: 10px 7px;background-color: #EBEAEA;border-radius: 6px; display: flex; margin-top: 20px;">
            <span style="font-weight: normal;color: #000000;font-size: 12px;margin-left: 6px;">
                {!! __('mail.sales.order.new.summary', ['customer_name' => $order->customer_full_name, 'order_id' => $order->order_id]) !!}
            </span>
        </div>
        <div style="margin-top: 20px;">
            <div style="font-weight: normal;color: #000000;font-size: 12px;margin-left: 6px;">
                {!! __('mail.sales.order.new.dear', ['customer_name' => $order->customer_full_name, 'order_id' => $order->order_id]) !!}
            </div>
        </div>
        <div style="margin-top: 20px;">
            <div style="font-weight: normal;color: #FF9201;font-size: 12px;margin-left: 6px;">
                {!! __('mail.sales.order.new.summary-invoice') !!}
            </div>
        </div>

        <div style="padding: 10px 7px;background-color: #EBEAEA;border-radius: 6px; display: flex; margin-top: 20px;">
            <div style="display: flex;width: 100%;">
                <div style="width: 70%">
                    <div style="text-align:center;font-weight: 600;color: #000000;font-size: 12px;margin-left: 6px;">{{ __('mail.sales.order.view.product-name') }}</div>
                </div>
                <div style="width: 20%">
                    <div style="text-align:center;font-weight: 600;color: #000000;font-size: 12px;margin-left: 6px; ">{{ __('mail.sales.order.view.price') }}</div>
                </div>
                <div style="width: 10%">
                    <div style="text-align:center;font-weight: 600;color: #000000;font-size: 12px;margin-left: 6px;">{{ __('mail.sales.order.view.qty') }}</div>
                </div>
            </div>
        </div>

        @foreach ($order->items as $item)
            <div style="width: 100%;margin-top: 10px;padding-bottom: 10px;border-bottom: .5px solid #407D38;">
                <div style="display: flex;width: 100%;">
                    <div style="width: 70%">
                        <div style="font-weight: normal;color: #000000;font-size: 12px;margin-left: 6px;">
                            {{ $item->name }}

                            @if (isset($item->additional['attributes']))
                                <div class="item-options">

                                    @foreach ($item->additional['attributes'] as $attribute)
                                        <b>{{ $attribute['attribute_name'] }} : </b>{{ $attribute['option_label'] }}</br>
                                    @endforeach

                                </div>
                            @endif
                        </div>
                    </div>
                    <div style="width: 20%">
                        <div style="text-align:center;font-weight: normal;color: #000000;font-size: 12px;margin-left: 6px;">
                            {{ core()->formatPrice($item->price, $order->order_currency_code) }}
                        </div>
                    </div>
                    <div style="width: 10%">
                        <div style="text-align:center;font-weight: normal;color: #000000;font-size: 12px;margin-left: 6px;">
                            {{ $item->qty_ordered }}
                        </div>
                    </div>
                </div>
            </div>
        @endforeach

        <div style="display: flex;justify-content: end;">
            <div
                style="width: 50%; margin-top: 20px;padding-bottom: 10px;background-color: #E2E9DD; padding: 10px;border-radius: 8px;display: flex;flex-wrap: wrap; ">
                <div style="width: 60%;">
                    <div style="font-weight: normal;color: #000000;font-size: 12px;margin-left: 6px;">
                        <span>{{ __('mail.sales.order.view.subtotal') }}</span>
                    </div>
                    <div style="font-weight: 600;color: #000000;font-size: 12px;margin-left: 6px;">
                        @foreach (Webkul\Tax\Helpers\Tax::getTaxRatesWithAmount($order, false) as $taxRate => $taxAmount )
                            <span
                                id="taxrate-{{ core()->taxRateAsIdentifier($taxRate) }}">{{ __('mail.sales.order.view.tax') }} {{ $taxRate }} %</span>
                        @endforeach
                    </div>
                    @if ($order->shipping_address)
                        <div style="font-weight: normal;color: #FF9201;font-size: 12px;margin-left: 6px;">
                            <span>{{ __('mail.sales.order.view.shipping-handling') }}</span>
                        </div>
                    @endif
                    @if ($order->discount_amount > 0)
                        <div style="font-weight: normal;color: #FF9201;font-size: 12px;margin-left: 6px;">
                            <span>{{ __('mail.sales.order.view.discount') }}</span>
                        </div>
                    @endif
                </div>
                <div style="width: 40%;">
                    <div style="font-weight: normal;color: #000000;font-size: 12px;margin-left: 6px;">
                        <span>
                            {{ core()->formatPrice($order->sub_total, $order->order_currency_code) }}
                        </span>
                    </div>
                    <div style="font-weight: normal;color: #000000;font-size: 12px;margin-left: 6px;">
                        @foreach (Webkul\Tax\Helpers\Tax::getTaxRatesWithAmount($order, false) as $taxRate => $taxAmount )
                            <span id="taxamount-{{ core()->taxRateAsIdentifier($taxRate) }}">
                                {{ core()->formatPrice($taxAmount, $order->order_currency_code) }}
                            </span>
                        @endforeach
                    </div>
                    @if ($order->shipping_address)
                        <div style="font-weight: normal;color: #FF9201;font-size: 12px;margin-left: 6px;">
                            {{ core()->formatPrice($order->shipping_amount, $order->order_currency_code) }}
                        </div>
                    @endif
                    @if ($order->discount_amount > 0)
                        <div style="font-weight: normal;color: #FF9201;font-size: 12px;margin-left: 6px;">
                            {{ core()->formatPrice($order->discount_amount, $order->order_currency_code) }}
                        </div>
                    @endif
                </div>
                <div
                    style="width: 100%; display: flex; padding-top: 5px;margin-top: 5px;border-top: .5px solid #407D38;">
                    <div style="width: 60%;">
                        <div style="font-weight: normal;color: #000000;font-size: 12px;margin-left: 6px;font-weight: 700;">
                            <span>{{ __('mail.sales.order.view.grand-total') }}</span>
                        </div>
                    </div>
                    <div style="width: 40%;">
                        <div style="font-weight: normal;color: #000000;font-size: 12px;margin-left: 6px;font-weight: 700;">
                            <span>
                                {{ core()->formatPrice($order->grand_total, $order->order_currency_code) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endsection
@endcomponent