<?php

return [
    'homepage' => [
        'drsp' => 'Digital Real Estate Share Certificate (DRESC)',
        'search-text' => 'Search...',
        'content' => 'Real estate investors from all regions of our country will love our investment platform, which is fast, secure, user-friendly, and available 24/7.',
        'content-line' => 'With our expert team, we are always by your side!',
        'easy-buy-sell' => 'Easy Buying and Selling',
        'buy-sell' => 'Buy/Sell',
        'drsp-card' => 'You can instantly buy or sell your real estate properties using "digital real estate share certificates" as you please.',
        'mfnft-title' => 'Multi-Fractional Non-Fungible Token/ MFNFT',
        'mfnft-card' => 'Token holders have ownership rights to rental income and other proceeds generated from the real estate properties. They can also participate in community decisions regarding the real estate by exercising their voting rights.',
        'nft-title' => 'NFT (Non-Fungible Token) Documentation',
        'nft-card' => 'You can mint your real estate properties as NFTs (Non-Fungible Tokens) and convert them into cash as desired on national and international marketplaces.',
        'ensure' => 'How Do We Achieve This?',
        'ensure-comment' => 'Credipto prepares you for faster, more secure, and profitable real estate investments by utilizing blockchain technology.',
        'ensure-text' => 'In this new technological world where you come across terms like blockchain, NFTs, Web 3, DeFi, we aim to guide you through.',
        'text' => 'You can buy, sell, and generate rental income from real estate properties.',
        'start-button' => 'Get Started',
        'portfolio' => 'Become one of those who buy and sell the digital real estate share certificates (DRESC) of the products in our portfolio!',
        'terramirum-text' => 'Our marketplace (TerraMirum) is presented as a modern investment space based on blockchain technology to virtual world investors, with an approximate transaction volume of 3 trillion USD, aiming to prevent them from disconnecting from the real world.',
        'tenant' => 'Tenant',
        'thorne' => 'Credipto',
        'income' => 'Income',
        'convert' => 'Converts to Token',
        'token' => 'Token Holders Receive It',
        'send-bank' => 'Sends to Your Bank Account',
        'slider-text' => 'Blockchain Based Real Estate',
        'slider-text-line' => 'Investment Platform',
        'subscribe-newsletter' => 'Subscribe to our newsletter',
        'sign-up' => 'Sign Up',
        'kvvk-agree-text' => 'I accept the Privacy Statement and KVKK Policy',
        'subscribe-ok' => 'Email added to newsletter list',
        'check-email' => 'Check your email address!',
        'check-kvkk' => 'Make sure you approve the contracts!',
    ],

    'footer' => [
        'guide' => 'How To Guides',
        'webinars' => 'Webinars',
        'blog' => 'Blog',
        'career' => 'Career',
        'case' => 'Case Studies',
        'resource' => 'Resource Center',
        'about-terramirum' => 'About Terramirum',
        'follow' => 'Follow Us!',
        'footer-text' => '2024 Terramirum, Inc. All rights reserved.',
        'terms' => 'Terms and Conditions',
        'privacy-policy' => 'Privacy Policy',

    ],

    'subscriber' => [
        'subscribe' => 'Subscribe to our newsletter',
        'kvkk' => 'I accept the Privacy Statement and KVKK Policy',
        'subscriber-text' => 'Credipto GmbH. aims to develop digital solution proposals in the real estate sector, starting with the selection of the "Model Digital Product."',

    ],

    'products' => [
        'product-cat-title' => 'In the category the most luxurious ads',
        'product-cat-text' => '3rd most rated post',
        'breadcrumb-part1' => 'Home',
        'breadcrumb-part2' => 'Housing',
        'breadcrumb-land' => 'Land',
        'unit-price' => 'm2 Price',
        'unit-price-land' => 'Unit Price',
        'value' => 'Value',
        'drsp-sales' => 'DRESC Sales Rate',
        'drsp-unit-price' => 'DRESC Share Unit Price',
        'property-value' => 'Value of the Real Estate',
        'drsp-total' => 'DRESC Total Share Amount',
        'using' => 'Residential Use',
        'type' => 'Building/Land Type',
        'usage-area' => 'Usega Area',
        'using-status' => 'Using Status',
        'details' => 'Details',
        'bath-color' => 'Bathroom Color',
        'kitchen-color' => 'Kitchen Color',
        'room' => 'Room',
        'usage-area-sqfit' => 'Residential Use / Sq Fit',
        'parking' => 'Car Park',
        'price' => 'Price',
        'land-city' => 'City',
        'land-district' => 'District',
        'land-neighborhood' => 'Neighborhood',
        'land-ada' => 'Block',
        'land-parcel' => 'Plot',
        'land-expertise' => 'Expertise',
        'land-area' => 'Area',
        'land-kind' => 'Kind',
        'land-status' => 'Current Situation',
        'land-unit-price' => 'Unit Price',
        'land-total-price' => 'Total Price',
        'land-total-nft' => 'Total NFT',
        'land-nft-unit-price' => 'NFT Unit Price',
        'land-sold-nft' => 'Sold NFT',
        'land-sale-nft' => 'NFT On Sale',
        'land-increase' => 'NFT Value Increase',

    ],

    'cart' => [
        'page-title' => 'Cart',
        'cart-text' => 'My Cart',
        'empty-msg' => 'Your Cart is Empty',
        'empty-btn-msg' => 'Continue Reviewing',
        'share-amount' => 'Quantity',
        'total-amount' => 'Total (Share) Amount',
        'remainig-share' => 'Unit Amount',
        'address' => 'My invoice will come to the same address.',
        'continue' => 'Continue',
        'payment-methods' => 'Payment methods',
        'payment-options' => 'Payment options',
        'address-info' => 'Address information',
        'share-info' => 'Share Information',
        'campaign-info' => 'discount applied within the',
        'discount' => 'Discount',
        'discount-amount' => 'Discounted Amount',

    ],

    'mini-cart' => [
        'total' => 'Total',
        'cart' => 'Cart',
        'share' => 'Number of Shares',
        'pay' => 'Pay',

    ],

    'login' => [
        'remember' => 'Remember Me',
        'continue' => 'Continue without a member',
        'signup' => 'Sign Up!',
        'signin' => 'Sign in to your TerraMirum account',
        'page-title' => 'Sign In',

    ],

    'signup' => [
        'hello' => 'Hello',
        'start-msg' => 'Create an account, do not miss the opportunity!',
        'terms' => 'I accept the terms of membership by clicking on "Sign Up".',
        'id' => 'ID Number',
        'verify' => 'Verify',
        'phone' => 'Phone',
        'pass-confirm' => 'Password Confirm',
        'uname' => 'Username',
        'btn-text' => 'Sign Up',
        'nationality' => 'Nationality',

    ],

    'verification' => [
        'page-title' => 'Verification',
        'hello' => 'Hello',
        'start-msg' => 'Please verify your account to continue.',

        'email' => [
            'required' => 'Email is required',
            'email' => 'Email is not valid',
            'unique' => 'Email is already taken',
        ],
        'id_number' => [
            'required' => 'ID Number is required',
            'numeric' => 'ID Number must be numeric',
            'digits' => 'ID Number must be 11 digits',
            'unique' => 'ID Number is already taken',
        ],
        'first_name' => [
            'required' => 'First Name is required',
            'string' => 'First Name must be string',
        ],
        'last_name' => [
            'required' => 'Last Name is required',
            'string' => 'Last Name must be string',
        ],
        'date_of_birth' => [
            'required' => 'Date of Birth is required',
            'date_format' => 'Date of Birth must be in format of YYYY-MM-DD',
        ],
    ],

    'verification-pending' => [
        'page-title' => 'Verification',
        'hello' => 'Hello',
        'start-msg' => 'Verification is in progress. Please try again later.',
        'contact-msg' => 'Your verification is in progress. Need help? Contact us.',
        'verify-info' => 'Your account verification process has not been completed, but you can make purchases. Confirmation must be completed for withdrawals.',
    ],

    'category' => [
        'price-range' => 'Price Range',
        'all-categories' => 'All Categories',
    ],

    'wishlist' => [
        'title' => 'Wishlist',
        'empty-msg' => 'There are no products in your wishlist.',
        'empty-content' => 'Discover the tokens of special real estate, divided into thousands of shares.',
        'btn-text' => 'Discover',
    ],

    'address' => [
        'empty-msg' => 'There are no address in your profile.',
        'empty-content' => 'You do not have any saved addresses here, please try to create it by clicking the add button.',
    ],

    'user-profile' => [
        'wallet' => 'Wallet/Orders',
        'wallet-address' => 'Wallet Address',
        'empty-wallet' => 'Your wallet is empty',
        'table-product-name' => 'Product',
        'table-actions' => 'Actions',
        'contract-button' => 'Smart Contract',
        'contract-notfound' => 'Smart Contract Not Found',
        'advantage' => 'Advantages',
        'wishlist' => 'Wishlist',
        'notification' => 'Notifications',
        'address' => 'My Addresses',
        'logout' => 'Logout',
        'preferences' => 'Communication Preferences',
        'user-info' => 'User Information',
        'account-info' => 'Account Information',
        'profile' => 'Information Profile',
        'info' => 'You can change your username a maximum of 2 times.',
        'change' => 'Change',
        'pass-change' => 'Password Change',
        'pass-change-msg' => 'Your password must contain at least one letter, number or special character. Also, your password must be at least 8 characters.',
        'info-msg' => 'Within the scope of the Consent Text, you can specify the methods you prefer to be informed about important campaigns.',
        'info-email' => 'I would like to receive e-mail about campaigns and market bulletins that may interest me.',
        'info-sms' => 'I would like to receive SMS about campaigns and market bulletins that may interest me.',
        'info-tel' => 'I would like to receive calls about campaigns and market bulletins that may interest me.',
        'info-text' => 'When you turn off your communication preferences regarding the campaigns, you can continue to receive e-mail, notification, SMS or phone call regarding your membership settings.',
        'info-part' => 'You can specify the methods you prefer to be informed about important campaigns within the scope of',
        'consent-text' => 'Consent Text',
        'amount-vue' => 'Amount',
        'email' => 'E-mail',
        'sms' => 'SMS',
        'call' => 'Phone Call',
        'current-pass' => 'Current Password',
        'new-pass' => 'New Password',
        'confirm-pass' => 'Confirm Password',
        'wallet-tab' => 'Wallet',
        'orders-tab' => 'Orders',
        'nft-tab' => 'My NFTs',
        'trans-tab' => 'Transactions',
        'orders-menu-title' => 'Wallet/Orders',
        'wallet-currency' => 'Currency',
        'wallet-amount' => 'Amount',
        'nft-empty-msg' => 'Discover the tokens of special real estate and land, divided into thousands of shares.',
        'nft-empty-title' => 'You don\'t have any NFTs yet.',
        'transactions' => 'transactions',
        'deposit' => 'deposit',
        'withdraw' => 'withdraw',
        'balance' => 'balance',
        'pending-balance' => 'pending balance',

    ],

    'forgot-password' => [
        'forgot-pass' => 'Forgot Password',
        'new-pass' => 'New Password',
        'new-pass-confirm' => 'Confirm New Password',
        'title' => 'Having trouble logging in?',
        'or' => 'Or',
        'btn-text' => 'Send Reset Mail',
        'new-account' => 'Create a New Account',
        'return' => 'Return to Home Screen',
        'reset-pass' => 'Reset Password',
        'reset-my-pass' => 'Reset My Password',
        'success-msg' => 'Your password has been successfully reset.',
        'success-msg-line' => 'You can login to your account.',
        'content' => 'Select your mobile phone number or e-mail address registered to your account and we will send you a code to re-login to your account.',

    ],

    'orders' => [
        'title' => 'Your order has been received, Congratulations.',
        'btn-text' => 'Go to my purchases',
        'content' => 'Details about your order have been sent to',
        'order-no' => 'Order Number',
        'billing' => 'Billing Address',
        'amount' => 'Amount Paid',
        'bank-info' => 'Bank Account Information',
        'bank-name' => 'Bank',
        'account-name' => 'Account Name',
        'iban' => 'IBAN',
        'branch-code' => 'Branch Code',
    ],

    'advantages' => [
        'title' => 'Advantages',
        'coupons' => 'My Coupons',
        'use' => 'Use It',
        'purchase-amount' => 'Purchase Amount',
        'conditions' => 'Conditions',
        'expire-date' => 'Expiration Date',
        'last' => 'The last',
        'days' => 'days',
        'campaigns' => 'Campaigns',
        'discount' => 'Discount',
        'opportunity' => 'Opportunity',
        'favorite' => 'Add Favorites',
        'notify' => 'Be the First to Notify!',
        'add-fav' => 'Add to your favourites!',
        'expired' => 'Expired',
        'info-msg' => 'Start adding favorites now, be the first to know about the deals on the big discount days on',
        'empty-msg' => 'There is no defined advantage yet.',

    ],

    'my-nfts' => [
        'empty-msg' => 'You don\'t have any NFTs yet.',
        'empty-content' => 'Discover the tokens of special real estate and land, divided into thousands of shares.',
        'btn-text' => 'Discover',
    ]
];
