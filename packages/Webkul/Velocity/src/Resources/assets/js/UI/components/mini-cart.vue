<template>
    <div :class="`dropdown`">
        <div class="dropdown-toggle btn btn-link" id="mini-cart" >
            <div class="mini-cart-content">
                <div class="badge-container flex">
                    <span class="badge" v-text="cartItems.length" v-if="cartItems.length != 0" style="background: #79914e; color:white; border-radius: 50%;right: 0;"></span>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="30" height="34" viewBox="0 0 39.577 35">
                    <g id="Group_1629" data-name="Group 1629">
                        <path id="Path_1574" data-name="Path 1574"
                              d="M11.106,29.04h9.662a4.473,4.473,0,0,0,.5,4.132,4.229,4.229,0,0,0,2.409,1.675,4.316,4.316,0,0,0,3.335-7.865c.783-3.256,1.562-6.482,2.333-9.709Q30.981,10.439,32.6,3.6a1.081,1.081,0,0,1,1.175-.946c1.443-.006,2.887,0,4.33,0A1.305,1.305,0,0,0,39.576,1.33,1.32,1.32,0,0,0,38.1.006C36.677,0,35.25,0,33.822,0A3.725,3.725,0,0,0,30.03,3.03c-.176.739-.391,1.47-.528,2.216-.069.376-.233.442-.571.441q-8.541-.013-17.083-.006-5.187,0-10.373.007A1.326,1.326,0,0,0,.064,7.5Q1.833,14.935,3.6,22.372c.24,1.009.631,1.32,1.656,1.32H25.1c-.155.626-.267,1.2-.442,1.753a1.372,1.372,0,0,1-1.36.926c-.936.01-1.871,0-2.807,0q-6.614,0-13.229,0A4.312,4.312,0,1,0,11.29,31.72a4.637,4.637,0,0,0-.183-2.68M19.3,8.34c-.137,1.693-.27,3.341-.4,4.989H12.842c-.132-1.675-.262-3.313-.394-4.989ZM2.986,8.353A1.519,1.519,0,0,1,3.2,8.308c2.077,0,4.154.006,6.231-.012.392,0,.39.239.41.488q.161,2.014.307,4.028c.012.168,0,.337,0,.523H4.173L2.986,8.353m18.961-.016h6.781c-.029.174-.044.312-.076.446-.326,1.37-.67,2.736-.972,4.111-.08.366-.216.485-.59.48-1.681-.02-3.362-.009-5.043-.011-.153,0-.307-.013-.5-.022.134-1.681.265-3.321.4-5M13.06,16.031h5.619c-.122,1.6-.247,3.164-.358,4.727-.022.311-.218.29-.431.29q-1.665,0-3.331,0h-1.1c-.135-1.7-.264-3.342-.4-5.016m7.883,5.015c.123-1.622.245-3.168.353-4.715.021-.309.187-.344.441-.341.935.009,1.87,0,2.8,0h2.393c-.4,1.671-.782,3.282-1.178,4.889a.31.31,0,0,1-.24.157c-1.5.009-3,.007-4.573.007m-10.138,0c-1.56,0-3.076,0-4.592-.011-.088,0-.224-.15-.253-.255-.132-.455-.236-.918-.345-1.38-.265-1.118-.528-2.236-.794-3.364h5.586l.4,5.009m15.63,9.639a1.641,1.641,0,0,1-1.648,1.667A1.683,1.683,0,0,1,23.113,30.7a1.7,1.7,0,0,1,1.665-1.671,1.652,1.652,0,0,1,1.658,1.66m-20.97-.008a1.647,1.647,0,0,1,1.663-1.652A1.662,1.662,0,1,1,7.1,32.35a1.631,1.631,0,0,1-1.635-1.673"
                              transform="translate(0 0)" />
                    </g>
                </svg>


                <!-- <span class="fs18 fw6 cart-text" v-text="cartText"></span> -->
            </div>

            <!-- <div class="down-arrow-container">
                <span class="rango-arrow-down"></span>
            </div> -->
        </div>

        <div
            v-if="cartItems.length === 0"
            class="modal-content dropdown-list sensitive-modal cart-modal-content rounded-xl border-solid"
            style="border-top: 2px solid #327b05ab"
        >
            <div class="mini-cart-container">
                    <div class="modal-footer">
                        <span class="fs16 text-nowrap fw6 product-name px-7">
                            <!-- Sepetiniz Boş!  -->
                            {{ emptyMsg }}
                        </span>
                    </div>
            </div>

        </div>

        <div
            v-else
            id="cart-modal-content"
            class="modal-content dropdown-list sensitive-modal cart-modal-content rounded-xl border-solid"

        >
            <div class="mini-cart-container">
                <div
                    class="row items-center small-card-container border-b-1p border-terra-khaki-green"
                    :key="index"
                    v-for="(item, index) in cartItems">
                    <div class="col-3 product-image-container mr15">
                        <span class="remove-item" @click="removeProduct(item.id)">
                            <span class="rango-close"></span>
                        </span>

                        <a
                            class="unset"
                            :href="`${$root.baseUrl}/${item.url_key}`"
                        >
                            <div
                                class="product-image rounded-14p"
                                :style="
                                    `background-image: url(${item.images.medium_image_url});`
                                "
                            ></div>
                        </a>
                    </div>
                    <div class="col-9 no-padding card-body align-vertical-top">
                        <div class="no-padding">
                            <div
                                class="text-sm font-bold text-nowrap product-name"
                                v-html="item?.parent?.name ? item.parent.name + ' - ' + item.name : item?.name || ''"
                            ></div>

                            <div class="fs18 card-current-price fw6 text-right">
                                <div class="display-inbl">
                                    <label class="text-sm">
                                        <!-- Hisse Adedi -->
                                        {{ shareText }}
                                    </label>
                                    <input
                                        type="text"
                                        disabled
                                        :value="item.quantity"
                                        class="ml5 rounded-14p"
                                    />
                                </div>
                                <span class="card-total-price text-sm font-bold">
                                    {{
                                        isTaxInclusive == '1'
                                            ? item.base_total_with_tax
                                            : item.base_total
                                    }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer pt-2 pb-1">
                <h5 class="col-6 text-left text-lg font-bold">
                    <!-- Toplam -->
                    {{ totalText }}
                </h5>

                <h5 class="col-6 text-right text-lg font-bold no-padding">
                    {{
                        isTaxInclusive == '1'
                            ? cartInformation.base_grand_total
                            : cartInformation.base_sub_total
                    }}
                </h5>
            </div>

            <div class="modal-footer pt-1 py-3" style="border-top: none!important;">
                <a
                    class="col-auto text-left fs16 remove-decoration bg-terra-soft-khaki-green font-terramirum text-white font-bold text-sm text-truncate px-4 py-2 rounded-full hover:opacity-90 transition-all ease-in-out duration-300"
                    :href="viewCartRoute"
                    >
                    {{cartText2}}
                </a>

                <div class="text-right bg-terra-orange px-4 py-2 rounded-full" style="box-shadow: 0 1px 2px rgb(50 123 5 /67%)">
                    <a :href="checkoutRoute" @click="checkMinimumOrder($event)"
                    class="font-terramirum remove-decoration text-white font-bold text-sm text-truncate hover:opacity-90 transition-all ease-in-out duration-300"
                    >
                            {{payText}}
                    </a>
                </div>
            </div>
        </div>


    </div>
</template>

<style lang="scss">
.hide {
    display: none !important;
}
</style>

<script>
export default {
    props: [
        'isTaxInclusive',
        'viewCartRoute',
        'checkoutRoute',
        'checkMinimumOrderRoute',
        'cartText',
        'viewCartText',
        'checkoutText',
        'subtotalText',
        'totalText',
        'shareText',
        'payText',
        'cartText2',
        'emptyMsg',
    ],

    data: function() {
        return {
            cartItems: [],
            cartInformation: []
        };
    },

    mounted: function() {
        this.getMiniCartDetails();
    },

    watch: {
        '$root.miniCartKey': function() {
            this.getMiniCartDetails();
        }
    },

    methods: {
        getMiniCartDetails: function() {
            this.$http
                .get(`${this.$root.baseUrl}/mini-cart`)
                .then(response => {
                    if (response.data.status) {
                        this.cartItems = response.data.mini_cart.cart_items;
                        this.cartInformation =
                            response.data.mini_cart.cart_details;
                    }
                })
                .catch(exception => {
                    console.log(this.__('error.something_went_wrong'));
                });
        },

        removeProduct: function(productId) {
            this.$http
                .delete(`${this.$root.baseUrl}/cart/remove/${productId}`)
                .then(response => {
                    this.cartItems = this.cartItems.filter(
                        item => item.id != productId
                    );
                    this.$root.miniCartKey++;

                    window.showAlert(
                        `alert-${response.data.status}`,
                        response.data.label,
                        response.data.message
                    );
                })
                .catch(exception => {
                    console.log(this.__('error.something_went_wrong'));
                });
        },

        checkMinimumOrder: function(e) {
            e.preventDefault();

            this.$http.post(this.checkMinimumOrderRoute).then(({ data }) => {
                if (!data.status) {
                    window.showAlert(`alert-warning`, 'Warning', data.message);
                } else {
                    window.location.href = this.checkoutRoute;
                }
            });
        }
    }
};
</script>
