<template>
    <div class="col-12 lg-card-container list-card product-card row" v-if="list">
        <div class="product-image">
            <a :title="product.name" :href="`${baseUrl}/${product.slug}`">
                <img
                    :src="product.image || product.product_image"
                    :onerror="`this.src='${this.$root.baseUrl}/deneme/large-product-placeholder.png'`" />

                <product-quick-view-btn :quick-view-details="product" v-if="!isMobile()"></product-quick-view-btn>
            </a>
        </div>

        <div class="product-information">
            <div>
                <div class="product-name">
                    <a :href="`${baseUrl}/${product.slug}`" :title="product.name" class="unset">
                        <span class="fs16">{{ product.name }}</span>
                    </a>
                </div>

                <div class="sticker new" v-if="product.new">
                    {{ product.new }}
                </div>

                <div class="product-price" v-html="product.priceHTML"></div>

                <div class="product-rating" v-if="product.totalReviews && product.totalReviews > 0">
                    <star-ratings :ratings="product.avgRating"></star-ratings>
                    <span>{{ __('products.reviews-count', {'totalReviews': product.totalReviews}) }}</span>
                </div>

                <div class="product-rating" v-else>
                    <span class="fs14" v-text="product.firstReviewText"></span>
                </div>

                <vnode-injector :nodes="getDynamicHTML(product.addToCartHtml)"></vnode-injector>
            </div>
        </div>
    </div>

    <div class="w-full lg:w-11/12 px-2 large-width mx-auto" v-else >
                <div class="w-full rounded-34p bg-terra-light-wheat pb-4 px-0 mb-5 overflow-hidden" style="box-shadow: 0 5px 10px rgb(0 0 0 / 16%)">
                        <a :href="`${baseUrl}/${product.slug}`" :title="product.name" class=" product-image-container max-h-[200px] lg:max-h-auto overflow-y-hidden lg:overflow-y-visible">
                            <img
                                loading="lazy"
                                :alt="product.name"
                                :src="product.image || product.product_image"
                                :data-src="product.image || product.product_image"
                                class="w-full max-w-full card-img-top lzy_img max-h-[220px] transition-all duration-500" style="border-radius: 34px 34px 0 0 ; max-height: 220px;"
                                :onerror="`this.src='${this.$root.baseUrl}/deneme/large-product-placeholder.png'`"
                                />
                        </a>
                    <!-- <img src="/deneme/uskudar.png" class="w-full max-w-full" alt=""> -->
                    <div class="w-full flex justify-center items-center pt-2 bg-terra-light-wheat relative z-40">
                            <div class="w-full flex justify-center font-terramirum font-bold text-sm px-4 text-center flex items-center border-b-1p pb-2 border-terra-khaki-green transition-all duration-500">
                                <a v-if="product.short_name"
                                   :href="`${baseUrl}/${product.slug}`" :title="product.name"
                                   class="leading-4 transition-all duration-500 product-image-container hover:text-black flex justify-center items-center" style="min-height: 40px;">
                                    {{ product.short_name }}
                                </a>
                                <a :href="`${baseUrl}/${product.slug}`" :title="product.name"
                                   class="leading-4 transition-all duration-500 product-image-container hover:text-black flex justify-center items-center" style="min-height: 40px;"
                                   v-else>
                                    {{ product.name }}
                                </a>
                            </div>
                    </div>
                    <div class="mt-1 px-4 bg-terra-light-wheat relative z-40">
                        <div class="flex flex-wrap border-b-1p pb-1 border-terra-khaki-green">
                        <div class="w-1/2 " style="border-right: 1px solid rgb(121, 145, 78)">
                            <div class="flex items-center justify-center ml-1">
                                <div class="font-terramirum font-bold text-2xs text-terra-orange mr-2">{{amount}}</div>
                                <img src="/deneme/svg/i-icon-orange.svg">
                            </div>
                            <div class="flex items-center">
                                <img src="/deneme/svg/up-icon.svg">
                                <span class="text-base ml-2"> {{ product.currencySymbol }} </span>
                                <!-- <div class="font-terramirum font-bold text-xs ml-0.5" >{{ product.deger }}</div> -->
                                <div class="font-terramirum font-bold text-xs ml-0.5" v-if="product.productAttributeFamilyId === 3">
                                    {{ product.arsaToplamFiyat }}
                                </div>
                                <div class="font-terramirum font-bold text-xs ml-0.5" v-else>
                                    {{ product.price }}
                                </div>
                            </div>
                        </div >
                        <div class="w-1/2 pl-1">
                            <div class="flex items-center justify-center ml-1">
                                <div class="font-terramirum font-bold text-2xs text-terra-orange mr-2" v-if="product.productAttributeFamilyId === 3">
                                    {{ product.unitPriceLand }}
                                </div>
                                <div class="font-terramirum font-bold text-2xs text-terra-orange mr-2" v-else>
                                    {{unitPrice}}
                                </div>
                                <img src="/deneme/svg/i-icon-orange.svg">
                            </div>
                            <div class="flex justify-center items-center">
                                <img src="/deneme/svg/hand-icon.svg">

                                    <div class="font-terramirum font-bold text-xs ml-0.5 whitespace-nowrap" v-if="product.productAttributeFamilyId === 3">
                                        <span class="text-base ml-2"> {{ product.currencySymbol }} </span>
                                        {{ product.price }}
                                    </div>
                                    <div class="font-terramirum font-bold text-xs ml-0.5 whitespace-nowrap" v-else>
                                        <!-- <span class="text-base ml-2">£</span> -->
                                        <span class="text-base ml-2"> {{ product.currencySymbol }} </span>
                                        {{ product.birimFiyat }}
                                    </div>

                            </div>
                        </div>
                        <!-- <div class="ml-6 bg-terra-soft-khaki-green px-5 rounded-full" style="box-shadow: 0 1px 2px rgb(50 123 5 /67%)">
                                <img src="/deneme/svg/bidding-icon.svg">
                        </div> -->
                        </div>
                        <vnode-injector :nodes="getDynamicHTML(product.addToCartHtml)"></vnode-injector>
                    </div>
                </div>
            </div>

    <!-- <div class="card grid-card product-card-new" v-else>
        <a :href="`${baseUrl}/${product.slug}`" :title="product.name" class="product-image-container">
            <img
                loading="lazy"
                :alt="product.name"
                :src="product.image || product.product_image"
                :data-src="product.image || product.product_image"
                class="card-img-top lzy_img"
                :onerror="`this.src='${this.$root.baseUrl}/vendor/webkul/ui/assets/images/product/large-product-placeholder.png'`" />
                 :src="`${$root.baseUrl}/vendor/webkul/ui/assets/images/product/meduim-product-placeholder.png`" /> -->

            <!-- <product-quick-view-btn :quick-view-details="product"></product-quick-view-btn>
        </a> -->

        <!-- <div class="card-body">
            <div class="product-name col-12 no-padding">
                <a
                    class="unset"
                    :title="product.name"
                    :href="`${baseUrl}/${product.slug}`">

                    <span class="fs16">{{ product.name }}</span>
                </a>
            </div>

            <div class="sticker new" v-if="product.new">
                {{ product.new }}
            </div>

            <div v-html="product.priceHTML"></div>

            <div
                class="product-rating col-12 no-padding"
                v-if="product.totalReviews && product.totalReviews > 0">

                <star-ratings :ratings="product.avgRating"></star-ratings>
                <a class="fs14 align-top unset active-hover" :href="`${$root.baseUrl}/reviews/${product.slug}`">
                    {{ __('products.reviews-count', {'totalReviews': product.totalReviews}) }}
                </a>
            </div>

            <div class="product-rating col-12 no-padding" v-else>
                <span class="fs14" v-text="product.firstReviewText"></span>
            </div>

            <vnode-injector :nodes="getDynamicHTML(product.addToCartHtml)"></vnode-injector>
        </div> -->
    <!-- </div>  -->
</template>

<script type="text/javascript">
    export default {
        props: [
            'list',
            'product',
            'amount',
            'unitPrice',
        ],

        data: function () {
            return {
                'addToCart': 0,
                'addToCartHtml': '',
            }
        },

        methods: {
            'isMobile': function () {
                if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                    return true;
                } else {
                    return false;
                }
            }
        }
    }
</script>
<style>
@media (max-width: 720px) {
    .product-image-container{
        max-height: 200px;
        overflow-y: hidden;
        display: block;
    }
}
@media (min-width: 720px) {
    .large-width{
        width: 100%!important;
    }
}
.card-img-top.lzy_img:hover{
    transform: scale(1.3);
}
</style>