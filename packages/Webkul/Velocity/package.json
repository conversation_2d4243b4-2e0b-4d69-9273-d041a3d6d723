{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "devDependencies": {"axios": "^0.24.0", "clean-webpack-plugin": "^4.0.0", "jquery": "^3.2", "laravel-mix": "^6.0.41", "laravel-mix-clean": "^0.1.0", "laravel-mix-merge-manifest": "^2.0.0", "resolve-url-loader": "^4.0.0", "sass": "^1.49.4", "sass-loader": "^12.3.0", "vue": "^2.6.14", "vue-carousel": "^0.18.0", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.14"}, "dependencies": {"@inotom/vue-go-top": "^1.3.0", "accounting": "^0.4.1", "bootstrap-sass": "^3.4.1", "font-awesome": "^4.7.0", "lazysizes": "^5.2.2", "material-icons": "^1.10.6", "vee-validate": "^2.2.15", "vue-gallery": "^2.0.5", "vue-slider-component": "^3.2.15"}}