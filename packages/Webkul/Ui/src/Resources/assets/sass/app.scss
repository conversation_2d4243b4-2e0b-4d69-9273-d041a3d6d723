/* main imports */
@import 'main/icons';
@import 'main/variables';
@import 'main/mixins';

/* includes */
@import 'includes/animations';
@import 'includes/components';

/* start from here */
* {
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

*:focus {
    outline: none;
}

// margin bottom classes
.mb-10 {
    margin-bottom: 10px;
}

.mb-15 {
    margin-bottom: 15px;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-25 {
    margin-bottom: 25px;
}

.mb-30 {
    margin-bottom: 30px;
}

.mb-35 {
    margin-bottom: 35px;
}

.mb-40 {
    margin-bottom: 40px;
}

.mb-45 {
    margin-bottom: 45px;
}

.mb-50 {
    margin-bottom: 50px;
}

.mb-60 {
    margin-bottom: 60px;
}

.mb-70 {
    margin-bottom: 70px;
}

.mb-80 {
    margin-bottom: 80px;
}

.mb-90 {
    margin-bottom: 90px;
}

// margin-top
.mt-5 {
    margin-top: 5px;
}

.mt-10 {
    margin-top: 10px;
}

.mt-15 {
    margin-top: 15px;
}

.mt-20 {
    margin-top: 20px;
}

.mt-25 {
    margin-top: 25px;
}

.mt-30 {
    margin-top: 30px;
}

.mt-35 {
    margin-top: 35px;
}

.mt-40 {
    margin-top: 40px;
}

.mt-45 {
    margin-top: 45px;
}

.mt-50 {
    margin-top: 50px;
}

.mt-60 {
    margin-top: 60px;
}

.mt-70 {
    margin-top: 70px;
}

.mt-80 {
    margin-top: 80px;
}

.mt-90 {
    margin-top: 90px;
}

.m-20 {
    margin: 20px;
}

body {
    letter-spacing: -0.26px;
    line-height: 19px;
}

a:link,
a:hover,
a:visited,
a:focus,
a:active {
    text-decoration: none;
    color: $brand-color;
}

::selection {
    background-color: $selection-color;
    color: $white-color;
}

textarea {
    resize: none;
}

ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

h1 {
    font-size: 28px;
    color: $font-color;
    margin-top: 0;
}

h2 {
    font-size: 24px;
    color: $font-color;
}

h3 {
    font-size: 20px;
    color: $font-color;
}

h4 {
    font-size: 16px;
    color: $font-color;
}

h5 {
    font-size: 12px;
    color: $font-color;
}

.hide {
    display: none !important;
}

.row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.btn {
    @include box-shadow(
        0 1px 4px 0 rgba(0, 0, 0, 0.2),
        0 0 8px 0 rgba(0, 0, 0, 0.1)
    );
    @include border-radius(3px);
    border: none;
    color: $white-color;
    cursor: pointer;
    transition: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font: inherit;
    display: inline-block;

    &:hover,
    &:active,
    &:focus {
        opacity: 0.75;
        border: none;
    }

    &.btn-sm {
        padding: 6px 12px;
    }

    &.btn-md {
        padding: 8px 16px;
    }

    &.btn-lg {
        padding: 10px 20px;
    }

    &.btn-xl {
        padding: 12px 24px;
        font-size: 16px;
    }

    &.btn-primary {
        background: $brand-color;
        color: $white-color;
    }

    &.btn-black {
        background: $black-color;
        color: $white-color;
    }

    &.btn-white {
        background: $white-color;
        color: $black-color;
    }

    &:disabled,
    &[disabled='disabled'],
    &[disabled='disabled']:hover,
    &[disabled='disabled']:active {
        cursor: not-allowed;
        background: #b1b1ae !important;
        box-shadow: none;
        opacity: 1;
    }
}

.dropdown-btn {
    min-width: 150px;
    text-align: left;
    background: $white-color;
    border: 2px solid $control-border-color;
    @include border-radius(3px);
    font-size: 14px;
    padding: 8px 35px 8px 10px;
    cursor: pointer;
    position: relative;

    &:hover,
    &:active,
    &:focus {
        opacity: 0.75;
        border: 2px solid $control-border-color;
    }

    .icon {
        position: absolute;
        right: 10px;
        top: 50%;
        margin-top: -4px;
    }
}

.dropdown-toggle {
    cursor: pointer;
}

.dropdown-open {
    position: relative;
}

.dropdown-list {
    width: 200px;
    margin-bottom: 20px;
    @include box-shadow(
        0 2px 4px 0 rgba(0, 0, 0, 0.16),
        0 0 9px 0 rgba(0, 0, 0, 0.16)
    );
    @include border-radius(3px);
    background-color: $white-color;
    position: absolute;
    display: none;
    z-index: 10;
    text-align: left;

    &.bottom-left {
        top: 42px;
        left: 0px;
    }

    &.bottom-right {
        top: 42px;
        right: 0px;
    }

    &.top-left {
        bottom: 0px;
        left: 42px;
    }

    &.top-right {
        bottom: 0px;
        right: 42px;
    }

    .search-box {
        padding: 20px;
        border-bottom: 1px solid $border-color;

        .control {
            background: $white-color;
            border: 2px solid $control-border-color;
            @include border-radius(3px);
            width: 100%;
            height: 36px;
            display: inline-block;
            vertical-align: middle;
            transition: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0px 10px;
            font-size: 15px;

            &:focus {
                border-color: $brand-color;
            }
        }
    }

    .dropdown-container {
        padding: 20px;
        overflow-y: auto;

        label {
            font-size: 15px;
            display: inline-block;
            text-transform: uppercase;
            color: #9e9e9e;
            font-weight: 700;
            padding-bottom: 5px;
        }

        ul {
            margin: 0px;
            list-style-type: none;
            padding: 0px;

            li {
                padding: 5px 0px;

                a:link,
                a:active,
                a:visited,
                a:focus {
                    color: $font-color;
                    display: block;
                }

                a:hover {
                    color: $brand-color !important;
                }

                .checkbox {
                    margin: 0;
                }

                .control-group label {
                    color: $font-color;
                    font-size: 15px;
                    font-weight: 500;
                    text-transform: capitalize;
                    width: 100%;
                }
            }
        }

        .btn {
            width: 100%;
            margin-top: 10px;
        }
    }
}

.notification {
    width: 400px;
    margin-bottom: 20px;
    @include box-shadow(
        0 2px 4px 0 rgba(0, 0, 0, 0.16),
        0 0 9px 0 rgba(0, 0, 0, 0.16)
    );
    @include border-radius(3px);
    background-color: $white-color;
    position: absolute;
    display: none;
    z-index: 10;
    text-align: left;
    border-radius: 10px;

    &.bottom-left {
        top: 42px;
        left: 0px;
    }

    &.bottom-right {
        top: 42px;
        right: 0px;
    }

    &.top-left {
        bottom: 0px;
        left: 42px;
    }

    &.top-right {
        bottom: 0px;
        right: 42px;
    }

    .dropdown-container {
        padding: 0px;
        overflow-y: auto;

        label {
            font-size: 15px;
            display: inline-block;
            text-transform: uppercase;
            color: #9e9e9e;
            font-weight: 700;
            padding-bottom: 5px;
        }

        ul {
            margin: 0px;
            list-style-type: none;
            padding: 0px;

            li {
                border-bottom: 1px solid #c7c7c7;
                padding: 0;

                a {
                    padding: 16px 10px;

                    &:link,
                    &:active,
                    &:visited,
                    &:focus {
                        color: $font-color;
                        display: block;
                    }
    
                    &:hover {
                        color: $brand-color;
                    }
                }

                &.read {
                    a {
                        text-decoration: none;
                        color: rgba(58, 58, 58, 0.5);
    
                        &:hover {
                            color: rgba(58, 58, 58, 0.5);
                        }
                    }
                }

                .checkbox {
                    margin: 0;
                }

                .control-group label {
                    color: $font-color;
                    font-size: 15px;
                    font-weight: 500;
                    text-transform: capitalize;
                    width: 100%;
                }
            }

            .bottom-li {
                border-bottom: none !important;
                color: #3a3a3a;
                display: flex;
                justify-content: space-between;

                a {
                    display: initial;
                    color: #3a3a3a !important;
                }
            }
        }

        .btn {
            width: 100%;
            margin-top: 10px;
        }
    }
}

.section {
    .secton-title {
        font-size: 18px;
        color: $font-color;
        padding: 15px 0;
        border-bottom: 1px solid $border-color;
    }

    .section-content {
        display: block;
        padding: 20px 0;

        .row {
            display: block;
            padding: 7px 0;

            .title {
                width: 200px;
                color: $font-color;
                letter-spacing: -0.26px;
                display: inline-block;
            }

            .value {
                color: $font-color;
                letter-spacing: -0.26px;
                display: inline-block;
            }
        }
    }
}

.table {
    width: 100%;

    .table-responsive {
        overflow-x: auto;
        width: 100%;

        &::-webkit-scrollbar {
            height: 5px !important;
        }

        &::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 6px rgba(200, 200, 200, 1) !important;
        }

        &::-webkit-scrollbar-thumb {
            border-radius: 10px !important;
            background-color: #fff !important;
            -webkit-box-shadow: inset 0 0 6px rgba(90, 90, 90, 0.7) !important;
        }
    }

    table {
        border-collapse: collapse;
        text-align: left;
        width: 100%;

        thead th {
            font-weight: 700;
            padding: 12px 10px;
            background: #f8f9fa;
            color: $font-color;

            &.sortable {
                cursor: pointer;
            }
        }

        tbody td {
            padding: 10px;
            border-bottom: solid 1px #d3d3d3;
            color: $font-color;
            vertical-align: top;

            &.actions {
                .action {
                    display: inline-flex;
                }

                .icon {
                    cursor: pointer;
                    vertical-align: middle;
                }
            }

            &.empty {
                text-align: center;
            }
        }

        tbody tr:last-child td {
            border-bottom: none;
        }
    }

    .control-group {
        width: 100%;
        margin-bottom: 0;

        .control {
            width: 100%;
            margin: 0;
        }
    }
}

.pagination {
    .page-item {
        background: $white-color;
        border: 2px solid $control-border-color;
        @include border-radius(3px);
        padding: 7px 14px;
        margin-right: 5px;
        font-size: 16px;
        display: inline-block;
        color: $font-color;
        vertical-align: middle;
        text-decoration: none;

        &.previous,
        &.next {
            padding: 6px 9px;
        }

        &.active {
            background: $brand-color;
            color: $white-color;
            border-color: $brand-color;
        }

        .icon {
            vertical-align: middle;
            margin-bottom: 3px;
        }
    }
}

.checkbox {
    position: relative;
    display: block;

    input {
        left: 0;
        opacity: 0;
        position: absolute;
        top: 0;
        height: 24px;
        width: 24px;
        z-index: 100;
    }

    .checkbox-view {
        background-image: url('../images/Checkbox.svg');
        height: 24px;
        width: 24px;
        margin: 0;
        display: inline-block !important;
        vertical-align: middle;
        margin-right: 5px;
    }

    input:checked + .checkbox-view {
        background-image: url('../images/Checkbox-Checked.svg');
    }

    input:disabled + .checkbox-view {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.radio {
    position: relative;
    display: block;
    margin: 10px 5px 5px 0px;

    input {
        left: -4px;
        opacity: 0;
        position: absolute;
        top: -1px;
        z-index: 100;
    }

    .radio-view {
        background-image: url('../images/controls.svg');
        background-position: -21px 0px;
        height: 20px;
        width: 20px;
        margin: 0;
        display: inline-block !important;
        vertical-align: middle;
        margin-right: 5px;
    }

    input:checked + .radio-view {
        background-position: -21px -21px;
    }

    input:disabled + .radio-view {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.rtl {
    .radio {
        input {
            left: unset;
            right: 1px;
        }
    }

    .checkbox {
        input {
            left: unset;
        }
    }
}

.boolean {
    label {
        pointer-events: none;
    }
}

.control-group {
    display: block;
    margin-bottom: 25px;
    font-size: 15px;
    color: $font-color;
    width: 750px;
    max-width: 100%;
    position: relative;

    label {
        display: block;
        color: $font-color;
        
        &.required::after {
            content: '*';
            color: $danger-color;
            font-weight: 700;
            display: inline-block;
        }
    }

    textarea.control {
        height: 100px;
        padding: 10px;
    }

    .control {
        background: $white-color;
        border: 2px solid $control-border-color;
        @include border-radius(3px);
        width: 70%;
        height: 36px;
        display: inline-block;
        vertical-align: middle;
        transition: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        padding: 0px 10px;
        font-size: 15px;
        margin-top: 10px;
        margin-bottom: 5px;

        &:focus {
            border-color: $brand-color;
        }

        &[disabled='disabled'] {
            border-color: #d3d3d3;
            background-color: #d3d3d3;
            cursor: not-allowed;
        }

        &[multiple] {
            height: 100px;
        }
    }

    &.date,
    &.datetime {
        .control {
            padding-right: 40px;
        }

        &::after {
            background-image: url('../images/Icon-Calendar.svg');
            width: 24px;
            height: 24px;
            content: '';
            display: inline-block;
            vertical-align: middle;
            margin-left: -34px;
            pointer-events: none;
            position: absolute;
            top: 50%;
            left: 70%;
        }

        .cross-icon {
            position: absolute;
            cursor: pointer;
            margin-left: -54px;
            top: 38px;
        }
    }

    &.multi-select {
        .control {
            padding: 0;
        }
    }

    .control-info {
        display: block;
        font-size: 14px;
        color: #6f6f6f;
        font-style: italic;
    }

    .control-error {
        display: none;
        color: $danger-color;
        margin-top: 5px;
    }

    &.has-error {
        .control {
            border-color: $danger-color;
        }

        .control-error {
            display: block;
        }
    }

    &.has-error.date,
    &.has-error.date {
        &::after {
            margin-top: -12px;
        }
    }

    &.price {
        .currency-code {
            vertical-align: middle;
            display: inline-block;
        }
    }

    &.input-group {
        margin-bottom: 0;
    }

    .input-group {
        position: relative;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-align: stretch;
        -ms-flex-align: stretch;
        align-items: stretch;
        width: 100%;

        .input-group-append,
        .input-group-prepend {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
        }

        > .control {
            margin: 0;
            position: relative;
            -webkit-box-flex: 1;
            -ms-flex: 1 1 auto;
            flex: 1 1 auto;
            width: 1% !important;

            &:not(:first-child) {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }

            &:not(:last-child) {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }
        }

        .input-group-prepend {
            margin-right: -1px;

            .control,
            .btn {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }
        }

        .input-group-append {
            margin-left: -1px;

            .control,
            .btn {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
        }
    }
}

.table {
    .control-group {
        &.date,
        &.datetime {
            &::after {
                top: 6px;
                left: 100%;
            }
        }

        &.has-error.date,
        &.has-error.date {
            &.date,
            &.datetime {
                &::after {
                    top: 18px;
                }
            }
        }
    }
}

.rtl {
    .control-group {
        &.date,
        &.datetime {
            .control {
                padding-left: 40px;
                padding-right: 10px;
            }

            &::after {
                margin-right: -34px;
                right: 70%;
            }

            .cross-icon {
                margin-right: -54px;
                top: 38px;
            }
        }
    }

    .table {
        .control-group {
            &.date,
            &.datetime {
                &::after {
                    right: unset !important;
                    left: auto;
                    top: 6px;
                }
            }
        }
    }
}

.control-group {
    .switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
        margin-top: 10px;
        margin-bottom: 5px;
        pointer-events: auto;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: $filter-toggle-color;
        -webkit-transition: 0.2s;
        transition: 0.2s;
    }

    .slider:before {
        position: absolute;
        content: '';
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: $white-color;
        -webkit-transition: 0.2s;
        transition: 0.2s;
    }

    input:checked + .slider {
        background-color: $brand-color;
    }

    input:focus + .slider {
        box-shadow: 0 0 1px $brand-color;
    }

    input:checked + .slider:before {
        -webkit-transform: translateX(26px);
        -ms-transform: translateX(26px);
        transform: translateX(26px);
    }

    /* rounded sliders */
    .slider.round {
        border-radius: 34px;
    }

    .slider.round:before {
        border-radius: 50%;
    }
}

.button-group {
    margin-top: 20px;
    margin-bottom: 20px;
}

.alert-wrapper {
    width: 300px;
    top: 10px;
    right: 10px;
    position: fixed;
    z-index: 100;
    text-align: left;

    .alert {
        width: 300px;
        padding: 15px;
        @include border-radius(3px);
        display: inline-block;
        @include box-shadow(
            0px 4px 15.36px 0.64px rgba(0, 0, 0, 0.1),
            0px 2px 6px 0px rgba(0, 0, 0, 0.12)
        );
        position: relative;
        animation: jelly 0.5s ease-in-out;
        transform-origin: center top;
        z-index: 500;
        margin-bottom: 10px;

        &.alert-error {
            background: $danger-color;
        }

        &.alert-info {
            background: $info-color;
        }

        &.alert-success {
            background: $success-color;
        }

        &.alert-warning {
            background: $warning-color;
        }

        .icon {
            position: absolute;
            right: 10px;
            top: 10px;
            cursor: pointer;
        }

        p {
            color: $white-color;
            margin: 0px;
            padding: 0px;
            font-size: 14px;
        }
    }
}

.tabs {
    ul {
        border-bottom: solid 1px $border-color;

        li {
            display: inline-block;

            a {
                padding: 15px 20px;
                cursor: pointer;
                margin: 0px 2px;
                text-align: center;
                color: $black-color-shade;
                display: block;
            }

            &.active a {
                border-bottom: 3px solid $brand-color;
            }
        }
    }
}

.accordian,
accordian {
    display: inline-block;
    width: 100%;

    .accordian-header,
    div[slot*='header'] {
        width: 100%;
        display: inline-block;
        font-size: 18px;
        color: $font-color;
        border-top: solid 1px $border-color;
        border-bottom: solid 1px $border-color;
        padding: 20px 15px;
        cursor: pointer;
        margin-top: -1px;

        .expand-icon {
            background-image: url('../images/Expand-Light.svg');
            margin-right: 10px;
            margin-top: 3px;
        }

        h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
            display: inline-block;
        }

        .icon {
            float: right;

            &.left {
                float: left;
            }
        }
    }

    &.error {
        .accordian-header {
            color: #ff5656 !important;
        }
    }

    .accordian-content,
    div[slot*='body'] {
        width: 100%;
        padding: 20px 15px;
        display: none;
        transition: 0.3s ease all;
    }

    &.active > .accordian-content {
        display: inline-block;
    }

    &.active > .accordian-header .expand-icon {
        background-image: url('../images/Expand-Light-On.svg');
    }
}

.tree-container {
    .tree-item {
        padding-left: 30px;
        display: inline-block;
        margin-top: 10px;
        width: 100%;

        > .tree-item {
            display: none;
        }

        &.active > .tree-item {
            display: inline-block;
        }

        .checkbox {
            margin: 0;
            display: inline-block;
        }

        .radio {
            margin: 0;
            display: inline-block;
        }

        .expand-icon {
            display: inline-block;
            margin-right: 10px;
            cursor: pointer;
            background-image: url('../images/Expand-Light.svg');
            width: 18px;
            height: 18px;
            vertical-align: middle;
        }

        .folder-icon {
            vertical-align: middle;
            margin-right: 10px;
        }

        &.active {
            > .expand-icon {
                background-image: url('../images/Expand-Light-On.svg');
            }
        }
    }

    > .tree-item {
        padding-left: 0;
    }
}

.panel {
    @include box-shadow(0 2px 25px 0 rgba(0, 0, 0, 0.15));
    @include border-radius(5px);
    background: $white-color;

    .panel-content {
        padding: 20px;
    }
}

modal {
    display: none;
}

.modal-open {
    overflow: hidden;
}

.loader-open {
    overflow: hidden;
}

.modal-overlay {
    display: none;
    overflow-y: auto;
    z-index: 1001;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
    position: fixed;
    background: $black-color;
    opacity: 0.7;
}

.modal-open .modal-overlay {
    display: block;
}

.loader-open .loader-overlay {
    display: block;
}

.modal-container {
    background: $white-color;
    top: 100px;
    width: 600px;
    max-width: 80%;
    left: 50%;
    margin-left: -300px;
    position: fixed;
    z-index: 1002;
    @include box-shadow(
        0px 15px 25px 0px rgba(0, 0, 0, 0.03),
        0px 20px 45px 5px rgba(0, 0, 0, 0.2)
    );
    animation: fade-in-white 0.3s ease-in-out;
    animation: jelly 0.5s ease-in-out;
    @include border-radius(5px);
    overflow-y: auto;
    max-height: 80%;

    .modal-header {
        padding: 20px;

        h3 {
            display: inline-block;
            font-size: 20px;
            color: $font-color;
            margin: 0;
        }

        .icon {
            float: right;
            cursor: pointer;
        }
    }

    .modal-body {
        padding: 20px;

        .control-group .control {
            width: 100%;
        }
    }
}

// responsive css for modal
@media only screen and (max-width: 770px) {
    .modal-container {
        max-width: 80%;
        left: 10%;
        margin-left: 0px;
    }

    .control-group {
        width: 100%;

        .control {
            width: 100%;
        }

        &.date,
        &.datetime {
            .control {
                padding-left: 40px;
            }
        }
    }
}

.label {
    background: #e7e7e7;
    @include border-radius(2px);
    padding: 8px;
    color: $black-color-shade;
    display: inline-block;

    &.label-sm {
        padding: 5px;
    }

    &.label-md {
        padding: 8px;
    }

    &.label-lg {
        padding: 11px;
    }

    &.label-xl {
        padding: 14px;
    }
}

.badge {
    @include border-radius(50px);
    color: white;
    padding: 8px;
    white-space: nowrap;

    &.badge-sm {
        padding: 5px;
    }

    &.badge-md {
        padding: 3px 10px;
    }

    &.badge-lg {
        padding: 11px;
    }

    &.badge-xl {
        padding: 14px;
    }

    &.badge-success {
        background-color: $success-color;
    }

    &.badge-info {
        background-color: $brand-color;
    }

    &.badge-danger {
        background-color: $danger-color;
    }

    &.badge-warning {
        background-color: $warning-color;
    }
}

// image wrapper
.image-wrapper {
    margin-bottom: 20px;
    margin-top: 10px;
    display: inline-block;
    width: 100%;

    .image-item {
        width: 200px;
        height: 200px;
        margin-right: 20px;
        background: #f8f9fa;
        @include border-radius(3px);
        display: inline-block;
        position: relative;
        background-image: url('../images/placeholder-icon.svg');
        background-repeat: no-repeat;
        background-position: center;
        margin-bottom: 20px;
        float: left;

        &.draggable {
            cursor: pointer;
        }

        img.preview {
            width: 100%;
            height: 100%;
        }

        input {
            display: none;
        }

        .remove-image {
            background-image: linear-gradient(
                -180deg,
                rgba(0, 0, 0, 0.08) 0%,
                rgba(0, 0, 0, 0.24) 100%
            );
            @include border-radius(0 0 4px 4px);
            position: absolute;
            bottom: 0;
            width: 100%;
            padding: 10px;
            text-align: center;
            color: $white-color;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.24);
            margin-right: 20px;
            cursor: pointer;
        }

        &:hover .remove-image {
            display: block;
        }

        &.has-image {
            background-image: none;
        }
    }
}

.vue-swatches__trigger {
    border: 1px solid #d3d3d3;
}

.helper-container {
    display: block;

    .group {
        code {
            font-weight: bold;
        }
    }
}

.overlay-loader {
    position: fixed;
    z-index: 10000;
    top: 50%;
    left: 50%;
    margin-top: -24px;
    margin-left: -24px;
}

.tooltip {
    display: block !important;
    z-index: 10000;

    .tooltip-inner {
        background: black;
        color: white;
        border-radius: 4px;
        padding: 5px 10px 4px;
    }

    .tooltip-arrow {
        width: 0;
        height: 0;
        border-style: solid;
        position: absolute;
        margin: 5px;
        border-color: black;
        z-index: 1;
    }

    &[x-placement^='top'] {
        margin-bottom: 5px;

        .tooltip-arrow {
            border-width: 5px 5px 0 5px;
            border-left-color: transparent !important;
            border-right-color: transparent !important;
            border-bottom-color: transparent !important;
            bottom: -5px;
            left: calc(50% - 5px);
            margin-top: 0;
            margin-bottom: 0;
        }
    }

    &[x-placement^='bottom'] {
        margin-top: 5px;

        .tooltip-arrow {
            border-width: 0 5px 5px 5px;
            border-left-color: transparent !important;
            border-right-color: transparent !important;
            border-top-color: transparent !important;
            top: -5px;
            left: calc(50% - 5px);
            margin-top: 0;
            margin-bottom: 0;
        }
    }

    &[x-placement^='right'] {
        margin-left: 5px;

        .tooltip-arrow {
            border-width: 5px 5px 5px 0;
            border-left-color: transparent !important;
            border-top-color: transparent !important;
            border-bottom-color: transparent !important;
            left: -5px;
            top: calc(50% - 5px);
            margin-left: 0;
            margin-right: 0;
        }
    }

    &[x-placement^='left'] {
        margin-right: 5px;

        .tooltip-arrow {
            border-width: 5px 0 5px 5px;
            border-top-color: transparent !important;
            border-right-color: transparent !important;
            border-bottom-color: transparent !important;
            right: -5px;
            top: calc(50% - 5px);
            margin-left: 0;
            margin-right: 0;
        }
    }
}

.start-date::after,
.end-date::after {
    background-image: url(../images/Icon-Calendar.svg);
    width: 24px;
    height: 24px;
    content: '';
    display: inline-block;
    vertical-align: middle;
    margin-left: -34px;
    pointer-events: none;
    position: absolute;
    top: 43%;
}

@media only screen and (max-width: 414px) {
    .notification {
        width: 414px;
    }
}

@media only screen and (max-width: 411px) {
    .notification {
        width: 410px;
    }
}

@media only screen and (max-width: 393px) {
    .notification {
        width: 393px;
    }
}

@media only screen and (max-width: 390px) {
    .notification {
        width: 390px;
    }
}

@media only screen and (max-width: 375px) {
    .notification {
        width: 375px;
    }
}

@media only screen and (max-width: 360px) {
    .notification {
        width: 360px;
    }
}

@media only screen and (max-width: 320px) {
    .notification {
        width: 320px;
    }
}
