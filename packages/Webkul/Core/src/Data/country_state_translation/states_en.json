[{"country_state_id": 1, "default_name": "Alabama"}, {"country_state_id": 2, "default_name": "Alaska"}, {"country_state_id": 3, "default_name": "American Samoa"}, {"country_state_id": 4, "default_name": "Arizona"}, {"country_state_id": 5, "default_name": "Arkansas"}, {"country_state_id": 6, "default_name": "Armed Forces Africa"}, {"country_state_id": 7, "default_name": "Armed Forces Americas"}, {"country_state_id": 8, "default_name": "Armed Forces Canada"}, {"country_state_id": 9, "default_name": "Armed Forces Europe"}, {"country_state_id": 10, "default_name": "Armed Forces Middle East"}, {"country_state_id": 11, "default_name": "Armed Forces Pacific"}, {"country_state_id": 12, "default_name": "California"}, {"country_state_id": 13, "default_name": "Colorado"}, {"country_state_id": 14, "default_name": "Connecticut"}, {"country_state_id": 15, "default_name": "Delaware"}, {"country_state_id": 16, "default_name": "District of Columbia"}, {"country_state_id": 17, "default_name": "Federated States Of Micronesia"}, {"country_state_id": 18, "default_name": "Florida"}, {"country_state_id": 19, "default_name": "Georgia"}, {"country_state_id": 20, "default_name": "Guam"}, {"country_state_id": 21, "default_name": "Hawaii"}, {"country_state_id": 22, "default_name": "Idaho"}, {"country_state_id": 23, "default_name": "Illinois"}, {"country_state_id": 24, "default_name": "Indiana"}, {"country_state_id": 25, "default_name": "Iowa"}, {"country_state_id": 26, "default_name": "Kansas"}, {"country_state_id": 27, "default_name": "Kentucky"}, {"country_state_id": 28, "default_name": "Louisiana"}, {"country_state_id": 29, "default_name": "Maine"}, {"country_state_id": 30, "default_name": "Marshall Islands"}, {"country_state_id": 31, "default_name": "Maryland"}, {"country_state_id": 32, "default_name": "Massachusetts"}, {"country_state_id": 33, "default_name": "Michigan"}, {"country_state_id": 34, "default_name": "Minnesota"}, {"country_state_id": 35, "default_name": "Mississippi"}, {"country_state_id": 36, "default_name": "Missouri"}, {"country_state_id": 37, "default_name": "Montana"}, {"country_state_id": 38, "default_name": "Nebraska"}, {"country_state_id": 39, "default_name": "Nevada"}, {"country_state_id": 40, "default_name": "New Hampshire"}, {"country_state_id": 41, "default_name": "New Jersey"}, {"country_state_id": 42, "default_name": "New Mexico"}, {"country_state_id": 43, "default_name": "New York"}, {"country_state_id": 44, "default_name": "North Carolina"}, {"country_state_id": 45, "default_name": "North Dakota"}, {"country_state_id": 46, "default_name": "Northern Mariana Islands"}, {"country_state_id": 47, "default_name": "Ohio"}, {"country_state_id": 48, "default_name": "Oklahoma"}, {"country_state_id": 49, "default_name": "Oregon"}, {"country_state_id": 50, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 51, "default_name": "Pennsylvania"}, {"country_state_id": 52, "default_name": "Puerto Rico"}, {"country_state_id": 53, "default_name": "Rhode Island"}, {"country_state_id": 54, "default_name": "South Carolina"}, {"country_state_id": 55, "default_name": "South Dakota"}, {"country_state_id": 56, "default_name": "Tennessee"}, {"country_state_id": 57, "default_name": "Texas"}, {"country_state_id": 58, "default_name": "Utah"}, {"country_state_id": 59, "default_name": "Vermont"}, {"country_state_id": 60, "default_name": "Virgin Islands"}, {"country_state_id": 61, "default_name": "Virginia"}, {"country_state_id": 62, "default_name": "Washington"}, {"country_state_id": 63, "default_name": "West Virginia"}, {"country_state_id": 64, "default_name": "Wisconsin"}, {"country_state_id": 65, "default_name": "Wyoming"}, {"country_state_id": 66, "default_name": "Alberta"}, {"country_state_id": 67, "default_name": "British Columbia"}, {"country_state_id": 68, "default_name": "Manitoba"}, {"country_state_id": 69, "default_name": "Newfoundland and Labrador"}, {"country_state_id": 70, "default_name": "New Brunswick"}, {"country_state_id": 71, "default_name": "Nova Scotia"}, {"country_state_id": 72, "default_name": "Northwest Territories"}, {"country_state_id": 73, "default_name": "Nunavut"}, {"country_state_id": 74, "default_name": "Ontario"}, {"country_state_id": 75, "default_name": "Prince Edward Island"}, {"country_state_id": 76, "default_name": "Quebec"}, {"country_state_id": 77, "default_name": "Saskatchewan"}, {"country_state_id": 78, "default_name": "Yukon Territory"}, {"country_state_id": 79, "default_name": "Niedersachsen"}, {"country_state_id": 80, "default_name": "Baden-Württemberg"}, {"country_state_id": 81, "default_name": "Bayern"}, {"country_state_id": 82, "default_name": "Berlin"}, {"country_state_id": 83, "default_name": "Brandenburg"}, {"country_state_id": 84, "default_name": "Bremen"}, {"country_state_id": 85, "default_name": "Hamburg"}, {"country_state_id": 86, "default_name": "Hessen"}, {"country_state_id": 87, "default_name": "Mecklenburg-Vorpommern"}, {"country_state_id": 88, "default_name": "Nordrhein-Westfalen"}, {"country_state_id": 89, "default_name": "Rheinland-Pfalz"}, {"country_state_id": 90, "default_name": "Saarland"}, {"country_state_id": 91, "default_name": "Sachsen"}, {"country_state_id": 92, "default_name": "Sachsen-Anhalt"}, {"country_state_id": 93, "default_name": "Schleswig-Holstein"}, {"country_state_id": 94, "default_name": "T<PERSON><PERSON><PERSON>en"}, {"country_state_id": 95, "default_name": "Wien"}, {"country_state_id": 96, "default_name": "Niederösterreich"}, {"country_state_id": 97, "default_name": "Oberösterreich"}, {"country_state_id": 98, "default_name": "Salzburg"}, {"country_state_id": 99, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 100, "default_name": "Steiermark"}, {"country_state_id": 101, "default_name": "Tirol"}, {"country_state_id": 102, "default_name": "Burgenland"}, {"country_state_id": 103, "default_name": "Vorarlberg"}, {"country_state_id": 104, "default_name": "Aargau"}, {"country_state_id": 105, "default_name": "Appenzell Innerrhoden"}, {"country_state_id": 106, "default_name": "<PERSON><PERSON><PERSON><PERSON> Ausserrhoden"}, {"country_state_id": 107, "default_name": "Bern"}, {"country_state_id": 108, "default_name": "Basel-Landschaft"}, {"country_state_id": 109, "default_name": "Basel-Stadt"}, {"country_state_id": 110, "default_name": "Freiburg"}, {"country_state_id": 111, "default_name": "Genf"}, {"country_state_id": 112, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 113, "default_name": "Graubünden"}, {"country_state_id": 114, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 115, "default_name": "Luzern"}, {"country_state_id": 116, "default_name": "Neuenburg"}, {"country_state_id": 117, "default_name": "Nidwalden"}, {"country_state_id": 118, "default_name": "Obwalden"}, {"country_state_id": 119, "default_name": "St. Gallen"}, {"country_state_id": 120, "default_name": "Sc<PERSON>ffhausen"}, {"country_state_id": 121, "default_name": "Solothurn"}, {"country_state_id": 122, "default_name": "Schwyz"}, {"country_state_id": 123, "default_name": "Thurgau"}, {"country_state_id": 124, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 125, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 126, "default_name": "Waadt"}, {"country_state_id": 127, "default_name": "<PERSON>"}, {"country_state_id": 128, "default_name": "<PERSON>ug"}, {"country_state_id": 129, "default_name": "Zürich"}, {"country_state_id": 130, "default_name": "La Coruña"}, {"country_state_id": 131, "default_name": "Álava"}, {"country_state_id": 132, "default_name": "Albacete"}, {"country_state_id": 133, "default_name": "Alicante"}, {"country_state_id": 134, "default_name": "Almería"}, {"country_state_id": 135, "default_name": "Asturias"}, {"country_state_id": 136, "default_name": "Ávila"}, {"country_state_id": 137, "default_name": "Badajoz"}, {"country_state_id": 138, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 139, "default_name": "Barcelona"}, {"country_state_id": 140, "default_name": "Burgos"}, {"country_state_id": 141, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 142, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 143, "default_name": "Cantabria"}, {"country_state_id": 144, "default_name": "Castellón"}, {"country_state_id": 145, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 146, "default_name": "Ciudad Real"}, {"country_state_id": 147, "default_name": "Córdoba"}, {"country_state_id": 148, "default_name": "Cuenca"}, {"country_state_id": 149, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 150, "default_name": "Granada"}, {"country_state_id": 151, "default_name": "Guadalajara"}, {"country_state_id": 152, "default_name": "Guipúzcoa"}, {"country_state_id": 153, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 154, "default_name": "Huesca"}, {"country_state_id": 155, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 156, "default_name": "La Rioja"}, {"country_state_id": 157, "default_name": "Las Palmas"}, {"country_state_id": 158, "default_name": "León"}, {"country_state_id": 159, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 160, "default_name": "Lugo"}, {"country_state_id": 161, "default_name": "Madrid"}, {"country_state_id": 162, "default_name": "Málaga"}, {"country_state_id": 163, "default_name": "Melilla"}, {"country_state_id": 164, "default_name": "Murcia"}, {"country_state_id": 165, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 166, "default_name": "Orense"}, {"country_state_id": 167, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 168, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 169, "default_name": "Salamanca"}, {"country_state_id": 170, "default_name": "Santa Cruz de Tenerife"}, {"country_state_id": 171, "default_name": "Segovia"}, {"country_state_id": 172, "default_name": "Sevilla"}, {"country_state_id": 173, "default_name": "Soria"}, {"country_state_id": 174, "default_name": "Tarragona"}, {"country_state_id": 175, "default_name": "Teruel"}, {"country_state_id": 176, "default_name": "Toledo"}, {"country_state_id": 177, "default_name": "Valencia"}, {"country_state_id": 178, "default_name": "Valladoli<PERSON>"}, {"country_state_id": 179, "default_name": "Vizcaya"}, {"country_state_id": 180, "default_name": "Zamora"}, {"country_state_id": 181, "default_name": "Zaragoza"}, {"country_state_id": 182, "default_name": "Ain"}, {"country_state_id": 183, "default_name": "Aisne"}, {"country_state_id": 184, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 185, "default_name": "Alpes-de-Haute-Provence"}, {"country_state_id": 186, "default_name": "Hautes-Alpes"}, {"country_state_id": 187, "default_name": "Alpes-Maritimes"}, {"country_state_id": 188, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 189, "default_name": "Ardennes"}, {"country_state_id": 190, "default_name": "Ariège"}, {"country_state_id": 191, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 192, "default_name": "Aude"}, {"country_state_id": 193, "default_name": "Aveyr<PERSON>"}, {"country_state_id": 194, "default_name": "Bouches-du-Rhône"}, {"country_state_id": 195, "default_name": "Calvados"}, {"country_state_id": 196, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 197, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 198, "default_name": "Charente-Maritime"}, {"country_state_id": 199, "default_name": "Cher"}, {"country_state_id": 200, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 201, "default_name": "Corse-du-Sud"}, {"country_state_id": 202, "default_name": "Haute-Corse"}, {"country_state_id": 203, "default_name": "Côte-d'Or"}, {"country_state_id": 204, "default_name": "Côtes-d'Armor"}, {"country_state_id": 205, "default_name": "Creuse"}, {"country_state_id": 206, "default_name": "Dordogne"}, {"country_state_id": 207, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 208, "default_name": "Dr<PERSON>"}, {"country_state_id": 209, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 210, "default_name": "Eure-et-Loir"}, {"country_state_id": 211, "default_name": "Finistère"}, {"country_state_id": 212, "default_name": "Gard"}, {"country_state_id": 213, "default_name": "Haute-Garonne"}, {"country_state_id": 214, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 215, "default_name": "Gironde"}, {"country_state_id": 216, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 217, "default_name": "Ille-et-Vilaine"}, {"country_state_id": 218, "default_name": "Indre"}, {"country_state_id": 219, "default_name": "Indre-et-Loire"}, {"country_state_id": 220, "default_name": "Isère"}, {"country_state_id": 221, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 222, "default_name": "Landes"}, {"country_state_id": 223, "default_name": "Loir-et-Cher"}, {"country_state_id": 224, "default_name": "Loire"}, {"country_state_id": 225, "default_name": "Haute-Loire"}, {"country_state_id": 226, "default_name": "Loire-Atlantique"}, {"country_state_id": 227, "default_name": "Loiret"}, {"country_state_id": 228, "default_name": "Lot"}, {"country_state_id": 229, "default_name": "Lot-et-Garonne"}, {"country_state_id": 230, "default_name": "Lozère"}, {"country_state_id": 231, "default_name": "Maine-et-Loire"}, {"country_state_id": 232, "default_name": "Manche"}, {"country_state_id": 233, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 234, "default_name": "Haute-Marne"}, {"country_state_id": 235, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 236, "default_name": "Meurthe-et-Moselle"}, {"country_state_id": 237, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 238, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 239, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 240, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 241, "default_name": "Nord"}, {"country_state_id": 242, "default_name": "Oise"}, {"country_state_id": 243, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 244, "default_name": "Pas-de-Calais"}, {"country_state_id": 245, "default_name": "Puy-de-Dôme"}, {"country_state_id": 246, "default_name": "Pyrénées-Atlantiques"}, {"country_state_id": 247, "default_name": "Hautes-Pyrénées"}, {"country_state_id": 248, "default_name": "Pyrénées-Orientales"}, {"country_state_id": 249, "default_name": "Bas-Rhin"}, {"country_state_id": 250, "default_name": "Haut-Rhin"}, {"country_state_id": 251, "default_name": "Rhône"}, {"country_state_id": 252, "default_name": "Haute-Saône"}, {"country_state_id": 253, "default_name": "Saône-et-Loire"}, {"country_state_id": 254, "default_name": "Sarthe"}, {"country_state_id": 255, "default_name": "Savoie"}, {"country_state_id": 256, "default_name": "Haute-Savoie"}, {"country_state_id": 257, "default_name": "Paris"}, {"country_state_id": 258, "default_name": "Seine-Maritime"}, {"country_state_id": 259, "default_name": "Seine-et-Marne"}, {"country_state_id": 260, "default_name": "Yvelines"}, {"country_state_id": 261, "default_name": "Deux-Sèvres"}, {"country_state_id": 262, "default_name": "Somme"}, {"country_state_id": 263, "default_name": "Tarn"}, {"country_state_id": 264, "default_name": "Tarn-et-Garonne"}, {"country_state_id": 265, "default_name": "Var"}, {"country_state_id": 266, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 267, "default_name": "Vendée"}, {"country_state_id": 268, "default_name": "Vienne"}, {"country_state_id": 269, "default_name": "Haute-Vienne"}, {"country_state_id": 270, "default_name": "Vosges"}, {"country_state_id": 271, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 272, "default_name": "Territoire-de-Belfort"}, {"country_state_id": 273, "default_name": "Essonne"}, {"country_state_id": 274, "default_name": "Hauts-de-Seine"}, {"country_state_id": 275, "default_name": "Seine-Saint-Denis"}, {"country_state_id": 276, "default_name": "Val-de-Marne"}, {"country_state_id": 277, "default_name": "Val-d'Oise"}, {"country_state_id": 278, "default_name": "Alba"}, {"country_state_id": 279, "default_name": "Arad"}, {"country_state_id": 280, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 281, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 282, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 283, "default_name": "Bistriţa-Năsăud"}, {"country_state_id": 284, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 285, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 286, "default_name": "Brăila"}, {"country_state_id": 287, "default_name": "Bucureşti"}, {"country_state_id": 288, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 289, "default_name": "Caraş-<PERSON><PERSON><PERSON>"}, {"country_state_id": 290, "default_name": "Călăraşi"}, {"country_state_id": 291, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 292, "default_name": "Con<PERSON>ţ<PERSON>"}, {"country_state_id": 293, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 294, "default_name": "Dâmboviţa"}, {"country_state_id": 295, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 296, "default_name": "Galaţi"}, {"country_state_id": 297, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 298, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 299, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 300, "default_name": "Hunedoara"}, {"country_state_id": 301, "default_name": "<PERSON>alo<PERSON>ţ<PERSON>"}, {"country_state_id": 302, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 303, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 304, "default_name": "Maramureş"}, {"country_state_id": 305, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 306, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 307, "default_name": "Neamţ"}, {"country_state_id": 308, "default_name": "Olt"}, {"country_state_id": 309, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 310, "default_name": "Satu-Mare"}, {"country_state_id": 311, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 312, "default_name": "Sibiu"}, {"country_state_id": 313, "default_name": "Su<PERSON>va"}, {"country_state_id": 314, "default_name": "Teleorman"}, {"country_state_id": 315, "default_name": "<PERSON><PERSON>ş"}, {"country_state_id": 316, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 317, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 318, "default_name": "V<PERSON><PERSON><PERSON>"}, {"country_state_id": 319, "default_name": "Vrancea"}, {"country_state_id": 320, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 321, "default_name": "Pohjois-<PERSON><PERSON><PERSON>ma<PERSON>"}, {"country_state_id": 322, "default_name": "Kai<PERSON><PERSON>"}, {"country_state_id": 323, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 324, "default_name": "Pohjois-Savo"}, {"country_state_id": 325, "default_name": "Etelä-Savo"}, {"country_state_id": 326, "default_name": "Etelä-Pohjanmaa"}, {"country_state_id": 327, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 328, "default_name": "Pirkanmaa"}, {"country_state_id": 329, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 330, "default_name": "Keski-Pohjanmaa"}, {"country_state_id": 331, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 332, "default_name": "Varsina<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 333, "default_name": "Etelä-<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 334, "default_name": "Päijät-Häme"}, {"country_state_id": 335, "default_name": "Kanta-Häme"}, {"country_state_id": 336, "default_name": "Uusimaa"}, {"country_state_id": 337, "default_name": "Itä-Uusimaa"}, {"country_state_id": 338, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 339, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 340, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 341, "default_name": "Hiiumaa"}, {"country_state_id": 342, "default_name": "country_state_ida-<PERSON>irumaa"}, {"country_state_id": 343, "default_name": "Jõgevamaa"}, {"country_state_id": 344, "default_name": "Järvamaa"}, {"country_state_id": 345, "default_name": "Läänemaa"}, {"country_state_id": 346, "default_name": "Lääne-Virumaa"}, {"country_state_id": 347, "default_name": "Põlvamaa"}, {"country_state_id": 348, "default_name": "P<PERSON><PERSON>uma<PERSON>"}, {"country_state_id": 349, "default_name": "Raplamaa"}, {"country_state_id": 350, "default_name": "Saarema<PERSON>"}, {"country_state_id": 351, "default_name": "Tartumaa"}, {"country_state_id": 352, "default_name": "Valgamaa"}, {"country_state_id": 353, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 354, "default_name": "Võrumaa"}, {"country_state_id": 355, "default_name": "Daugavpils"}, {"country_state_id": 356, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 357, "default_name": "Jēkabpils"}, {"country_state_id": 358, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 359, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 360, "default_name": "Liepājas novads"}, {"country_state_id": 361, "default_name": "Rē<PERSON>kne"}, {"country_state_id": 362, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 363, "default_name": "Rīgas novads"}, {"country_state_id": 364, "default_name": "Valmiera"}, {"country_state_id": 365, "default_name": "Ventspils"}, {"country_state_id": 366, "default_name": "Aglonas novads"}, {"country_state_id": 367, "default_name": "Aizkraukles novads"}, {"country_state_id": 368, "default_name": "Aizputes novads"}, {"country_state_id": 369, "default_name": "Akn<PERSON><PERSON>s no<PERSON>"}, {"country_state_id": 370, "default_name": "<PERSON><PERSON><PERSON> no<PERSON>"}, {"country_state_id": 371, "default_name": "Alsungas novads"}, {"country_state_id": 372, "default_name": "Alūksnes novads"}, {"country_state_id": 373, "default_name": "Amatas novads"}, {"country_state_id": 374, "default_name": "Apes novads"}, {"country_state_id": 375, "default_name": "Auces novads"}, {"country_state_id": 376, "default_name": "Babītes novads"}, {"country_state_id": 377, "default_name": "Baldones novads"}, {"country_state_id": 378, "default_name": "Baltinavas novads"}, {"country_state_id": 379, "default_name": "Balvu novads"}, {"country_state_id": 380, "default_name": "Bauskas novads"}, {"country_state_id": 381, "default_name": "Beverīnas novads"}, {"country_state_id": 382, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> novads"}, {"country_state_id": 383, "default_name": "Burtnieku novads"}, {"country_state_id": 384, "default_name": "Carnikavas novads"}, {"country_state_id": 385, "default_name": "Cesvaines novads"}, {"country_state_id": 386, "default_name": "Ciblas novads"}, {"country_state_id": 387, "default_name": "<PERSON><PERSON><PERSON> novads"}, {"country_state_id": 388, "default_name": "<PERSON><PERSON><PERSON> nova<PERSON>"}, {"country_state_id": 389, "default_name": "Daugavpils novads"}, {"country_state_id": 390, "default_name": "Dobeles novads"}, {"country_state_id": 391, "default_name": "Dundagas novads"}, {"country_state_id": 392, "default_name": "Durbes novads"}, {"country_state_id": 393, "default_name": "Engures novads"}, {"country_state_id": 394, "default_name": "Garkalnes novads"}, {"country_state_id": 395, "default_name": "Grobiņas novads"}, {"country_state_id": 396, "default_name": "Gulbenes novads"}, {"country_state_id": 397, "default_name": "Iecavas novads"}, {"country_state_id": 398, "default_name": "Ikšķiles novads"}, {"country_state_id": 399, "default_name": "Ilūkstes novads"}, {"country_state_id": 400, "default_name": "Inčukalna novads"}, {"country_state_id": 401, "default_name": "Jaunjelgavas novads"}, {"country_state_id": 402, "default_name": "Jaunpiebalgas novads"}, {"country_state_id": 403, "default_name": "Jaunpils novads"}, {"country_state_id": 404, "default_name": "Jelgavas novads"}, {"country_state_id": 405, "default_name": "Jēkabpils novads"}, {"country_state_id": 406, "default_name": "Kandavas novads"}, {"country_state_id": 407, "default_name": "Kokneses novads"}, {"country_state_id": 408, "default_name": "Krimuldas novads"}, {"country_state_id": 409, "default_name": "Krustpils novads"}, {"country_state_id": 410, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>"}, {"country_state_id": 411, "default_name": "Kuldīgas novads"}, {"country_state_id": 412, "default_name": "<PERSON><PERSON><PERSON><PERSON> novads"}, {"country_state_id": 413, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> novads"}, {"country_state_id": 414, "default_name": "Limbažu novads"}, {"country_state_id": 415, "default_name": "Lubānas novads"}, {"country_state_id": 416, "default_name": "Ludzas novads"}, {"country_state_id": 417, "default_name": "Līgatnes novads"}, {"country_state_id": 418, "default_name": "<PERSON>īvānu novads"}, {"country_state_id": 419, "default_name": "Mad<PERSON>s no<PERSON>"}, {"country_state_id": 420, "default_name": "Mazsalacas novads"}, {"country_state_id": 421, "default_name": "Māl<PERSON><PERSON> novads"}, {"country_state_id": 422, "default_name": "<PERSON><PERSON><PERSON><PERSON> nova<PERSON>"}, {"country_state_id": 423, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> novads"}, {"country_state_id": 424, "default_name": "Neretas novads"}, {"country_state_id": 425, "default_name": "Nīcas novads"}, {"country_state_id": 426, "default_name": "Ogres novads"}, {"country_state_id": 427, "default_name": "<PERSON><PERSON><PERSON> novads"}, {"country_state_id": 428, "default_name": "Ozolnieku novads"}, {"country_state_id": 429, "default_name": "Preiļu novads"}, {"country_state_id": 430, "default_name": "Priekules novads"}, {"country_state_id": 431, "default_name": "Priekuļu novads"}, {"country_state_id": 432, "default_name": "Pārgaujas novads"}, {"country_state_id": 433, "default_name": "Pāvilostas novads"}, {"country_state_id": 434, "default_name": "Pļaviņu novads"}, {"country_state_id": 435, "default_name": "Raunas novads"}, {"country_state_id": 436, "default_name": "Riebiņ<PERSON> novads"}, {"country_state_id": 437, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 438, "default_name": "Ropažu novads"}, {"country_state_id": 439, "default_name": "Rucavas novads"}, {"country_state_id": 440, "default_name": "Rugāju novads"}, {"country_state_id": 441, "default_name": "<PERSON><PERSON><PERSON><PERSON> nova<PERSON>"}, {"country_state_id": 442, "default_name": "Rēzeknes novads"}, {"country_state_id": 443, "default_name": "Rūjienas novads"}, {"country_state_id": 444, "default_name": "Salacgrīvas novads"}, {"country_state_id": 445, "default_name": "Salas novads"}, {"country_state_id": 446, "default_name": "Salaspils novads"}, {"country_state_id": 447, "default_name": "Saldus novads"}, {"country_state_id": 448, "default_name": "Saulkrastu novads"}, {"country_state_id": 449, "default_name": "<PERSON><PERSON><PERSON><PERSON> novads"}, {"country_state_id": 450, "default_name": "Sk<PERSON>das novads"}, {"country_state_id": 451, "default_name": "Skrīveru novads"}, {"country_state_id": 452, "default_name": "Smiltenes novads"}, {"country_state_id": 453, "default_name": "Stopiņu novads"}, {"country_state_id": 454, "default_name": "Strenču novads"}, {"country_state_id": 455, "default_name": "Sējas novads"}, {"country_state_id": 456, "default_name": "Talsu novads"}, {"country_state_id": 457, "default_name": "Tukuma novads"}, {"country_state_id": 458, "default_name": "Tērvetes novads"}, {"country_state_id": 459, "default_name": "Vaiņodes novads"}, {"country_state_id": 460, "default_name": "Valkas novads"}, {"country_state_id": 461, "default_name": "Valmieras novads"}, {"country_state_id": 462, "default_name": "Varakļānu novads"}, {"country_state_id": 463, "default_name": "Vecpiebalgas novads"}, {"country_state_id": 464, "default_name": "Vecumnieku novads"}, {"country_state_id": 465, "default_name": "Ventspils novads"}, {"country_state_id": 466, "default_name": "Viesī<PERSON> novads"}, {"country_state_id": 467, "default_name": "Viļaka<PERSON> no<PERSON>"}, {"country_state_id": 468, "default_name": "Viļānu novads"}, {"country_state_id": 469, "default_name": "<PERSON><PERSON><PERSON><PERSON> novads"}, {"country_state_id": 470, "default_name": "Zilupes novads"}, {"country_state_id": 471, "default_name": "<PERSON><PERSON><PERSON><PERSON> nova<PERSON>"}, {"country_state_id": 472, "default_name": "Ērgļu novads"}, {"country_state_id": 473, "default_name": "Ķeguma novads"}, {"country_state_id": 474, "default_name": "Ķekavas novads"}, {"country_state_id": 475, "default_name": "Alytaus Apskritis"}, {"country_state_id": 476, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 477, "default_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Apskrit<PERSON>"}, {"country_state_id": 478, "default_name": "Marijampolė<PERSON>"}, {"country_state_id": 479, "default_name": "Panevė<PERSON><PERSON> Apskrit<PERSON>"}, {"country_state_id": 480, "default_name": "Šiaulių Apskritis"}, {"country_state_id": 481, "default_name": "Taurag<PERSON><PERSON>"}, {"country_state_id": 482, "default_name": "Telšių Apskritis"}, {"country_state_id": 483, "default_name": "Utenos <PERSON>"}, {"country_state_id": 484, "default_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_state_id": 485, "default_name": "Acre"}, {"country_state_id": 486, "default_name": "Alagoas"}, {"country_state_id": 487, "default_name": "Amapá"}, {"country_state_id": 488, "default_name": "Amazonas"}, {"country_state_id": 489, "default_name": "Bahía"}, {"country_state_id": 490, "default_name": "Ceará"}, {"country_state_id": 491, "default_name": "Espíritu Santo"}, {"country_state_id": 492, "default_name": "Goiás"}, {"country_state_id": 493, "default_name": "Maranhão"}, {"country_state_id": 494, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 495, "default_name": "Mato Grosso del Sur"}, {"country_state_id": 496, "default_name": "Minas Gerais"}, {"country_state_id": 497, "default_name": "Pará"}, {"country_state_id": 498, "default_name": "Paraíba"}, {"country_state_id": 499, "default_name": "Paraná"}, {"country_state_id": 500, "default_name": "Pernambuco"}, {"country_state_id": 501, "default_name": "Piauí"}, {"country_state_id": 502, "default_name": "Río de Janeiro"}, {"country_state_id": 503, "default_name": "Río Grande del Norte"}, {"country_state_id": 504, "default_name": "Río Grande del Sur"}, {"country_state_id": 505, "default_name": "Rondônia"}, {"country_state_id": 506, "default_name": "Roraima"}, {"country_state_id": 507, "default_name": "Santa Catarina"}, {"country_state_id": 508, "default_name": "São Paulo"}, {"country_state_id": 509, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 510, "default_name": "Tocantins"}, {"country_state_id": 511, "default_name": "Distrito Federal"}, {"country_state_id": 512, "default_name": "Zagrebačka županija"}, {"country_state_id": 513, "default_name": "Krapinsko-zagorska županija"}, {"country_state_id": 514, "default_name": "Sisačko-moslavačka županija"}, {"country_state_id": 515, "default_name": "Karlovačka županija"}, {"country_state_id": 516, "default_name": "Varaždinska županija"}, {"country_state_id": 517, "default_name": "Koprivničko-križevačka županija"}, {"country_state_id": 518, "default_name": "Bjelovarsko-bilogorska županija"}, {"country_state_id": 519, "default_name": "Primorsko-goranska županija"}, {"country_state_id": 520, "default_name": "Ličko-senjska županija"}, {"country_state_id": 521, "default_name": "Virovitičko-podravska županija"}, {"country_state_id": 522, "default_name": "Požeško-slavonska županija"}, {"country_state_id": 523, "default_name": "Brodsko-posavska županija"}, {"country_state_id": 524, "default_name": "Zadarska županija"}, {"country_state_id": 525, "default_name": "Osječko-baranjska županija"}, {"country_state_id": 526, "default_name": "Šibensko-kninska županija"}, {"country_state_id": 527, "default_name": "Vukovarsko-srijemska županija"}, {"country_state_id": 528, "default_name": "Splitsko-dalmatinska županija"}, {"country_state_id": 529, "default_name": "Istarska županija"}, {"country_state_id": 530, "default_name": "Dubrovačko-neretvanska županija"}, {"country_state_id": 531, "default_name": "Međimurska županija"}, {"country_state_id": 532, "default_name": "Grad Zagreb"}, {"country_state_id": 533, "default_name": "Andaman and Nicobar Islands"}, {"country_state_id": 534, "default_name": "Andhra Pradesh"}, {"country_state_id": 535, "default_name": "Arunachal Pradesh"}, {"country_state_id": 536, "default_name": "Assam"}, {"country_state_id": 537, "default_name": "Bihar"}, {"country_state_id": 538, "default_name": "Chandigarh"}, {"country_state_id": 539, "default_name": "Chhattisgarh"}, {"country_state_id": 540, "default_name": "Dadra and Nagar Haveli"}, {"country_state_id": 541, "default_name": "<PERSON><PERSON> and <PERSON><PERSON>"}, {"country_state_id": 542, "default_name": "Delhi"}, {"country_state_id": 543, "default_name": "Goa"}, {"country_state_id": 544, "default_name": "Gujarat"}, {"country_state_id": 545, "default_name": "Haryana"}, {"country_state_id": 546, "default_name": "Himachal Pradesh"}, {"country_state_id": 547, "default_name": "Jammu and Kashmir"}, {"country_state_id": 548, "default_name": "Jharkhand"}, {"country_state_id": 549, "default_name": "Karnataka"}, {"country_state_id": 550, "default_name": "Kerala"}, {"country_state_id": 551, "default_name": "Lakshadweep"}, {"country_state_id": 552, "default_name": "Madhya Pradesh"}, {"country_state_id": 553, "default_name": "Maharashtra"}, {"country_state_id": 554, "default_name": "Manipur"}, {"country_state_id": 555, "default_name": "<PERSON><PERSON><PERSON>"}, {"country_state_id": 556, "default_name": "Mizoram"}, {"country_state_id": 557, "default_name": "Nagaland"}, {"country_state_id": 558, "default_name": "Odisha"}, {"country_state_id": 559, "default_name": "Puducherry"}, {"country_state_id": 560, "default_name": "Punjab"}, {"country_state_id": 561, "default_name": "Rajasthan"}, {"country_state_id": 562, "default_name": "Sikkim"}, {"country_state_id": 563, "default_name": "Tamil Nadu"}, {"country_state_id": 564, "default_name": "Telangana"}, {"country_state_id": 565, "default_name": "<PERSON>ura"}, {"country_state_id": 566, "default_name": "Uttar Pradesh"}, {"country_state_id": 567, "default_name": "Uttarakhand"}, {"country_state_id": 568, "default_name": "West Bengal"}, {"country_state_id": 569, "default_name": "Alto Paraguay"}, {"country_state_id": 570, "default_name": "Alto Paraná"}, {"country_state_id": 571, "default_name": "Amam<PERSON>"}, {"country_state_id": 572, "default_name": "Asunción"}, {"country_state_id": 573, "default_name": "Boquerón"}, {"country_state_id": 574, "default_name": "Caaguazú"}, {"country_state_id": 575, "default_name": "Caazapá"}, {"country_state_id": 576, "default_name": "Canindeyú"}, {"country_state_id": 577, "default_name": "Central"}, {"country_state_id": 578, "default_name": "Concepción"}, {"country_state_id": 579, "default_name": "Cordillera"}, {"country_state_id": 580, "default_name": "Guairá"}, {"country_state_id": 581, "default_name": "Itapúa"}, {"country_state_id": 582, "default_name": "Misiones"}, {"country_state_id": 583, "default_name": "Paraguarí"}, {"country_state_id": 584, "default_name": "<PERSON><PERSON>"}, {"country_state_id": 585, "default_name": "San Pedro"}, {"country_state_id": 586, "default_name": "Ñeembucú"}]