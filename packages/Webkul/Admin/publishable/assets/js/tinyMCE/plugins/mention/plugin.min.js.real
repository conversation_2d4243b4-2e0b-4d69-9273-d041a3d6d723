!function(a){"use strict";if("object"==typeof exports&&"undefined"!=typeof module)module.exports=a(require("jquery"));else if("function"==typeof define&&define.amd)define(["jquery"],a);else{var b;b="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,a(b.jQuery)}}(function(a){"use strict";var b=function(b,c){this.editor=b,this.options=a.extend({},{source:[],delay:500,queryBy:"name",items:10},c),this.matcher=this.options.matcher||this.matcher,this.renderDropdown=this.options.renderDropdown||this.renderDropdown,this.render=this.options.render||this.render,this.insert=this.options.insert||this.insert,this.highlighter=this.options.highlighter||this.highlighter,this.query="",this.hasFocus=!0,this.renderInput(),this.bindEvents()};b.prototype={constructor:b,renderInput:function(){var a='<span id="autocomplete"><span id="autocomplete-delimiter">'+this.options.delimiter+'</span><span id="autocomplete-searchtext"><span class="dummy">\ufeff</span></span></span>';this.editor.execCommand("mceInsertContent",!1,a),this.editor.focus(),this.editor.selection.select(this.editor.selection.dom.select("span#autocomplete-searchtext span")[0]),this.editor.selection.collapse(0)},bindEvents:function(){this.editor.on("keyup",this.editorKeyUpProxy=a.proxy(this.rteKeyUp,this)),this.editor.on("keydown",this.editorKeyDownProxy=a.proxy(this.rteKeyDown,this),!0),this.editor.on("click",this.editorClickProxy=a.proxy(this.rteClicked,this)),a("body").on("click",this.bodyClickProxy=a.proxy(this.rteLostFocus,this)),a(this.editor.getWin()).on("scroll",this.rteScroll=a.proxy(function(){this.cleanUp(!0)},this))},unbindEvents:function(){this.editor.off("keyup",this.editorKeyUpProxy),this.editor.off("keydown",this.editorKeyDownProxy),this.editor.off("click",this.editorClickProxy),a("body").off("click",this.bodyClickProxy),a(this.editor.getWin()).off("scroll",this.rteScroll)},rteKeyUp:function(a){switch(a.which||a.keyCode){case 40:case 38:case 16:case 17:case 18:break;case 8:""===this.query?this.cleanUp(!0):this.lookup();break;case 9:case 13:var b=void 0!==this.$dropdown?this.$dropdown.find("li.active"):[];b.length?(this.select(b.data()),this.cleanUp(!1)):this.cleanUp(!0);break;case 27:this.cleanUp(!0);break;default:this.lookup()}},rteKeyDown:function(a){switch(a.which||a.keyCode){case 9:case 13:case 27:a.preventDefault();break;case 38:a.preventDefault(),void 0!==this.$dropdown&&this.highlightPreviousResult();break;case 40:a.preventDefault(),void 0!==this.$dropdown&&this.highlightNextResult()}a.stopPropagation()},rteClicked:function(b){var c=a(b.target);this.hasFocus&&"autocomplete-searchtext"!==c.parent().attr("id")&&this.cleanUp(!0)},rteLostFocus:function(){this.hasFocus&&this.cleanUp(!0)},lookup:function(){this.query=a.trim(a(this.editor.getBody()).find("#autocomplete-searchtext").text()).replace("\ufeff",""),void 0===this.$dropdown&&this.show(),clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout(a.proxy(function(){var b=a.isFunction(this.options.source)?this.options.source(this.query,a.proxy(this.process,this),this.options.delimiter):this.options.source;b&&this.process(b)},this),this.options.delay)},matcher:function(a){return~a[this.options.queryBy].toLowerCase().indexOf(this.query.toLowerCase())},sorter:function(a){for(var e,b=[],c=[],d=[];void 0!==(e=a.shift());)e[this.options.queryBy].toLowerCase().indexOf(this.query.toLowerCase())?~e[this.options.queryBy].indexOf(this.query)?c.push(e):d.push(e):b.push(e);return b.concat(c,d)},highlighter:function(a){return a.replace(new RegExp("("+this.query.replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1")+")","ig"),function(a,b){return"<strong>"+b+"</strong>"})},show:function(){var b=this.editor.inline?this.offsetInline():this.offset();this.$dropdown=a(this.renderDropdown()).css({top:b.top,left:b.left}),a("body").append(this.$dropdown),this.$dropdown.on("click",a.proxy(this.autoCompleteClick,this))},process:function(b){if(this.hasFocus){var c=this,d=[],e=a.grep(b,function(a){return c.matcher(a)});e=c.sorter(e),e=e.slice(0,this.options.items),a.each(e,function(b,f){var g=a(c.render(f));g.html(g.html().replace(g.text(),c.highlighter(g.text()))),a.each(e[b],function(a,b){g.attr("data-"+a,b)}),d.push(g[0].outerHTML)}),d.length?this.$dropdown.html(d.join("")).show():this.$dropdown.hide()}},renderDropdown:function(){return'<ul class="rte-autocomplete dropdown-menu"><li class="loading"></li></ul>'},render:function(a){return'<li><a href="javascript:;"><span>'+a[this.options.queryBy]+"</span></a></li>"},autoCompleteClick:function(b){var c=a(b.target).closest("li").data();a.isEmptyObject(c)||(this.select(c),this.cleanUp(!1)),b.stopPropagation(),b.preventDefault()},highlightPreviousResult:function(){var a=this.$dropdown.find("li.active").index(),b=0===a?this.$dropdown.find("li").length-1:--a;this.$dropdown.find("li").removeClass("active").eq(b).addClass("active")},highlightNextResult:function(){var a=this.$dropdown.find("li.active").index(),b=a===this.$dropdown.find("li").length-1?0:++a;this.$dropdown.find("li").removeClass("active").eq(b).addClass("active")},select:function(a){this.editor.focus();var b=this.editor.dom.select("span#autocomplete")[0];this.editor.dom.remove(b),this.editor.execCommand("mceInsertContent",!1,this.insert(a))},insert:function(a){return"<span>"+a[this.options.queryBy]+"</span>&nbsp;"},cleanUp:function(b){if(this.unbindEvents(),this.hasFocus=!1,void 0!==this.$dropdown&&(this.$dropdown.remove(),delete this.$dropdown),b){var c=this.query,d=a(this.editor.dom.select("span#autocomplete")),e=a("<p>"+this.options.delimiter+c+"</p>")[0].firstChild,f=a(this.editor.selection.getNode()).offset().top===d.offset().top+(d.outerHeight()-d.height())/2;this.editor.dom.replace(e,d[0]),f&&(this.editor.selection.select(e),this.editor.selection.collapse())}},offset:function(){var b=a(this.editor.getContainer()).offset(),c=a(this.editor.getContentAreaContainer()).position(),d=a(this.editor.dom.select("span#autocomplete")).position();return{top:b.top+c.top+d.top+a(this.editor.selection.getNode()).innerHeight()-a(this.editor.getDoc()).scrollTop()+5,left:b.left+c.left+d.left}},offsetInline:function(){var b=a(this.editor.dom.select("span#autocomplete")).offset();return{top:b.top+a(this.editor.selection.getNode()).innerHeight()+5,left:b.left}}},tinymce.create("tinymce.plugins.Mention",{init:function(c){function f(){var b=c.selection.getRng(!0).startOffset,d=c.selection.getRng(!0).startContainer.data||"",e=d.substr(b-1,1);return a.trim(e).length?!1:!0}var d,e=c.getParam("mentions");e.delimiter=void 0!==e.delimiter?a.isArray(e.delimiter)?e.delimiter:[e.delimiter]:["@"],c.on("keypress",function(g){var h=a.inArray(String.fromCharCode(g.which||g.keyCode),e.delimiter);h>-1&&f()&&(void 0===d||void 0!==d.hasFocus&&!d.hasFocus)&&(g.preventDefault(),d=new b(c,a.extend({},e,{delimiter:e.delimiter[h]})))})},getInfo:function(){return{longname:"mention",author:"Steven Devooght",version:tinymce.majorVersion+"."+tinymce.minorVersion}}}),tinymce.PluginManager.add("mention",tinymce.plugins.Mention)});