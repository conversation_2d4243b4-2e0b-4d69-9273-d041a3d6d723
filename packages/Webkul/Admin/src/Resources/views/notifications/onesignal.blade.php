@extends('admin::layouts.content')

@section('page_title')
    {{ __( 'admin::app.push-notification.push-notification-title' ) }}
@endsection

@php
    /**
     * Bu array tablo filtre işlemi için kullanılıyor.
     */
    $orderStatus = [
        'all'        => trans('admin::app.notification.status.all'),
        'closed'     => trans('admin::app.notification.status.closed'),
        'pending'    => trans('admin::app.notification.status.pending'),
        'canceled'   => trans('admin::app.notification.status.canceled'),
        'completed'  => trans('admin::app.notification.status.completed'),
        'processing' => trans('admin::app.notification.status.processing')
    ];
    /**
     * Bu array tablo filtre işlemi için kullanılıyor.
     */
    $orderStatusMessages = [
        'closed'     => trans('admin::app.notification.order-status-messages.closed'),
        'pending'    => trans('admin::app.notification.order-status-messages.pending'),
        'canceled'   => trans('admin::app.notification.order-status-messages.canceled'),
        'completed'  => trans('admin::app.notification.order-status-messages.completed'),
        'processing' => trans('admin::app.notification.order-status-messages.processing')
    ];
    /**
     * Bu array Mobile Bildirim işlemi için app üzerindeki sayfaların.
     */
    $appPageAndScreen = [
        [
            'label'     => 'Screen',
            'labelSlug' => 'screen',
            'option'    => [
                'Search',
                'Profile',
                'HomeScreen',
                'FundScreen',
                'WalletScreen',
            ]
        ], [
            'label'     => 'Page',
            'labelSlug' => 'page',
            'option'    => [
                'FAQ',
                'BASKET',
                'ABOUTUS',
                'COMPREF',
                'FAVORITES',
                'FUNDDETAIL',
                'ADVANTAGES',
                'MYNOTIFICATIONS',
            ]
        ]
    ];
    /**
     *
     */
    $applicationDataArray = [
        'usersTotalCount'               => $totalUsers,
        'productList'                   => $productList,
        'applicationRemovedCount'       => $applicationRemoved,
        'applicationInstalledCount'     => $applicationInstalled,
        'listRoute'                     => route( 'admin.push.notifications.list' ),
        'noRecordText'                  => __( 'admin::app.notification.no-record' ),
        'createRoute'                   => route( 'admin.push.notifications.create' ),
        'modalSchedulingDateTime'       => \Carbon\Carbon::now()->format('Y-m-d H:i'),
        'logListRoute'                  => route( 'admin.push.notifications.logs.list' ),
        'userListRoute'                 => route( 'admin.push.notifications.user.list' ),
        'tableDefautLargeIcon'          => asset( 'vendor/webkul/ui/assets/images/Camera.svg' ),
        'tableArray'                    => __( 'admin::app.push-notification.push-notification-table' ),
        'title'                         => __( 'admin::app.push-notification.push-notification-title' ),
        'logModalArray'                 => __( 'admin::app.push-notification.push-notification-log-modal' ),
        'usersTotalText'                => __( 'admin::app.push-notification.push-notification-total-user' ),
        'addedModalArray'               => __( 'admin::app.push-notification.push-notification-added-modal' ),
        'pushNotificationErrorsText'    => __( 'admin::app.push-notification.push-notification-errors-text' ),
        'addPushNotificationButtontext' => __( 'admin::app.push-notification.push-notification-add-button-text' ),
        'applicationRemovedText'        => __( 'admin::app.push-notification.push-notification-application-removed-text' ),
        'applicationInstalledText'      => __( 'admin::app.push-notification.push-notification-application-installed-text' ),
    ];
@endphp

@push('css')
    <style>
        .sr-only{
            display:none;
        }
        .pagination .page-item {
            height: 40px !important;
            width: 40px !important;
            margin-right: 5px;
            font-size: 16px;
            display: inline-block;
            color: #3a3a3a;
            vertical-align: middle;
            text-decoration: none;
            text-align: center;
        }
        .page-item .pagination-page-nav .active .page-link {
            color:#fff !important;
        }
        .badge.badge-noread{
            background-color:gray;
        }
    </style>
@endpush

@section('onesignal-content')
    <push-notification-list-component
        order-status="{{ json_encode($orderStatus) }}"
        app-page-and-screen="{{ json_encode($appPageAndScreen) }}"
        order-status-messages="{{ json_encode($orderStatusMessages) }}"
        application-data="{{ json_encode($applicationDataArray) }}"
    ></push-notification-list-component>
@endsection
