<?php

use Illuminate\Support\Facades\Route;
use Webkul\Notification\Http\Controllers\Admin\NotificationController;

/**
 * Notification routes.
 */
Route::group(['middleware' => ['web', 'admin'], 'prefix' => config('app.admin_url')], function () {
    Route::get('notifications', [NotificationController::class, 'index'])->defaults('_config', [
        'view' => 'admin::notifications.index',
    ])->name('admin.notification.index');

    Route::get('get-notifications', [NotificationController::class, 'getNotifications'])
        ->name('admin.notification.get-notification');

    Route::get('viewed-notifications/{orderId}', [NotificationController::class, 'viewedNotifications'])
        ->name('admin.notification.viewed-notification');

    Route::post('read-all-notifications', [NotificationController::class, 'readAllNotifications'])
        ->name('admin.notification.read-all');

    /** Push Notification (OneSignal) **/
    Route::get('push-notifications', [NotificationController::class, 'pushNotifications'])->defaults('_config', [
        'view' => 'admin::notifications.onesignal',
    ])->name('admin.notifications.push-notifications');

    Route::post('push-notifications-list', [NotificationController::class, 'pushNotificationsList'])
         ->name('admin.push.notifications.list');

    Route::post('push-notifications-create', [NotificationController::class, 'pushNotificationsCreated'])
         ->name('admin.push.notifications.create');

    Route::post('push-notifications-user-list', [NotificationController::class, 'pushNotificationsUsersList'])
         ->name('admin.push.notifications.user.list');

    Route::post('push-notifications-logs-list', [NotificationController::class, 'pushNotificationLogsList'])
        ->name('admin.push.notifications.logs.list');
});
