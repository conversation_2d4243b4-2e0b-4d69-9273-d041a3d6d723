<?php

namespace Webkul\Product\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Webkul\Product\Events\ProductOrderedEvent;
use Webkul\Product\Models\ProductInventory;

class IncrementOrderedStockQuantity implements ShouldQueue
{
    public function handle(ProductOrderedEvent $event)
    {
        foreach ($event->order->items()->get() as $item) {
            $inventory = ProductInventory::where('product_id', $item->product_id)->first();
            if ($inventory) {
                $inventory->update([
                    'ordered_stock_quantity' => $inventory->ordered_stock_quantity + $item->qty_ordered,
                ]);
            }
        }
    }
}
