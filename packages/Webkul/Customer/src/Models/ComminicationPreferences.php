<?php

namespace Webkul\Customer\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
class ComminicationPreferences extends Model {

    use HasFactory, Notifiable;

    protected $connection = 'mysql';
    protected $primaryKey = 'id';
    protected $table      = 'comminication_preferences';
    protected $guarded    = [];
    protected $appends    = [];

    protected $casts      = [
        'created_at'        => 'datetime:d-m-Y H:i:s',
        'updated_at'        => 'datetime:d-m-Y H:i:s',
    ];

}