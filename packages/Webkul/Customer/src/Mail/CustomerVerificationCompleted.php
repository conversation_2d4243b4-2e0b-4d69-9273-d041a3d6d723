<?php

namespace Webkul\Customer\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Webkul\Customer\Models\Customer;

class CustomerVerificationCompleted extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new mailable instance.
     *
     * @return void
     */
    public function __construct(public Customer $customer) {}

    public function build()
    {
        return $this->from(core()->getSenderEmailDetails()['email'], core()->getSenderEmailDetails()['name'])
            ->to($this->customer->email)
            ->subject(trans('shop::app.mail.customer.auth.verified.customer.subject', ['app_name' => config('app.name')]))
            ->view('shop::emails.customer.auth.identity-verified')
            ->with('data', [
                'name' => $this->customer->first_name.' '.$this->customer->last_name,
            ]);
    }
}
