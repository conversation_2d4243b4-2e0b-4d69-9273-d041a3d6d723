<?php

namespace Webkul\Attribute\Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CustomAttributeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $now = Carbon::now();

        DB::table('attributes')->insert([
            [
                'id' => '100',
                'code' => 'm2price',
                'admin_name' => 'Price per m2',
                'type' => 'pricerange',
                'validation' => null,
                'position' => '1',
                'is_required' => '0',
                'is_unique' => '0',
                'value_per_locale' => '0',
                'value_per_channel' => '0',
                'is_filterable' => '1',
                'is_configurable' => '0',
                'is_user_defined' => '0',
                'is_visible_on_front' => '0',
                'use_in_flat' => '1',
                'show_in_all_products_filter' => '1',
                'created_at' => $now,
                'updated_at' => $now,
                'is_comparable' => '0',
            ],
            [
                'id' => '101',
                'code' => 'parking',
                'admin_name' => 'Parking',
                'type' => 'boolean',
                'validation' => null,
                'position' => '2',
                'is_required' => '0',
                'is_unique' => '0',
                'value_per_locale' => '0',
                'value_per_channel' => '0',
                'is_filterable' => '1',
                'is_configurable' => '0',
                'is_user_defined' => '0',
                'is_visible_on_front' => '0',
                'use_in_flat' => '1',
                'show_in_all_products_filter' => '1',
                'created_at' => $now,
                'updated_at' => $now,
                'is_comparable' => '0',
            ],
            [
                'id' => '102',
                'code' => 'discounted',
                'admin_name' => 'Discounted Products',
                'type' => 'boolean',
                'validation' => null,
                'position' => '3',
                'is_required' => '0',
                'is_unique' => '0',
                'value_per_locale' => '0',
                'value_per_channel' => '0',
                'is_filterable' => '1',
                'is_configurable' => '0',
                'is_user_defined' => '0',
                'is_visible_on_front' => '0',
                'use_in_flat' => '1',
                'show_in_all_products_filter' => '1',
                'created_at' => $now,
                'updated_at' => $now,
                'is_comparable' => '0',
            ],
            [
                'id' => '103',
                'code' => 'new_products',
                'admin_name' => 'New Products',
                'type' => 'boolean',
                'validation' => null,
                'position' => '4',
                'is_required' => '0',
                'is_unique' => '0',
                'value_per_locale' => '0',
                'value_per_channel' => '0',
                'is_filterable' => '1',
                'is_configurable' => '0',
                'is_user_defined' => '0',
                'is_visible_on_front' => '0',
                'use_in_flat' => '1',
                'show_in_all_products_filter' => '1',
                'created_at' => $now,
                'updated_at' => $now,
                'is_comparable' => '0',
            ],
            [
                'id' => '104',
                'code' => 'room_count',
                'admin_name' => 'Room Count',
                'type' => 'boolean',
                'validation' => null,
                'position' => '5',
                'is_required' => '0',
                'is_unique' => '0',
                'value_per_locale' => '0',
                'value_per_channel' => '0',
                'is_filterable' => '1',
                'is_configurable' => '0',
                'is_user_defined' => '0',
                'is_visible_on_front' => '0',
                'use_in_flat' => '1',
                'show_in_all_products_filter' => '1',
                'created_at' => $now,
                'updated_at' => $now,
                'is_comparable' => '0',
            ],
        ]);

        DB::table('attribute_translations')->insert([
            [
                'id' => '100',
                'locale' => 'en',
                'name' => 'Price per m2',
                'attribute_id' => '100',
            ],
            [
                'id' => '101',
                'locale' => 'en',
                'name' => 'Parking',
                'attribute_id' => '101',
            ],
        ]);

        DB::table('attribute_options')->insert([
            [
                'id' => '100',
                'admin_name' => '1000-2000',
                'sort_order' => '1',
                'attribute_id' => '100',
            ], [
                'id' => '101',
                'admin_name' => '2000-3000',
                'sort_order' => '2',
                'attribute_id' => '100',
            ], [
                'id' => '102',
                'admin_name' => '3000-4000',
                'sort_order' => '3',
                'attribute_id' => '100',
            ], [
                'id' => '103',
                'admin_name' => '4000-5000',
                'sort_order' => '4',
                'attribute_id' => '100',
            ],
        ]);

        DB::table('attribute_option_translations')->insert([
            [
                'id' => '100',
                'locale' => 'en',
                'label' => '1000-2000',
                'attribute_option_id' => '100',
            ], [
                'id' => '101',
                'locale' => 'en',
                'label' => '2000-3000',
                'attribute_option_id' => '101',
            ], [
                'id' => '102',
                'locale' => 'en',
                'label' => '3000-4000',
                'attribute_option_id' => '102',
            ], [
                'id' => '103',
                'locale' => 'en',
                'label' => '4000-5000',
                'attribute_option_id' => '103',
            ],
        ]);
    }
}
