<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">

    <head>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-P8NZKRVDHZ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', 'G-P8NZKRVDHZ');
        </script>
        {{-- title --}}
        <title>@yield('page_title')</title>

        {{-- meta data --}}
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta http-equiv="content-language" content="{{ app()->getLocale() }}">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="base-url" content="{{ url()->to('/') }}">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <link rel="stylesheet" href="{{ mix('/css/app.css') }}">

        {!! view_render_event('bagisto.shop.layout.head') !!}

        {{-- for extra head data --}}
        @yield('head')

        {{-- seo meta data --}}
        @yield('seo')

        {{-- fav icon --}}
        @if ($favicon = core()->getCurrentChannel()->favicon_url)
            <link rel="icon" sizes="16x16" href="{{ $favicon }}" />
        @else
            <link rel="icon" sizes="16x16" href="{{ asset('/themes/velocity/assets/images/static/v-icon.png') }}" />
        @endif


        {{-- all styles --}}
        @include('paywithcryptoformarket::layouts.styles')
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Source+Serif+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap" rel="stylesheet">
        <style>
            .font-terramirum{
                font-family: 'Source Serif Pro', serif !important;
            }
            *{
                font-family: 'Source Serif Pro', serif !important;
            }
            /*input{*/
            /*    padding-left: 1x!important;*/
            /*}*/
            .text-2xs{
                font-size: 12px!important;
            }
        </style>
        <link rel="stylesheet" href="{{ mix('/css/app.css') }}">
        <style>
            header #search-form input{
                border: 0px !important;
            }
            #rememberMe{
                height: 28px;
                width: 28px;
                margin: 0 0 0 0;
            }
            .product-detail #product-form .form-container div.left .product-image-group{
                position: relative!important;
                top: 0!important;
            }
            .product-detail #product-form .form-container div.left{
                position: relative!important;
                top: 0!important;
            }
            .outer-assets-container .image-container .magnifier > img{
                max-height: 452px;
            }
            /*.card-img-top{*/
            /*    max-height: 159px;*/
            /*}*/
            .special-badge{
                background: rgb(121, 145, 78) !important;
                color: white!important;
                border-radius: 50% !important;
                padding: 4px!important;
                letter-spacing: -1px!important;
                bottom: 0!important;
            }
            #product-star .quantity.control-group.font-terramirum.font-bold.w-full.rounded-xl.text-base.p-0.border-0.text-center{
                opacity: 0;
                position: absolute;
                left: 0;
                bottom: 0;
            }
            .btn .badge{
                position: absolute;
                right: 0;
                top: auto;
                bottom: 5px;
            }
            input.ml5.rounded-14p{
                padding: 0 0 0 0 !important;
            }
            input #quantity-changer{
                padding: 0 0 0 0!important;
            }
            .cart-details .cart-content .product-quantity .quantity{
                top: 0;
            }
            .quantity .input-btn-group input{
                padding: 0 0 0 0!important;
                border: 0!important;
                max-width: 25px;
                height: 30px;
            }
            .quantity .input-btn-group button{
                border: 0!important;
                padding: 0px 5px;
                height: 25px;
            }
        </style>
        <style>
            .close-when-detail{
                width: 24px;
                height: 24px;
                margin-right: 0px;
            }
            @media (max-width: 720px) {
                .close-when-detail{
                    margin-right: 10px;
                }
            }
            .table-responsive{
                border-radius: 14px;
            }
            .head-bg-lightgreen{
                background-color: #E9EFEB;
            }
            th.bg-greens, td.bg-greens {
                background-color: rgba(121, 145,78, 0.45);
                text-align: center;
                vertical-align: middle;
            }
            th.bg-greens:nth-of-type(2), td.bg-greens:nth-of-type(2) {
                background-color: rgba(121, 145,78, 0.40);
            }
            th.bg-greens:nth-of-type(3), td.bg-greens:nth-of-type(3) {
                background-color: rgba(121, 145,78, 0.35);
            }
            th.bg-greens:nth-of-type(4), td.bg-greens:nth-of-type(4) {
                background-color: rgba(121, 145,78, 0.30);
            }
            th.bg-greens:nth-of-type(5), td.bg-greens:nth-of-type(5) {
                background-color: rgba(121, 145,78, 0.25);
            }
            th.bg-greens:nth-of-type(6), td.bg-greens:nth-of-type(6) {
                background-color: rgba(121, 145,78, 0.20);
            }
            th.bg-greens:nth-of-type(7), td.bg-greens:nth-of-type(7) {
                background-color: rgba(121, 145,78, 0.15);
            }
            th.bg-greens:nth-of-type(8), td.bg-greens:nth-of-type(8) {
                background-color: rgba(121, 145,78, 0.10);
            }
            .name-color b {
                color: #79914E !important;
            }

            /* Chrome, Safari, Edge, Opera */
            input::-webkit-outer-spin-button,
            input::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
            }

            /* Firefox */
            input[type=number] {
                -moz-appearance: textfield;
            }
            .alert {
                border: 2px solid transparent;
                border-radius: 20px;
                z-index: 99999;
            }
            .alert strong{
                display: block;
            }
            .alert-success{
                background-color: #ffffff;
                border-color: #437D3B;
                color: #437D3B;
            }
            .alert-warning{
                background-color: #ffffff;
                border-color: #D3801F;
                color: #D3801F;
            }
            .alert-danger{
                background-color: #ffffff;
                border-color: #D80000;
                color: #D80000;
            }
            .alert-dismissible{
                padding-right: 2rem;
            }

        </style>

        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700;900&display=swap" rel="stylesheet">
    </head>

    <body @if (core()->getCurrentLocale() && core()->getCurrentLocale()->direction === 'rtl') class="rtl" @endif>
        {!! view_render_event('bagisto.shop.layout.body.before') !!}

        {{-- main app --}}
        <div id="app">
            <product-quick-view v-if="$root.quickView"></product-quick-view>

            <div class="main-container-wrapper">

                @section('body-header')
                    {{-- top nav which contains currency, locale and login header --}}

                    {!! view_render_event('bagisto.shop.layout.header.before') !!}

                        {{-- primary header after top nav --}}
                        @include('paywithcryptoformarket::layouts.header.index')

                    {!! view_render_event('bagisto.shop.layout.header.after') !!}

                    <div class="main-content-wrapper col-12 no-padding">

                        {{-- secondary header --}}
                        {{-- <header class="row velocity-divide-page vc-header header-shadow active"> --}}

                            {{-- mobile header
                            <div class="vc-small-screen container">
                                @include('shop::layouts.header.mobile')
                            </div>--}}

                            {{-- desktop header --}}
                            {{-- @include('shop::layouts.header.desktop') --}}

                        {{-- </header> --}}

                        <div class="">
                            <div class="row col-12 remove-padding-margin">
                                <sidebar-component
                                    main-sidebar=true
                                    id="sidebar-level-0"
                                    url="{{ url()->to('/') }}"
                                    category-count="{{ $velocityMetaData ? $velocityMetaData->sidebar_category_count : 10 }}"
                                    add-class="category-list-container pt10">
                                </sidebar-component>

                                <div class="col-12 no-padding content" id="home-right-bar-container">
                                    <div class="container-right row no-margin col-12 no-padding">

                                        {!! view_render_event('bagisto.shop.layout.content.before') !!}

                                            @yield('content-wrapper')

                                        {!! view_render_event('bagisto.shop.layout.content.after') !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @show

                <div class="container">
                    {!! view_render_event('bagisto.shop.layout.full-content.before') !!}

                        @yield('full-content-wrapper')

                    {!! view_render_event('bagisto.shop.layout.full-content.after') !!}
                </div>
            </div>

            {{-- overlay loader --}}
            <velocity-overlay-loader></velocity-overlay-loader>

            <go-top bg-color="rgb(121, 145, 78)"></go-top>
        </div>

        @include('paywithcryptoformarket::layouts.mainpage-items.index')

        @if(Route::is('customer.register.forgot-password'))

            @include('paywithcryptoformarket::layouts.subscriber.index')

        @endif

        {{-- footer --}}
        @section('footer')
            {!! view_render_event('bagisto.shop.layout.footer.before') !!}

                @include('paywithcryptoformarket::layouts.footer.index')

            {!! view_render_event('bagisto.shop.layout.footer.after') !!}
        @show

        {!! view_render_event('bagisto.shop.layout.body.after') !!}


        {{-- alert container --}}
        <div id="alert-container"></div>

        {{-- all scripts --}}
        @include('paywithcryptoformarket::layouts.scripts')
        {{-- @yield('main-page-items') --}}
        @yield('couponscript')
    </body>
</html>
