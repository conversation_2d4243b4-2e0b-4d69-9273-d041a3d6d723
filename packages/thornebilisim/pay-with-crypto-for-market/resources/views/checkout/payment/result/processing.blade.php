@extends('paywithcryptoformarket::layouts.master')

@section('page_title')
    {{ __('ui.pay-with-crypto-for-market.result.processing.page-title') }}
@stop

@section('body')
    <!-- breadcrumb-area -->
    <section class="breadcrumb-area breadcrumb-bg">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="breadcrumb-content">
                        <h2 class="title">
                            {!! __('ui.pay-with-crypto-for-market.result.processing.heading') !!}
                        </h2>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- breadcrumb-area-end -->

    <div class="area-bg" style="background-position: top;">
        <section class="blog-area pt-130 pb-130">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="blog-details bg-black bg-opacity-25 shadow-lg rounded-lg border border-dark p-4">
                            <div class="blog-content p-4">
                                <div class="d-flex align-items-center pb-4">
                                    <div class="choose-icon m-0">
                                        <img src="{{ asset('assets/img/icon/choose_icon02.svg') }}" alt="">
                                    </div>
                                    <h2 class="m-0 pl-4">{!! __('ui.pay-with-crypto-for-market.result.processing.title') !!}</h2>
                                </div>
                                <p class="fs-5">
                                    {!! __('ui.pay-with-crypto-for-market.result.processing.description') !!}
                                </p>

                                <div class="d-flex pt-4">
                                    <a href="{{ route('customer.orders.index') }}" class="btn me-4">{{ __('ui.pay-with-crypto-for-market.result.processing.my-orders') }}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
