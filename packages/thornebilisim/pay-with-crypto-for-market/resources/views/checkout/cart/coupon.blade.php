@if ($cart)
    <script type="text/x-template" id="coupon-component-template">
        <div class="order-summary coupon-container bg-terra-light-wheat py-3 px-8 rounded-2xl my-2">
            <div class="discount-control">
                <form class="custom-form" method="post" @submit.prevent="applyCoupon">
                    <div class="control-group" :class="[errorMessage ? 'has-error' : '']">
                        <div class="control-error">@{{ errorMessage }}</div>
                        <div class="relative">
                            <input
                                type="text"
                                name="code"
                                class="control font-terramirum font-bold w-full rounded-xl text-base py-4 px-3 border-3 text-center placeholder:!text-borderlight-gray"
                                v-model="couponCode"
                                placeholder="{{ __('shop::app.checkout.onepage.enter-coupon-code') }}" />
                            <div class="absolute right-2 top-1/4">
                                <button class="cursor-pointer bg-transparent focus:border-transparent focus:ring-transparent" type="reset">
                                    <img class="max-w-[20px]" src="/deneme/clear.png" alt="">
                                </button>

                            </div>
                        </div>

                    </div>
                    <div class="flex justify-end">
                        <button class="cursor-pointer, max-w-[170px] unset w-full font-terramirum font-bold bg-terra-orange text-white rounded-xl h-8 self-center mt-2 hover:opacity-90 hover:underline" :disabled="disableButton">{{ __('shop::app.checkout.onepage.apply-coupon') }}</button>
                    </div>

                </form>
            </div>

            <div class="flex w-full items-center mt-2" v-if="appliedCoupon">
                <label class="mr-2">{{ __('shop::app.checkout.total.coupon-applied') }} : </label>

                <label class="flex items-center space-x-2">
                    <b class="text-terra-khaki-green text-base">@{{ appliedCoupon }}</b>

                    <i class="rango-close fs18 cursor-pointer" title="{{ __('shop::app.checkout.total.remove-coupon') }}" v-on:click="removeCoupon"></i>
                </label>
            </div>
        </div>
    </script>

    <script>
        Vue.component('coupon-component', {
            template: '#coupon-component-template',

            inject: ['$validator'],

            data: function() {
                return {
                    couponCode: '',
                    errorMessage: '',
                    appliedCoupon: "{{ $cart->coupon_code }}",
                    routeName: "{{ request()->route()->getName() }}",
                    disableButton: false,
                    removeIconEnabled: true
                }
            },

            watch: {
                couponCode: function (value) {
                    if (value != '') {
                        this.errorMessage = '';
                    }
                }
            },

            methods: {
                applyCoupon: function() {
                    if (! this.couponCode.length) {
                        this.errorMessage = '{{ __('shop::app.checkout.total.invalid-coupon') }}';

                        return;
                    }

                    this.errorMessage = null;

                    this.disableButton = true;

                    let code = this.couponCode;

                    axios
                        .post(
                            '{{ route('shop.checkout.cart.coupon.apply') }}', {code}
                        ).then(response => {
                            if (response.data.success) {
                                this.$emit('onApplyCoupon');

                                this.appliedCoupon = this.couponCode;
                                this.couponCode = '';

                                window.showAlert(
                                    'alert-success',
                                    response.data.label,
                                    response.data.message
                                 );

                                this.redirectIfCartPage();
                            } else {
                                this.errorMessage = response.data.message;
                            }

                            this.disableButton = false;
                        }).catch(error => {
                            this.errorMessage = error.response.data.message;

                            this.disableButton = false;
                        });
                },

                removeCoupon: function () {
                    let self = this;

                    if (self.removeIconEnabled) {
                        self.removeIconEnabled = false;

                        axios
                        .delete('{{ route('shop.checkout.coupon.remove.coupon') }}')
                        .then(function(response) {
                            self.$emit('onRemoveCoupon')

                            self.appliedCoupon = '';

                            self.disableButton = false;

                            self.removeIconEnabled = true;

                            window.showAlert(
                                'alert-success',
                                response.data.label,
                                response.data.message
                            );

                            self.redirectIfCartPage();
                        })
                        .catch(function(error) {
                            window.flashMessages = [{'type': 'alert-error', 'message': error.response.data.message}];

                            self.$root.addFlashMessages();

                            self.disableButton = false;

                            self.removeIconEnabled = true;
                        });
                    }
                },

                redirectIfCartPage: function() {
                    if (this.routeName != 'shop.checkout.cart.index') return;

                    setTimeout(function() {
                        window.location.reload();
                    }, 700);
                }
            }
        });
    </script>
@endif