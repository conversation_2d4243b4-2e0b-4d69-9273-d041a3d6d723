<?php

namespace Thorne\SumsubKyc\Services;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Thorne\SumsubKyc\Models\KYCVerification;
use Webkul\Customer\Models\Customer;

class SumsubWebhookService
{
    protected string $secretKey;

    public function __construct()
    {
        $this->secretKey = core()->getConfigData('customer.sumsub-kyc.settings.webhook_secret') ?? config('sumsub.webhook_secret') ?? env('SUMSUB_WEBHOOK_SECRET');
    }

    public function validateSignature(Request $request): bool
    {
        $signature = $request->header('x-payload-digest');
        $payload = $request->getContent();
        $digestAlgorithmHeader = $request->header('x-payload-digest-alg', 'HMAC_SHA256_HEX');

        if (! $signature || ! $this->secretKey) {
            Log::channel('sumsub-webhook')->error('Signature or secret key is missing');

            return false;
        }

        $hashAlgorithm = match ($digestAlgorithmHeader) {
            'HMAC_SHA1_HEX' => 'sha1',
            'HMAC_SHA512_HEX' => 'sha512',
            default => 'sha256',
        };

        $calculatedSignature = hash_hmac($hashAlgorithm, $payload, $this->secretKey);

        Log::channel('sumsub-webhook')->debug('Signature verification', [
            'signature' => $signature,
            'calculated_signature' => $calculatedSignature,
            'algorithm_header' => $digestAlgorithmHeader,
            'hash_algorithm' => $hashAlgorithm,
            'hash_equals' => hash_equals($calculatedSignature, $signature),
        ]);

        return hash_equals($calculatedSignature, $signature);
    }

    public function processWebhook(array $payload): void
    {
        try {
            $eventType = data_get($payload, 'type');
            $applicantId = data_get($payload, 'applicantId');
            $externalUserId = str_replace(
                core()->getConfigData('customer.sumsub-kyc.settings.external_user_id_prefix') ?? config('sumsub-kyc.external_user_id_prefix'), '', data_get($payload, 'externalUserId'));

            Log::channel('sumsub-webhook')->info("Sumsub webhook received for externalUserId: $externalUserId", $payload);

            // Events; applicantCreated, applicantPending, applicantReviewed, applicantOnHold, applicantActionPending, applicantActionReviewed, applicantActionOnHold, applicantPersonalInfoChanged, applicantTagsChanged, applicantActivated, applicantDeactivated, applicantDeleted, applicantReset, applicantLevelChanged, applicantWorkflowCompleted

            switch ($eventType) {
                case 'applicantCreated':
                    $this->handleApplicantCreated($payload);
                    break;

                case 'applicantPending':
                    $this->handleApplicantPending($payload);
                    break;

                case 'applicantReviewed':
                    $this->handleApplicantReviewed($payload);
                    break;

                case 'applicantOnHold':
                    $this->handleApplicantOnHold($payload);
                    break;

                case 'applicantActionPending':
                    $this->handleApplicantActionPending($payload);
                    break;

                case 'applicantActionReviewed':
                    $this->handleApplicantActionReviewed($payload);
                    break;

                case 'applicantActionOnHold':
                    $this->handleApplicantActionOnHold($payload);
                    break;

                case 'applicantPersonalInfoChanged':
                    $this->handleApplicantPersonalInfoChanged($payload);
                    break;

                case 'applicantTagsChanged':
                    $this->handleApplicantTagsChanged($payload);
                    break;

                case 'applicantActivated':
                    $this->handleApplicantActivated($payload);
                    break;

                case 'applicantDeactivated':
                    $this->handleApplicantDeactivated($payload);
                    break;

                case 'applicantDeleted':
                    $this->handleApplicantDeleted($payload);
                    break;

                case 'applicantReset':
                    $this->handleApplicantReset($payload);
                    break;

                case 'applicantLevelChanged':
                    $this->handleApplicantLevelChanged($payload);
                    break;

                case 'applicantWorkflowCompleted':
                    $this->handleApplicantWorkflowCompleted($payload);
                    break;

                default:
                    Log::channel('sumsub-webhook')->warning("Sumsub webhook event type not handled: $eventType");
            }

            $customer = Customer::find($externalUserId);
            if (isset($customer)) {
                $customer->kyc_verifications()->create([
                    'external_user_id' => $externalUserId,
                    'applicant_id' => $applicantId,
                    'action' => $eventType,
                    'data' => $payload,
                ]);

                Log::channel('sumsub-webhook')->info("Sumsub webhook saved successfully for externalUserId: $externalUserId", $payload);
            } else {
                Log::channel('sumsub-webhook')->error("Sumsub webhook customer not found for externalUserId: $externalUserId", $payload);
            }
        } catch (Exception $e) {
            Log::error('Error processing Sumsub Webhook:', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    protected function handleApplicantCreated(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $inspectionId = data_get($payload, 'inspectionId');
        $levelName = data_get($payload, 'levelName');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantCreated triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantPending(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $inspectionId = data_get($payload, 'inspectionId');
        $levelName = data_get($payload, 'levelName');
        $externalUserId = data_get($payload, 'externalUserId');
        $reviewStatus = data_get($payload, 'reviewStatus');
        $createdAt = data_get($payload, 'createdAtMs');

        Log::channel('sumsub-webhook')->info("Sumsub applicantPending triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantReviewed(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $reviewResult = data_get($payload, 'reviewResult');
        $reviewStatus = data_get($payload, 'reviewStatus');
        $reviewAnswer = data_get($payload, 'reviewResult.reviewAnswer');
        $externalUserId = str_replace(
            core()->getConfigData('customer.sumsub-kyc.settings.external_user_id_prefix') ?? config('sumsub-kyc.external_user_id_prefix'), '', data_get($payload, 'externalUserId'));

        Log::channel('sumsub-webhook')->info("Sumsub applicantReviewed triggered for externalUserId: $externalUserId", $payload);

        if ($reviewStatus === 'completed' && $reviewAnswer === 'GREEN') {
            try {
                Log::channel('sumsub-webhook')->info("Sumsub verification completed for externalUserId: $externalUserId", $payload);

                $sumSubService = app(SumsubService::class);
                $applicantData = $sumSubService->getApplicantData($applicantId);
                $applicantReviewDate = Carbon::parse(data_get($applicantData, 'review.reviewDate'))->toDateTimeString();

                $applicantReviewTypes = collect(data_get($applicantData, 'requiredIdDocs.docSets', []))
                    ->pluck('idDocSetType')
                    ->mapWithKeys(function ($docType) use ($applicantReviewDate) {
                        return match ($docType) {
                            'PHONE_VERIFICATION' => ['phone_verified_at' => $applicantReviewDate],
                            'EMAIL_VERIFICATION' => ['email_verified_at' => $applicantReviewDate],
                            'IDENTITY' => ['identity_verified_at' => $applicantReviewDate],
                            default => [],
                        };
                    })->toArray();

                $customer = $this?->getCustomerWithExternalUserId($externalUserId);

                if (isset($customer)) {
                    $customer->update(
                        count(array_filter($applicantReviewTypes)) === count($applicantReviewTypes) ? ['customer_verified_at' => now()] : $applicantReviewTypes
                    );
                    Log::channel('sumsub-webhook')->info("Sumsub verification updated for externalUserId: $externalUserId", $applicantReviewTypes);
                } else {
                    Log::channel('sumsub-webhook')->error("Sumsub verification customer not found for externalUserId: $externalUserId");
                }
            } catch (\Exception $e) {
                Log::error("Error processing Sumsub verification for externalUserId: $externalUserId", ['error' => $e->getMessage()]);
            }
        } elseif ($reviewStatus === 'completed' && $reviewAnswer === 'RED') {
            try {
                Log::channel('sumsub-webhook')->info("Sumsub verification rejected for externalUserId: $externalUserId", $payload);

                $customer = $this?->getCustomerWithExternalUserId($externalUserId);

                if (isset($customer)) {
                    $verificationFieldsToClear = [
                        'phone_verified_at' => null,
                        'email_verified_at' => null,
                        'identity_verified_at' => null,
                        'customer_verified_at' => null,
                    ];

                    $customer->update($verificationFieldsToClear);
                    Log::channel('sumsub-webhook')->info("Sumsub verification fields cleared for externalUserId: $externalUserId", $verificationFieldsToClear);
                } else {
                    Log::channel('sumsub-webhook')->error("Sumsub verification customer not found for externalUserId: $externalUserId");
                }
            } catch (\Exception $e) {
                Log::error("Error processing Sumsub verification rejection for externalUserId: $externalUserId", ['error' => $e->getMessage()]);
            }
        } else {
            Log::channel('sumsub-webhook')->info("Sumsub verification not completed for externalUserId: $externalUserId", $payload);
        }
    }

    protected function handleApplicantOnHold(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $reason = data_get($payload, 'reason');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantOnHold triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantActionPending(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $action = data_get($payload, 'action');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantActionPending triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantActionReviewed(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $reviewResult = data_get($payload, 'reviewResult');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantActionReviewed triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantActionOnHold(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $reason = data_get($payload, 'reason');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantActionOnHold triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantPersonalInfoChanged(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $changedFields = data_get($payload, 'changedFields');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantPersonalInfoChanged triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantTagsChanged(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $tags = data_get($payload, 'tags');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantTagsChanged triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantActivated(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantActivated triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantDeactivated(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantDeactivated triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantDeleted(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantDeleted triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantReset(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantReset triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantLevelChanged(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $newLevel = data_get($payload, 'newLevel');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantLevelChanged triggered for externalUserId: $externalUserId", $payload);
    }

    protected function handleApplicantWorkflowCompleted(array $payload): void
    {
        $applicantId = data_get($payload, 'applicantId');
        $workflowResult = data_get($payload, 'workflowResult');
        $externalUserId = data_get($payload, 'externalUserId');

        Log::channel('sumsub-webhook')->info("Sumsub applicantWorkflowCompleted triggered for externalUserId: $externalUserId", $payload);
    }

    protected function getCustomerWithExternalUserId(string $externalUserId)
    {
        return KYCVerification::where('external_user_id', $externalUserId)->first()?->customer;
    }
}
