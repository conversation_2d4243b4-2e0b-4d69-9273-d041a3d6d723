<?php

namespace Thorne\SumsubKyc\Providers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Thorne\SumsubKyc\Services\SumsubService;
use Webkul\Core\Facades\Core;

class SumsubKycServiceProvider extends ServiceProvider
{
    public const REQUIRED_SETTINGS = [
        'base_url',
        'app_token',
        'secret_key',
    ];

    public const ADDITIONAL_SETTINGS = [
        'webhook_secret',
        'default_level',
        'external_user_id_prefix',
        'max_ttl',
        'default_ttl',
        'status',
        'qr_code',
    ];

    public string $packageRoot;

    public function __construct($app)
    {
        parent::__construct($app);
        $this->packageRoot = dirname(__DIR__, 2);
    }

    public function register(): void
    {
        $this->registerConfigs();
        $this->registerSumsubService();
    }

    protected function registerConfigs(): void
    {
        $this->mergeConfigFrom($this->packageRoot.'/config/sumsub-kyc.php', 'sumsub-kyc');
        $this->mergeConfigFrom($this->packageRoot.'/config/logging.php', 'logging.channels');
        $this->mergeConfigFrom($this->packageRoot.'/config/logging.php', 'logging.channels');
    }

    protected function registerSumsubService(): void
    {
        $this->app->singleton(SumsubService::class, function () {
            return new class
            {
                private $instance = null;

                private bool $loggedError = false;

                public function __call($method, $args)
                {
                    if ($this->instance === null) {
                        $settings = $this->getSettings();

                        if (! $this->validateSettings($settings)) {
                            return null;
                        }

                        $this->instance = new SumsubService(
                            $settings['base_url'],
                            $settings['app_token'],
                            $settings['secret_key']
                        );
                    }

                    return $this->instance->$method(...$args);
                }

                private function getSettings(): array
                {
                    $settings = [];

                    foreach (SumsubKycServiceProvider::REQUIRED_SETTINGS as $key) {
                        $settings[$key] = Core::getConfigData("customer.sumsub-kyc.settings.{$key}") ?? config("sumsub-kyc.{$key}");
                    }

                    return $settings;
                }

                private function validateSettings(array $settings): bool
                {
                    $missingParams = array_keys(array_filter($settings, fn ($value) => empty($value)));

                    if (! empty($missingParams) && ! $this->loggedError) {
                        $this->logMissingSettings($missingParams, $settings);

                        return false;
                    }

                    return empty($missingParams);
                }

                private function logMissingSettings(array $missingParams, array $settings): void
                {
                    $additionalSettings = [];
                    foreach (SumsubKycServiceProvider::ADDITIONAL_SETTINGS as $key) {
                        $additionalSettings[$key] = Core::getConfigData("customer.sumsub-kyc.settings.{$key}");
                    }

                    $missingParams = array_merge($missingParams,
                        array_keys(array_filter($additionalSettings, fn ($value) => empty($value)))
                    );

                    session()->flash('error', 'Sumsub KYC settings are not configured properly. Please contact the support.');

                    $customer = auth()->guard('customer')->user()->id;

                    Log::channel('sumsub-api')->error("Sumsub KYC settings are not configured properly - (customer: $customer)", [
                        'missing_params' => $missingParams,
                        'params' => array_merge($settings, $additionalSettings),
                        'debug_trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
                        'timestamp' => now()->toDateTimeString(),
                    ]);

                    $this->loggedError = true;
                }
            };
        });
    }

    public function boot(): void
    {
        $this->loadTranslationsFrom($this->packageRoot.'/resources/lang', 'sumsub-kyc');
        $this->loadViewsFrom($this->packageRoot.'/resources/views', 'sumsub-kyc');
        $this->loadRoutesFrom($this->packageRoot.'/routes/routes.php');

        $this->publishAssets();
    }

    protected function publishAssets(): void
    {
        $this->publishes([
            $this->packageRoot.'/config/sumsub-kyc.php' => config_path('sumsub-kyc.php'),
            $this->packageRoot.'/resources/lang' => lang_path('vendor/sumsub-kyc'),
            $this->packageRoot.'/database/migrations' => database_path('migrations'),
        ], 'sumsub-kyc');
    }
}
