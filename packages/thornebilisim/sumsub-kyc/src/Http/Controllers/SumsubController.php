<?php

namespace Thorne\SumsubKyc\Http\Controllers;

use Thorne\SumsubKyc\Services\SumsubService;
use Webkul\Core\Facades\Core;

class SumsubController extends Controller
{
    public $customer;

    public const REVIEW_STATUSES = [
        'init' => 'init',
        'pending' => 'pending',
        'prechecked' => 'prechecked',
        'queued' => 'queued',
        'completed' => 'completed',
        'onHold' => 'onHold',
    ];

    public const REVIEW_ANSWERS = [
        'GRAY' => 'gray',
        'RED' => 'red',
        'GREEN' => 'green',
    ];

    public const REVIEW_RESULTS = [
        'PENDING' => 'pending',
        'VERIFIED' => 'verified',
        'REJECTED' => 'rejected',
        'FINAL_REJECTED' => 'final_rejected',
    ];

    public function __construct()
    {
        $this->middleware('web');

        $this->middleware(function ($request, $next) {
            if (! auth()->guard('customer')->check()) {
                return response()->json([
                    'status' => 401,
                    'message' => 'Unauthorized',
                ], 401);
            }

            if (! $request->ajax()) {
                return response()->json([
                    'status' => 400,
                    'message' => 'Bad Request',
                ], 400);
            }

            $this->customer = auth()->guard('customer')->user();

            return $next($request);
        });
    }

    public function status()
    {
        $config = Core::getConfigData('customer.sumsub-kyc.settings') ?? config('sumsub-kyc');
        $service = app(SumsubService::class);
        $externalUserId = $service->getExternalUserId($this->customer->id);
        $applicantData = $service->createApplicant(data_get($config, 'default_level'), $externalUserId, [
            'email' => $this->customer->email,
            'phone' => $this->customer->phone,
            'lang' => session()->get('locale') ?? app()->getLocale(),
            'fixedInfo' => [
                'firstName' => $this->customer->first_name,
                'lastName' => $this->customer->last_name,
                'dob' => $this->customer?->date_of_birth,
            ],
        ]);
        $applicantId = data_get($applicantData, 'data.id');

        $verificationUrl = $service->createApplicantVerificationUrl(data_get($config, 'default_level'), $externalUserId);
        $verificationQrCode = $service->createApplicantVerificationQrCode(data_get($verificationUrl, 'data.url'));
        $verificationIsAvailable = data_get($verificationUrl, 'status') == 200;
        $verificationQrIsAvailable = Core::getConfigData('customer.sumsub-kyc.settings.qr_code') ?? config('sumsub-kyc.qr_code');

        return response()->json([
            'status' => 200,
            'data' => [
                'customer' => $this->customer,
                'verification' => [
                    'url' => $verificationIsAvailable ? $verificationUrl : null,
                    'qrCode' => $verificationIsAvailable && $verificationQrIsAvailable ? $verificationQrCode : null,
                    'status' => $this->handleReview($applicantData),
                    'externalUserId' => $externalUserId,
                    'applicantId' => $applicantId,
                ],
            ],
        ], 200);
    }

    public function handleReview($applicantData): array
    {
        $review = data_get($applicantData, 'data.review');

        return [
            'status' => $this->handleReviewStatus($review),
            'answer' => $this->handleReviewAnswer($review),
            'result' => $this->handleReviewResult($review),
        ];
    }

    public function handleReviewStatus($review): string
    {
        $status = data_get($review, 'reviewStatus');

        return self::REVIEW_STATUSES[$status] ?? 'undefined_review_status';
    }

    public function handleReviewAnswer($review): string
    {
        if (data_get($review, 'reviewStatus') !== self::REVIEW_STATUSES['completed']) {
            return 'review_status_not_completed';
        }

        $answer = data_get($review, 'reviewResult.reviewAnswer');

        return self::REVIEW_ANSWERS[$answer] ?? 'undefined_review_answer';
    }

    public function handleReviewResult($review): array
    {
        if (data_get($review, 'reviewStatus') !== self::REVIEW_STATUSES['completed']) {
            return [
                'result' => self::REVIEW_RESULTS['PENDING'],
                'title' => __('sumsub-kyc::app.ui.pages.verification.review_result_pending_title'),
                'message' => __('sumsub-kyc::app.ui.pages.verification.review_result_pending_message'),
            ];
        }

        $answer = data_get($review, 'reviewResult.reviewAnswer');

        $resultKey = match ($answer) {
            'GRAY' => 'PENDING',
            'GREEN' => 'VERIFIED',
            'RED' => match (data_get($review, 'reviewResult.reviewRejectType')) {
                'RETRY' => 'REJECTED',
                default => 'FINAL_REJECTED',
            },
            default => 'undefined_review_answer',
        };

        return match ($resultKey) {
            'PENDING' => $this->handleReviewResultPending($review),
            'VERIFIED' => $this->handleReviewResultVerified($review),
            'REJECTED' => $this->handleReviewResultRejected($review),
            'FINAL_REJECTED' => $this->handleReviewResultFinalRejected($review),
            default => [
                'result' => 'undefined',
                'title' => __('sumsub-kyc::app.ui.pages.verification.review_result_undefined_title'),
                'message' => __('sumsub-kyc::app.ui.pages.verification.review_result_undefined_message'),
            ],
        };
    }

    public function handleReviewResultPending($review): array
    {
        return [
            'result' => 'pending',
            'title' => __('sumsub-kyc::app.ui.pages.verification.review_result_pending_title'),
            'message' => __('sumsub-kyc::app.ui.pages.verification.review_result_pending_message'),
        ];
    }

    public function handleReviewResultVerified($review): array
    {
        return [
            'result' => 'verified',
            'title' => __('sumsub-kyc::app.ui.pages.verification.review_result_verified_title'),
            'message' => __('sumsub-kyc::app.ui.pages.verification.review_result_verified_message'),
        ];
    }

    public function handleReviewResultRejected($review): array
    {
        $rejectReason = data_get($review, 'reviewResult.clientComment') ?? $this->handleReviewResultUndefinedReason($review);
        $rejectLabels = data_get($review, 'reviewResult.rejectLabels');

        return [
            'result' => 'rejected',
            'title' => __('sumsub-kyc::app.ui.pages.verification.review_result_rejected_title'),
            'message' => __('sumsub-kyc::app.ui.pages.verification.review_result_rejected_message'),
            'reason' => $rejectReason,
            'labels' => $rejectLabels,
        ];
    }

    public function handleReviewResultFinalRejected($review): array
    {
        $rejectReason = data_get($review, 'reviewResult.clientComment') ?? $this->handleReviewResultUndefinedReason($review);
        $rejectLabels = data_get($review, 'reviewResult.rejectLabels');

        return [
            'result' => 'final_rejected',
            'title' => __('sumsub-kyc::app.ui.pages.verification.review_result_final_rejected_title'),
            'message' => __('sumsub-kyc::app.ui.pages.verification.review_result_final_rejected_message'),
            'reason_with_labels' => __('sumsub-kyc::app.ui.pages.verification.review_result_final_rejected_reason').collect($rejectLabels)->map(function ($label) {
                return __('sumsub-kyc::app.ui.pages.verification.review_result_final_rejected_labels.'.$label);
            })->implode('. '),
            'reason' => __('sumsub-kyc::app.ui.pages.verification.review_result_final_rejected_reason').$rejectReason,
            'labels' => $rejectLabels,
        ];
    }

    public function handleReviewResultUndefinedReason($review): string
    {
        $rejectLabels = data_get($review, 'reviewResult.rejectLabels');
        $rejectLabels = is_array($rejectLabels) ? implode(', ', $rejectLabels) : 'none';

        return __('sumsub-kyc::app.ui.pages.verification.review_result_undefined_reason_message', ['labels' => $rejectLabels]);
    }
}
