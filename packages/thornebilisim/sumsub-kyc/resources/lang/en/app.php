<?php

return [
    'admin' => [
        'system' => [
            'name' => 'Sumsub KYC',
            'settings' => 'Sumsub KYC Settings',
            'fields' => [
                'base_url' => 'Base URL',
                'app_token' => 'App Token',
                'secret_key' => 'Secret Key',
                'webhook_secret' => 'Webhook Secret',
                'external_user_id_prefix' => 'External User ID Prefix',
                'default_level' => 'Default Level',
                'default_ttl' => 'Default TTL',
                'status' => 'Status',
                'qr_code' => 'QR Code',
            ],
        ],
    ],

    'ui' => [
        'pages' => [
            'verification' => [
                'title' => 'KYC Verification',
                'verification_url' => 'Verification URL',
                'verification_button' => 'Verify',
                'verification_button_retry' => 'Retry Verification',
                'verification_button_contact_support_description' => 'If you need help, please contact our support team.',
                'verification_button_contact_support_button' => 'Contact Support',

                'review_result_pending_title' => 'Verification Pending',
                'review_result_pending_message' => 'Your verification is pending. To fully access your account, please complete the verification process.',
                'review_result_verified_title' => 'Verification Completed',
                'review_result_verified_message' => 'Your verification has been completed successfully. You can now fully access your account.',
                'review_result_rejected_title' => 'Verification Failed',
                'review_result_rejected_message' => 'Your verification has been rejected. Please try again.',
                'review_result_final_rejected_title' => 'Verification Failed',
                'review_result_final_rejected_message' => 'Your verification has been final rejected. Please contact support.',
                'review_result_final_rejected_reason' => 'Reason: ',
                'review_result_final_rejected_labels' => [
                    'REGULATIONS_VIOLATIONS' => 'Regulations Violations',
                    'DUPLICATE' => 'Duplicate',
                ],
                'review_result_undefined_title' => 'Verification Failed',
                'review_result_undefined_message' => 'Your verification has been rejected. Please try again.',
                'review_result_undefined_reason_message' => 'Your verification has been rejected. Please try again. (Details: :labels)',

                'error_title' => 'An error occurred',
                'error_message' => 'An error occurred while fetching the verification status. Please try again later.',
                'error_unauthorized' => 'Unauthorized',
                'error_invalid_response' => 'Invalid response. Please try again later. If the problem persists, please contact support.',
                'error_fetching_status' => 'Failed to fetch verification status. Please try again later. If the problem persists, please contact support.',
                'retry_fetch' => 'Retry fetching',
            ],
        ],
    ],
];
