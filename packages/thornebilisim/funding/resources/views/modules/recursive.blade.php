@isset($fixedMenu)
    @php
        foreach($fixedMenu AS $menu) {
            array_push($pageItems, $menu);
        }
    @endphp
@endisset
@foreach($pageItems AS $key => $value)
    <div
        id="sidebar-{!! $key !!}"
        class="menu-click font-sans text-xl text-decoration-none cursor-pointer"
        style="padding-right: 20px;"
    >
        @if($value->parent_id == null)
            @if($value->slug == 'marketplace')
            <a
                type="button"
                class="pl5 py-3 transition-all duration-300 text-white hover:text-terra-light-green text-decoration-none font-poppins"
                href="{!! route('shop.productOrCategory.all', [$value->slug]) !!}"
            >
                {!! $value->name !!}
            </a>
            @else
                @if(isset($value->slugRemote))
                    <a
                        type="button"
                        class="pl5 py-3 transition-all duration-300 text-white hover:text-terra-light-green text-decoration-none font-poppins"
                        href="{!! $value->slug !!}"
                    >
                        {!! $value->name !!}
                    </a>
                @else
                    <a
                        type="button"
                        class="pl5 py-3 transition-all duration-300 text-white hover:text-terra-light-green text-decoration-none font-poppins"
                        href="{!! route('fund.productOrCategory.all', [$value->slug]) !!}"
                    >
                        {!! $value->name !!}
                    </a>
                @endif
            @endif
        @else
            <div
                class="category-content cursor-pointer block whitespace-no-wrap px-3 rounded-xl text-center"
                style="display: block !important;min-width: 150px;padding-right: 0;"
            >
                @if($mainCategories == 'marketplace')
                <a
                    href="{!! route( 'shop.productOrCategory.index', [$mainCategories . '/' . $value->slug]) !!}"
                    class="category unset font-poppins"
                    style="display: block !important;padding: 7px 5px 5px;"
                >
                    <span
                        class="category-title font-poppins text-white"
                        style="display:block!important;text-align:left!important;color:#ffffff!important;"
                    >
                        {!! $value->name !!}
                    </span>
                </a>
                @else
                    <a
                        href="{!! route( 'fund.productOrCategory.slug', [$mainCategories, $value->slug]) !!}"
                        class="category unset font-poppins"
                        style="display: block !important;padding: 7px 5px 5px;"
                    >
                    <span
                        class="category-title font-poppins text-white"
                        style="display:block!important;text-align:left!important;color:#ffffff!important;"
                    >
                        {!! $value->name !!}
                    </span>
                    </a>
                @endif
            </div>
        @endif
        @if(count($value->children) > 0)
            <div
                class="sidebar-{!! $key !!} category-list-container pt10 rounded-xl border border-gray-500"
                style="display: none;top: 80px;padding-right: 0;color: #ffffff;width: 250px !important;border-radius: 0;background-color: #000000;"
            >
                <div class="rounded-xl font-poppins" style="margin-bottom: 0;">
                    @include ('Funding::modules.recursive', ['pageItems' => $value->children, 'mainCategories' => $value->slug, 'fixedMenu' => []] )
                </div>
            </div>
        @else
            {!! NULL !!}
        @endif
    </div>
@endforeach
