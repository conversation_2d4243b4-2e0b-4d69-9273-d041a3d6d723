<style scoped>

</style>

<template>
    <div>
        <div class="col col-12 p-0 m-0 space-y-2">
            <funding-detail-right-progress-component
                :unit="unit"
                :unitPrice="unitPrice"
                :progressValue="progressValue"
                :investmentMade="investmentMade"
                :shareSharePrice="shareSharePrice"
                :remainingTargetText="remainingTargetText"
                :investmentMadePrice="investmentMadePrice"
                :remainingTargetPrice="remainingTargetPrice"
            ></funding-detail-right-progress-component>

            <funding-detail-right-description-component
                :fundingTarget="fundingTarget"
                :expectedReturn="expectedReturn"
                :fundingDateAlert="fundingDateAlert"
                :endForInvestment="endForInvestment"
                :fundingTargetPrice="fundingTargetPrice"
                :investmentDuration="investmentDuration"
                :endForInvestmentDuration="endForInvestmentDuration"
            ></funding-detail-right-description-component>

            <funding-detail-right-completed-component
                :moneySymbol="moneySymbol"
                :grandTotalText="grandTotalText"
                :contractButtonText="contractButtonText"
                :fundButtonText="fundButtonText"
                :grandTotalPrice="grandTotalPrice"
                :fundingApiRoute="fundingApiRoute"
                :yourEarningsText="yourEarningsText"
                :selectButtonText="selectButtonText"
                :monthsButtonItem="monthsButtonItem"
                :basketInformation="basketInformation"
                :smartContractAddress="smartContractAddress"
            ></funding-detail-right-completed-component>
        </div>
    </div>
</template>

<script>
import {EventBus} from "../app";
export default {
    name: "fundingdetailcomponent",
    props: {
        fundingTarget:String,
        expectedReturn:String,
        investmentMade:String,
        grandTotalText:String,
        fundButtonText:String,
        fundingApiRoute:String,
        shareSharePrice:String,
        selectButtonText:String,
        yourEarningsText:String,
        endForInvestment:String,
        detailApiGetroute:String,
        investmentDuration:String,
        contractButtonText:String,
        remainingTargetText:String,
        basketInformation:Object|Array,
        endForInvestmentDuration:String,
    },
    data() {
        return {
            unit: 1,
            unitPrice: [],
            progressValue: 0,
            investmentMadePrice: '',
            remainingTargetPrice: '',
            //shareSharePrice: this.shareSharePrice,
            detailTitle: 'İngiltere’de sabit kira kontratlı bir tatil mülküne yatırım yapın!!!',

            fundingTargetPrice: '',

            grandTotalPrice: [],
            detailTabItem:[],
            monthsButtonItem: [],
            imgSliderItem: [],
            moneySymbol: '',
            fundingDateAlert: 0,
            smartContractAddress: '',
        }
    },
    mounted() {},
    created() {
        setTimeout(this.functionDetailApiIndex, 100);
    },
    methods: {
        functionDetailTitleItem: function() {
            EventBus.$emit('detailTitleItem', this.detailTitle);
        },
        functionImgSliderItem: function() {
            EventBus.$emit('imgSliderItem', this.imgSliderItem);
        },
        functionDetailTabItem: function() {
            EventBus.$emit('detailTabItem', this.detailTabItem);
        },
        functionCalculatePriceDifference: function( fundingTargetPrice ) {
            console.log(fundingTargetPrice);
        },
        functionDetailApiIndex: function() {
            axios
                .get(
                    this.detailApiGetroute,
                )
                .then(response => {
                    this.moneySymbol = response?.data?.data?.moneySymbol;
                    this.detailTitle = response?.data?.data?.detailTitle;
                    this.detailTabItem = response?.data?.data?.tabsButton;
                    this.unitPrice = response?.data?.data?.shareSharePrice;
                    this.progressValue = response?.data?.data?.progressValue;
                    this.grandTotalPrice = response?.data?.data?.shareSharePrice;
                    this.fundingTargetPrice = response?.data?.data?.fundingTarget;
                    this.fundingDateAlert = response?.data?.data?.fundingDateAlert;
                    this.imgSliderItem = response?.data?.data?.data?.product_images;
                    this.investmentMadePrice = response?.data?.data?.investmentMadePrice;
                    this.remainingTargetPrice = response?.data?.data?.remainingTargetPrice;
                    this.monthsButtonItem = response?.data?.data?.monthsButton.sort( (a, b) => (a.months >= b.months) ? 1 : -1 );
                    this.smartContractAddress = response?.data?.data?.contract_address;
                    setTimeout(this.functionImgSliderItem, 50);
                    setTimeout(this.functionDetailTabItem, 50);
                    setTimeout(this.functionDetailTitleItem, 50);
                }).catch(error => {

            });
        }
    }
}

</script>