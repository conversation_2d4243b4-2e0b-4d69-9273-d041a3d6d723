<style scoped>
    .font-poppinx {
        font-family:"Poppins", sans-serif!important;
    }
    .bg-222-fund {
        background-color: rgba(17, 22, 18, 1) !important
    }
    .pl-34-fund {
        padding-left:34px!important;
    }
    .text-15-fund {
        font-size: 15px!important;
    }
    .text-242-fund {
        --tw-text-opacity: 1;
        color: rgba(134, 134, 134, var(--tw-text-opacity));
    }
    .drop-shadow-fund {
        -webkit-box-shadow: 0 0 6px 2px rgba(245, 153, 61, 1)!important;
        -moz-box-shadow: 0 0 6px 2px rgba(245, 153, 61, 1)!important;
        box-shadow: 0 0 6px 2px rgba(245, 153, 61, 1)!important;
    }
    .border-orange-fund {
        border-width: 1px!important;
        border-color: rgba(245, 153, 61, 1)!important;
    }
    .text-7xl-fund {
        font-size: 3.0rem!important;
        line-height: 1!important;
    }
    .h-100-fund {
        max-height: 100px!important;
    }
    .-ml-10-fund {
        margin-left: -30px !important;
        margin-right: -20px !important;
    }
    .ml-35-fund {
        margin-left: 35px!important;
    }
    .w-300-fund {
        width: 330px!important;
    }
</style>
<template>
<!--    <div>-->
<!--        <div class="flex flex-column w-full mt-10 bg-222-fund py-5 pl-34-fund">-->
<!--            <div class="flex w-full" style="display: flex;margin-left: 30px;">-->
<!--                <h1 class="flex items-center justify-start position-absolute font-bold text-white text-xl font-poppinx">-->
<!--                    {{ TrendFundersTitle }}-->
<!--                </h1>-->
<!--                <h4 class="flex mt-10 items-center justify-center text-white text-15-fund font-poppinx">-->
<!--                    Arsa Foncuları bu hafta yükselişte-->
<!--                </h4>-->
<!--            </div>-->
<!--            <div class="flex-column w-full" style="margin-left: 30px!important;">-->
<!--                <div v-for="(item, index) in trendFundersCart" class="flex w-300-fund h-100-fund mt-5 drop-shadow-fund float-left items-center justify-center m-3 mr-3 p-5 border-orange-fund">-->
<!--                    <div class="flex items-center justify-start mt-5 mb-5">-->
<!--                        <div class="flex items-center justify-start -ml-10-fund">-->
<!--                            <img-->
<!--                                alt=""-->
<!--                                class="flex object-none w-36 h-36 rounded-full"-->
<!--                                :src="'../themes/fundings/img/user-1.png'"-->
<!--                            />-->
<!--                        </div>-->
<!--                        <div class="flex flex-column">-->
<!--                            <h1 class="flex text-white text-lg font-bold font-poppinx">-->
<!--                                Esin Bayar-->
<!--                            </h1>-->
<!--                            <h3 class="flex text-242-fund font-bold font-poppinx">-->
<!--                                7 parça-->
<!--                            </h3>-->
<!--                        </div>-->
<!--                        <h1 class="flex items-center justify-end text-fund-text-gray-1 font-bold text-7xl-fund font-poppinx ml-35-fund">-->
<!--                            {{ index }}-->
<!--                        </h1>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
</template>

<script>
import {EventBus} from "../../app";

export default {
    name: "fundingcategoriescartcomponent",
    props: {
        TrendFundersTitle:String
    },
    data() {
        return {
            trendFundersCart: [
                {},{},{},{},{},{},{},{}
            ]
        }
    },
    mounted() {},
    created() {},
    methods: {
        funtionURLRedirect: function() {
            window.location.href = 'deneme_fund';
        }
    }
}
</script>
