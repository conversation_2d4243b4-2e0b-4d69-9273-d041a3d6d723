<style scoped>
    .font-poppinx {
        font-family:"Poppins", sans-serif!important;
    }
</style>
<template>
    <div>
        <div class="flex flex-wrap space-y-3">
            <div class="w-full">
                <h1 class="flex items-center justify-start font-bold text-white text-xl font-poppinx">
                    {{ categoriesCartTitle }}
                </h1>
                <!-- <h3 class="flex items-center justify-end position-absolute right-0 text-white text-lg font-poppinx">
                    {{ viewAllText }}
                    <img
                        :src="'../themes/fundings/img/right-facing-arrow-icon.png'"
                        style="display: flex;margin-left: 10px;width: 25px;"
                    />
                </h3> -->
            </div>
            <!--                 :class="`${'col col-' + functionGridCalculation(categoriesProductData)}`"-->
            <div class="flex flex-wrap w-full">
                <div v-for="item in categoriesProductData" class=" w-full md:w-1/2 lg:w-1/3 2xl:w-1/4">
                    <div class="relative group pb-[30px] overflow-hidden mx-3">
                        <div class=" text-white bg-black rounded-[10px] px-4 py-4 z-60 relative group">
                            <a class="max-h-[250px] overflow-hidden cursor-pointer flex" :href="item?.slug">
                                    <img
                                        :src="item?.image"
                                        class="w-full lg:min-h-200p rounded-[10px]"
                                        :alt="item?.name"
                                    />
                            </a>
                            <div class="fund-card-body">
                                <a :href="item?.slug">
                                    <h5 class="card-title text-lg font-bold text-terra-khaki-green mt-3 cursor-pointer">
                                        <!--                                    <span class="text-white">CityFUND / </span>-->
                                        <span>{{ item?.shortDescription }}</span>
                                    </h5>
                                </a>
                                <p class="text-[14px]">
                                    {{ item?.description }}
                                </p>
                                <div class="cart-infox">
                                    <div class="flex space-x-4 items-center p-3 text-white mb-[5px] rounded-[5px] bg-fund-cardgreen">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="23.328" height="26.509" viewBox="0 0 23.328 26.509">
                                            <defs>
                                                <clipPath id="clip-path">
                                                    <rect id="Rectangle_1098" data-name="Rectangle 1098" width="23.328" height="26.509" fill="#f5993d"/>
                                                </clipPath>
                                            </defs>
                                            <g id="Group_4300" data-name="Group 4300" transform="translate(0)">
                                                <g id="Group_1982" data-name="Group 1982" transform="translate(0)" clip-path="url(#clip-path)">
                                                    <path id="Path_1592" data-name="Path 1592" d="M12.305,334.819c-.792-.209-1.586-.41-2.376-.628-1.141-.314-2.281-.631-3.416-.965a1.761,1.761,0,0,0-1.46.171c-.109.064-.22.125-.334.18-.248.121-.46.235-.424.592.025.246-.193.405-.451.4q-1.683-.038-3.365-.089c-.337-.011-.488-.195-.479-.557q.053-2.009.111-4.018c.03-1.086.063-2.172.092-3.258.012-.468.147-.6.6-.591l3.121.081c.462.012.606.171.588.643-.006.159,0,.318,0,.488.38-.131.739-.275,1.109-.379a5.553,5.553,0,0,1,3.864.309,14.556,14.556,0,0,0,1.484.577,3.033,3.033,0,0,0,.806.08c.859.03,1.718.062,2.578.071a2.835,2.835,0,0,1,2.538,1.347.416.416,0,0,0,.049.065.371.371,0,0,0,.065.039l2.559-1.205c.278-.131.555-.264.833-.393a2.143,2.143,0,0,1,2.834.832c.***************.1.126v.272a2.219,2.219,0,0,1-.307.295q-3.645,2.373-7.3,4.73a4.916,4.916,0,0,1-1.1.512,11.717,11.717,0,0,1-1.234.277H12.305m-7.933-2.076a.252.252,0,0,0,.071,0,.774.774,0,0,0,.122-.058,2.789,2.789,0,0,1,2.4-.238c1.593.473,3.191.932,4.8,1.356a4.3,4.3,0,0,0,3.563-.531q3.339-2.156,6.674-4.317c.082-.053.162-.109.259-.174a1.5,1.5,0,0,0-.117-.1,1.4,1.4,0,0,0-1.49-.073c-.963.458-1.919.93-2.893,1.363-.344.153-.535.3-.468.722.055.345-.154.519-.5.509q-2.483-.068-4.965-.14a.8.8,0,0,1-.29-.055.389.389,0,0,1-.242-.465.382.382,0,0,1,.372-.336c.09-.005.181,0,.271,0l4.151.114c.095,0,.19,0,.29,0a1.883,1.883,0,0,0-1.854-1.523c-.8-.03-1.592-.067-2.388-.062a6.424,6.424,0,0,1-2.967-.714,4.58,4.58,0,0,0-4.469.175.394.394,0,0,0-.234.38c-.026,1.321-.068,2.642-.1,3.963,0,.068.009.137.014.206m-3.5.9H3.459c.061-2.2.122-4.413.184-6.667H1.059l-.183,6.667" transform="translate(0 -308.31)" fill="#f5993d"/>
                                                    <path id="Path_1593" data-name="Path 1593" d="M109.835,106.4a6.306,6.306,0,1,1-6.292,6.117,6.291,6.291,0,0,1,6.292-6.117m.027.866a5.44,5.44,0,1,0,5.409,5.567,5.418,5.418,0,0,0-5.409-5.567" transform="translate(-97.907 -100.609)" fill="#f5993d"/>
                                                    <path id="Path_1594" data-name="Path 1594" d="M212.18,2.5c0,.651,0,1.3,0,1.952,0,.355-.151.541-.425.541s-.433-.191-.434-.538q0-1.965,0-3.931c0-.408.327-.649.631-.449a.627.627,0,0,1,.218.447c.024.659.01,1.319.01,1.979" transform="translate(-199.818 0)" fill="#f5993d"/>
                                                    <path id="Path_1595" data-name="Path 1595" d="M152.278,41.449c0,.316.006.632,0,.948a.414.414,0,0,1-.424.457.421.421,0,0,1-.43-.457q-.013-.948,0-1.9a.42.42,0,0,1,.43-.458.414.414,0,0,1,.424.458c.008.316,0,.632,0,.948" transform="translate(-143.175 -37.864)" fill="#f5993d"/>
                                                    <path id="Path_1596" data-name="Path 1596" d="M272.012,41.45c0,.307,0,.614,0,.921s-.16.465-.409.473a.425.425,0,0,1-.446-.468q-.012-.934,0-1.868a.424.424,0,0,1,.442-.474c.253.007.408.175.414.47.007.316,0,.632,0,.948" transform="translate(-256.393 -37.853)" fill="#f5993d"/>
                                                    <path id="Path_1597" data-name="Path 1597" d="M183.742,162.094l.153.436c-.368.136-.72.268-1.073.4-.373.136-.751.262-1.119.41a.314.314,0,0,0-.178.215c-.01,1.041,0,2.082,0,3.123,0,.016.013.032.031.073a3.036,3.036,0,0,0,2-.984,3.116,3.116,0,0,0,.84-2.1h.418a3.5,3.5,0,0,1-3.769,3.546v-3.628l-.886.31-.157-.428c.274-.1.522-.215.781-.285a.306.306,0,0,0,.266-.386,3.383,3.383,0,0,1,0-.409l-.886.3-.158-.417c.276-.1.531-.215.8-.291a.308.308,0,0,0,.252-.37c-.015-.424,0-.849,0-1.3h.461v1.394l2.226-.807.165.431-.822.3c-.433.158-.861.33-1.3.468-.212.067-.3.167-.267.385a3.406,3.406,0,0,1,0,.413l2.226-.807" transform="translate(-170.209 -151.589)" fill="#f5993d"/>
                                                </g>
                                            </g>
                                        </svg>
                                        <span class="text-base">{{ item?.shareSharePrice }} {{sharePrice}}</span>
                                    </div>
                                    <div class="flex space-x-4 items-center p-3 text-white mb-[5px] rounded-[5px] bg-fund-cardgreen">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="23.328" height="20.409" viewBox="0 0 23.328 20.409">
                                            <defs>
                                                <clipPath id="clip-path">
                                                    <rect id="Rectangle_1097" data-name="Rectangle 1097" width="23.328" height="20.409" fill="#f5993d"/>
                                                </clipPath>
                                            </defs>
                                            <g id="Group_4299" data-name="Group 4299" transform="translate(0 0)">
                                                <g id="Group_1978" data-name="Group 1978" transform="translate(0 0)" clip-path="url(#clip-path)">
                                                    <path id="Path_1589" data-name="Path 1589" d="M26.377,20.409A.709.709,0,0,1,26,19.658a12.812,12.812,0,0,1,1.11-5.078A8.335,8.335,0,0,1,30.7,10.462c.284-.155.586-.276.851-.4a14.286,14.286,0,0,1-1.173-.991,5.188,5.188,0,0,1-1.493-4,5.352,5.352,0,1,1,6.333,5.547,6.879,6.879,0,0,1-1.269.1,5.713,5.713,0,0,0-4.756,2.4,10.252,10.252,0,0,0-2,4.944c-.056.39-.088.784-.13,1.176a.651.651,0,0,0,.007.1H41.41c-.067-.531-.112-1.058-.2-1.576a10.018,10.018,0,0,0-1.6-4.077,16.29,16.29,0,0,0-1.324-1.528.637.637,0,0,1-.243-.556.526.526,0,0,1,.9-.278,8.584,8.584,0,0,1,1.518,1.723,11.809,11.809,0,0,1,1.892,5.283c.051.444.052.894.1,1.34a.676.676,0,0,1-.368.732ZM29.916,5.35A4.3,4.3,0,1,0,34.3,1.06,4.3,4.3,0,0,0,29.916,5.35" transform="translate(-22.67 0)" fill="#f5993d"/>
                                                    <path id="Path_1590" data-name="Path 1590" d="M131.991,24.518c-.025.036-.052.07-.076.107a.525.525,0,0,1-.587.245.541.541,0,0,1-.386-.507c-.056-.711-.076-1.428-.186-2.131a6.989,6.989,0,0,0-1.955-4.023,6.873,6.873,0,0,0-2.363-1.509.676.676,0,0,0-.287-.016c-.256.02-.512.064-.768.073a.5.5,0,0,1-.562-.493.513.513,0,0,1,.539-.547,3.168,3.168,0,0,0,3.159-3.005,3.349,3.349,0,0,0-2.282-3.52,4.572,4.572,0,0,0-1.024-.182c-.353-.035-.58-.225-.575-.541a.523.523,0,0,1,.595-.511,4.4,4.4,0,0,1,4.293,3.79,4.331,4.331,0,0,1-1.462,4.036c-.118.105-.25.195-.389.3.314.2.626.379.918.585a7.46,7.46,0,0,1,2.97,4.368c.189.73.258,1.491.383,2.238.011.065.031.129.046.193Z" transform="translate(-108.663 -6.938)" fill="#f5993d"/>
                                                    <path id="Path_1591" data-name="Path 1591" d="M4.211,16.114a9.845,9.845,0,0,1-1.039-1.152A4.456,4.456,0,0,1,6.248,8.025a4.536,4.536,0,0,1,.5-.041.526.526,0,1,1,.028,1.044,3.375,3.375,0,0,0-.825,6.633,4.554,4.554,0,0,0,.654.076.544.544,0,0,1,.567.553.523.523,0,0,1-.592.493,2.033,2.033,0,0,1-.542-.047,1.326,1.326,0,0,0-1.014.211A6.628,6.628,0,0,0,1.3,21.9c-.143.726-.167,1.475-.243,2.213-.011.106,0,.214-.017.318A.519.519,0,0,1,.47,24.9.512.512,0,0,1,0,24.362a12.568,12.568,0,0,1,.3-2.848,7.762,7.762,0,0,1,2.279-4.143c.518-.472,1.113-.86,1.636-1.257" transform="translate(0 -6.961)" fill="#f5993d"/>
                                                </g>
                                            </g>
                                        </svg>
                                        <span class="text-base">{{ item?.investorTotal }} {{investor}}</span>
                                    </div>
                                    <div class="flex space-x-4 items-center p-3 text-white mb-[5px] rounded-[5px] bg-fund-cardgreen">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24.198" height="19.366" viewBox="0 0 24.198 19.366">
                                            <defs>
                                                <clipPath id="clip-path">
                                                    <rect id="Rectangle_1099" data-name="Rectangle 1099" width="24.198" height="19.366" fill="#f5993d"/>
                                                </clipPath>
                                            </defs>
                                            <g id="Group_4301" data-name="Group 4301" transform="translate(0 0)">
                                                <g id="Group_1986" data-name="Group 1986" transform="translate(0 0)" clip-path="url(#clip-path)">
                                                    <path id="Path_1598" data-name="Path 1598" d="M9.584,5.407V5.3c0-.831.014-1.662,0-2.492A1.561,1.561,0,0,1,10.2,1.5,4.37,4.37,0,0,1,11.445.818,11.58,11.58,0,0,1,13.809.225,19.927,19.927,0,0,1,16.629,0a20.249,20.249,0,0,1,3.252.209,11.3,11.3,0,0,1,2.592.662,3.946,3.946,0,0,1,1.18.692,1.49,1.49,0,0,1,.529.9,1.168,1.168,0,0,1,.01.165q0,1.959,0,3.918v8.142c0,.635-.013,1.271,0,1.905a1.5,1.5,0,0,1-.566,1.223,4.106,4.106,0,0,1-1.209.692,10.988,10.988,0,0,1-2.252.6,19.608,19.608,0,0,1-3.008.255,20.532,20.532,0,0,1-3.006-.173,12,12,0,0,1-2.746-.659c-.141-.055-.28-.116-.418-.18a.13.13,0,0,0-.137.007,6.815,6.815,0,0,1-2.285.872,7.048,7.048,0,0,1-6.017-1.563A6.846,6.846,0,0,1,.114,13.44a7,7,0,0,1,1.7-6.019A6.957,6.957,0,0,1,5.974,5.1a7.121,7.121,0,0,1,3.482.264l.129.041M7.183,18.43A6.247,6.247,0,1,0,.937,12.183,6.255,6.255,0,0,0,7.183,18.43m9.866-14.1c.175-.006.5-.012.832-.027a16.529,16.529,0,0,0,2.814-.352,7.533,7.533,0,0,0,1.757-.578A2.165,2.165,0,0,0,23.12,2.9a.348.348,0,0,0,0-.552L23.1,2.329a2.278,2.278,0,0,0-.662-.447A7.748,7.748,0,0,0,20.7,1.313,17.165,17.165,0,0,0,17.282.939a18.392,18.392,0,0,0-3.978.327,8.418,8.418,0,0,0-1.84.561,2.479,2.479,0,0,0-.781.5.365.365,0,0,0,0,.6,2.208,2.208,0,0,0,.6.414,7.57,7.57,0,0,0,1.889.62,18.823,18.823,0,0,0,3.879.361M23.257,4a.442.442,0,0,0-.049.021,6.159,6.159,0,0,1-1.42.606,15.949,15.949,0,0,1-3.891.609,21.324,21.324,0,0,1-2.5-.028,16.436,16.436,0,0,1-2.821-.42,7.207,7.207,0,0,1-2.01-.77.173.173,0,0,0-.044-.011c0,.155,0,.3,0,.453a.434.434,0,0,0,.121.324,1.7,1.7,0,0,0,.185.177,3.384,3.384,0,0,0,.923.5,11.765,11.765,0,0,0,2.867.644,19.743,19.743,0,0,0,2.463.123,17.861,17.861,0,0,0,3.447-.34,8.023,8.023,0,0,0,1.89-.6,2.042,2.042,0,0,0,.714-.5.5.5,0,0,0,.121-.246A5.238,5.238,0,0,0,23.257,4M23.246,16.21c-.019.009-.039.017-.057.028a5.905,5.905,0,0,1-1.116.5,13.563,13.563,0,0,1-2.961.61,21.138,21.138,0,0,1-3.225.091A17.4,17.4,0,0,1,12.593,17a.1.1,0,0,0-.108.033c-.206.2-.415.4-.623.606-.014.014-.027.03-.046.051l.093.035a12.694,12.694,0,0,0,2.952.61,19.9,19.9,0,0,0,2.531.094,18.11,18.11,0,0,0,2.824-.275,9.488,9.488,0,0,0,1.965-.55,2.727,2.727,0,0,0,.868-.521.572.572,0,0,0,.187-.274,3.964,3.964,0,0,0,.009-.595M11.7,6.574l-.016.018a.652.652,0,0,1,.06.039,7.489,7.489,0,0,1,1.131,1.16.217.217,0,0,0,.113.071,15.984,15.984,0,0,0,2.877.377,19.242,19.242,0,0,0,4.38-.261A9.161,9.161,0,0,0,22.266,7.4a2.87,2.87,0,0,0,.75-.449.529.529,0,0,0,.225-.309,5.578,5.578,0,0,0,.005-.6.353.353,0,0,0-.043.019,6.287,6.287,0,0,1-1.476.621,16.794,16.794,0,0,1-4.357.609,20.6,20.6,0,0,1-2.844-.126,14.726,14.726,0,0,1-2.2-.4c-.212-.058-.422-.124-.633-.186m1.549,9.477.054.014a15.532,15.532,0,0,0,1.851.261,20.939,20.939,0,0,0,2.9.04,16.791,16.791,0,0,0,2.446-.3,8.209,8.209,0,0,0,1.922-.6,2.209,2.209,0,0,0,.717-.5.469.469,0,0,0,.13-.346c-.006-.124,0-.248,0-.373,0-.023,0-.045-.008-.077l-.14.081c-.043.024-.087.049-.131.071a8.487,8.487,0,0,1-2.174.73,18.735,18.735,0,0,1-3.849.38A19.852,19.852,0,0,1,13.8,15.2a.1.1,0,0,0-.128.065c-.123.238-.254.471-.382.706l-.044.079m.425-6.965c.018.042.03.069.041.1.108.278.219.554.32.834a.128.128,0,0,0,.121.1,20.3,20.3,0,0,0,3.279.176,17.493,17.493,0,0,0,3.042-.324,8.194,8.194,0,0,0,1.954-.612,2.223,2.223,0,0,0,.7-.482.472.472,0,0,0,.137-.352c-.006-.124,0-.248,0-.373,0-.023,0-.046-.007-.074a.338.338,0,0,0-.04.017,6.959,6.959,0,0,1-1.858.73,17.419,17.419,0,0,1-4.132.506c-.51.007-1.022-.005-1.531-.032-.463-.024-.924-.077-1.385-.123-.208-.021-.414-.055-.637-.085m.428,5.081c.038.007.07.015.1.019a18.73,18.73,0,0,0,1.979.166,19.409,19.409,0,0,0,3.894-.244,9.807,9.807,0,0,0,2.08-.564,2.985,2.985,0,0,0,.9-.532.548.548,0,0,0,.207-.474c-.008-.093,0-.188,0-.281v-.122l-.068.033c-.171.089-.339.186-.515.265a10.128,10.128,0,0,1-2.445.695,19.193,19.193,0,0,1-2.99.266,20.613,20.613,0,0,1-2.233-.08c-.211-.02-.423-.042-.634-.066-.057-.007-.093,0-.1.069a1.558,1.558,0,0,1-.04.228c-.045.2-.091.408-.139.623m9.155-4.058a.269.269,0,0,0-.04.014,7.242,7.242,0,0,1-2.018.771,18.1,18.1,0,0,1-4.178.469,20.343,20.343,0,0,1-2.4-.12l-.3-.032c.017.312.032.615.052.917,0,.02.039.051.062.054.274.031.548.063.823.085a20.884,20.884,0,0,0,2.631.04,16.847,16.847,0,0,0,2.733-.337,7.992,7.992,0,0,0,1.77-.563,2.167,2.167,0,0,0,.733-.5.472.472,0,0,0,.126-.235,5.532,5.532,0,0,0,.006-.562" transform="translate(0 0)" fill="#f5993d"/>
                                                </g>
                                            </g>
                                        </svg>
                                        <span class="text-base">{{ item?.minPrice }} {{minInvesting}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-5 px-2">
                                <div class="flex flex-wrap justify-between mt-3 items-center">
                                    <div class="w-4/12 flex items-center space-x-2">
                                        <svg id="Group_4302" data-name="Group 4302" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="15.608" height="14.597" viewBox="0 0 15.608 14.597">
                                            <defs>
                                                <clipPath id="clip-path">
                                                    <rect id="Rectangle_1765" data-name="Rectangle 1765" width="15.608" height="14.597" fill="#fff"/>
                                                </clipPath>
                                            </defs>
                                            <g id="Group_4250" data-name="Group 4250" clip-path="url(#clip-path)">
                                                <path id="Path_1834" data-name="Path 1834" d="M14.965,6.985c-.04.068-.079.128-.118.186a4.769,4.769,0,0,1-.429.544c-1.416,1.534-2.124,2.3-2.429,2.628-2.861,3.081-3.148,3.387-3.72,4a.549.549,0,0,1-.923,0C5.3,12.137,3.237,9.935,1.2,7.712A4.539,4.539,0,0,1,.054,3.861,4.541,4.541,0,0,1,1.855.79,3.678,3.678,0,0,1,4.795.055a3.934,3.934,0,0,1,2.323,1.32l.687.76c.088-.1.167-.185.251-.271a13.061,13.061,0,0,1,1-1,3.649,3.649,0,0,1,3.035-.8,3.988,3.988,0,0,1,2.466,1.5,4.65,4.65,0,0,1,.626.99,4.907,4.907,0,0,1-.216,4.434m-7.158,6.3.078-.082q2.862-3.085,5.724-6.17a3.475,3.475,0,0,0,.934-2.151,3.589,3.589,0,0,0-.967-2.824,2.764,2.764,0,0,0-2.733-.92,2.931,2.931,0,0,0-1.6,1c-.341.384-.684.766-1.033,1.142a.526.526,0,0,1-.805,0c-.053-.054-.1-.114-.154-.169-.454-.479-.876-.994-1.369-1.429A2.675,2.675,0,0,0,2.692,1.5a3.516,3.516,0,0,0-1.6,2.6A3.575,3.575,0,0,0,2.054,7.08l4.9,5.287.849.913" transform="translate(0 0)" fill="#fff"/>
                                            </g>
                                        </svg>
                                        <h3 class="text-base font-semibold">300</h3>
                                    </div>
                                    <div class="w-8/12 flex justify-end items-center">
                                        <a :href="item?.slug">
                                            <h3 class="cursor-pointer hover:underline">{{readMore}}</h3>
                                        </a>
                                        <a :href="item?.slug" class="cursor-pointer w-[39px] h-[39px] ml-5 font-bold text-xl rounded-full bg-fund-badge flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="21.901" height="18.379" viewBox="0 0 21.901 18.379">
                                                <g id="Group_7096" data-name="Group 7096" transform="translate(-564.785 -987.398)">
                                                    <path id="Path_1851" data-name="Path 1851" d="M-2269.781,9389.05h17.725" transform="translate(2836.066 -8391.713)" fill="none" stroke="#fff" stroke-linecap="round" stroke-width="3"/>
                                                    <path id="Path_1852" data-name="Path 1852" d="M-2263.535,9385.34l7.57,7.7-7.57,6.439" transform="translate(2840.451 -8395.819)" fill="none" stroke="#fff" stroke-linecap="round" stroke-width="3"/>
                                                </g>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="group rounded-[10px] absolute top-0 left-0 w-full transition-all duration-600 delay-100 ease-in-out z-20 transition-transform transform group-hover:rotate-[7deg]" style="background: rgb(24,32,26);background: linear-gradient(90deg, rgba(24,32,26,1) 0%, rgba(211,105,0,1) 100%); height: calc(100% - 30px); width: calc(100% - 24px);"></div>
                    </div>
                </div>
                <div class=" w-full md:w-1/2 lg:w-1/3 2xl:w-1/4">
                    <div class="relative group pb-[30px] overflow-hidden mx-3">
                        <div class=" text-white bg-black rounded-[10px] px-4 py-4 z-60 relative group">
                            <div class="max-h-[250px] overflow-hidden cursor-pointer relative">
                                <img
                                    src="/deneme/grid/ofis.png"
                                    class="w-full lg:min-h-200p rounded-[10px]"
                                    alt="Coming Soon"
                                    style="filter: blur(6px)"
                                />
                                <img
                                    src="/fund/coming-soon.png"
                                    class="w-[90px] h-[60px] absolute top-5 left-0"
                                    alt="Coming Soon"
                                />
                            </div>
                            <div class="fund-card-body">
                                <h5 class="card-title text-lg font-bold text-terra-khaki-green mt-3 cursor-pointer">
                                    <!--                                    <span class="text-white">CityFUND / </span>-->
                                    <span>Dillenburg Project</span>
                                </h5>
                                <p class="text-[14px]">
                                     Dillenburg Shopping Mall Project
                                </p>
                                <div class="cart-infox">
                                    <div class="flex space-x-4 items-center p-3 text-white mb-[5px] rounded-[5px] bg-fund-cardgreen">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="23.328" height="26.509" viewBox="0 0 23.328 26.509">
                                            <defs>
                                                <clipPath id="clip-path">
                                                    <rect id="Rectangle_1098" data-name="Rectangle 1098" width="23.328" height="26.509" fill="#f5993d"/>
                                                </clipPath>
                                            </defs>
                                            <g id="Group_4300" data-name="Group 4300" transform="translate(0)">
                                                <g id="Group_1982" data-name="Group 1982" transform="translate(0)" clip-path="url(#clip-path)">
                                                    <path id="Path_1592" data-name="Path 1592" d="M12.305,334.819c-.792-.209-1.586-.41-2.376-.628-1.141-.314-2.281-.631-3.416-.965a1.761,1.761,0,0,0-1.46.171c-.109.064-.22.125-.334.18-.248.121-.46.235-.424.592.025.246-.193.405-.451.4q-1.683-.038-3.365-.089c-.337-.011-.488-.195-.479-.557q.053-2.009.111-4.018c.03-1.086.063-2.172.092-3.258.012-.468.147-.6.6-.591l3.121.081c.462.012.606.171.588.643-.006.159,0,.318,0,.488.38-.131.739-.275,1.109-.379a5.553,5.553,0,0,1,3.864.309,14.556,14.556,0,0,0,1.484.577,3.033,3.033,0,0,0,.806.08c.859.03,1.718.062,2.578.071a2.835,2.835,0,0,1,2.538,1.347.416.416,0,0,0,.049.065.371.371,0,0,0,.065.039l2.559-1.205c.278-.131.555-.264.833-.393a2.143,2.143,0,0,1,2.834.832c.***************.1.126v.272a2.219,2.219,0,0,1-.307.295q-3.645,2.373-7.3,4.73a4.916,4.916,0,0,1-1.1.512,11.717,11.717,0,0,1-1.234.277H12.305m-7.933-2.076a.252.252,0,0,0,.071,0,.774.774,0,0,0,.122-.058,2.789,2.789,0,0,1,2.4-.238c1.593.473,3.191.932,4.8,1.356a4.3,4.3,0,0,0,3.563-.531q3.339-2.156,6.674-4.317c.082-.053.162-.109.259-.174a1.5,1.5,0,0,0-.117-.1,1.4,1.4,0,0,0-1.49-.073c-.963.458-1.919.93-2.893,1.363-.344.153-.535.3-.468.722.055.345-.154.519-.5.509q-2.483-.068-4.965-.14a.8.8,0,0,1-.29-.055.389.389,0,0,1-.242-.465.382.382,0,0,1,.372-.336c.09-.005.181,0,.271,0l4.151.114c.095,0,.19,0,.29,0a1.883,1.883,0,0,0-1.854-1.523c-.8-.03-1.592-.067-2.388-.062a6.424,6.424,0,0,1-2.967-.714,4.58,4.58,0,0,0-4.469.175.394.394,0,0,0-.234.38c-.026,1.321-.068,2.642-.1,3.963,0,.068.009.137.014.206m-3.5.9H3.459c.061-2.2.122-4.413.184-6.667H1.059l-.183,6.667" transform="translate(0 -308.31)" fill="#f5993d"/>
                                                    <path id="Path_1593" data-name="Path 1593" d="M109.835,106.4a6.306,6.306,0,1,1-6.292,6.117,6.291,6.291,0,0,1,6.292-6.117m.027.866a5.44,5.44,0,1,0,5.409,5.567,5.418,5.418,0,0,0-5.409-5.567" transform="translate(-97.907 -100.609)" fill="#f5993d"/>
                                                    <path id="Path_1594" data-name="Path 1594" d="M212.18,2.5c0,.651,0,1.3,0,1.952,0,.355-.151.541-.425.541s-.433-.191-.434-.538q0-1.965,0-3.931c0-.408.327-.649.631-.449a.627.627,0,0,1,.218.447c.024.659.01,1.319.01,1.979" transform="translate(-199.818 0)" fill="#f5993d"/>
                                                    <path id="Path_1595" data-name="Path 1595" d="M152.278,41.449c0,.316.006.632,0,.948a.414.414,0,0,1-.424.457.421.421,0,0,1-.43-.457q-.013-.948,0-1.9a.42.42,0,0,1,.43-.458.414.414,0,0,1,.424.458c.008.316,0,.632,0,.948" transform="translate(-143.175 -37.864)" fill="#f5993d"/>
                                                    <path id="Path_1596" data-name="Path 1596" d="M272.012,41.45c0,.307,0,.614,0,.921s-.16.465-.409.473a.425.425,0,0,1-.446-.468q-.012-.934,0-1.868a.424.424,0,0,1,.442-.474c.253.007.408.175.414.47.007.316,0,.632,0,.948" transform="translate(-256.393 -37.853)" fill="#f5993d"/>
                                                    <path id="Path_1597" data-name="Path 1597" d="M183.742,162.094l.153.436c-.368.136-.72.268-1.073.4-.373.136-.751.262-1.119.41a.314.314,0,0,0-.178.215c-.01,1.041,0,2.082,0,3.123,0,.016.013.032.031.073a3.036,3.036,0,0,0,2-.984,3.116,3.116,0,0,0,.84-2.1h.418a3.5,3.5,0,0,1-3.769,3.546v-3.628l-.886.31-.157-.428c.274-.1.522-.215.781-.285a.306.306,0,0,0,.266-.386,3.383,3.383,0,0,1,0-.409l-.886.3-.158-.417c.276-.1.531-.215.8-.291a.308.308,0,0,0,.252-.37c-.015-.424,0-.849,0-1.3h.461v1.394l2.226-.807.165.431-.822.3c-.433.158-.861.33-1.3.468-.212.067-.3.167-.267.385a3.406,3.406,0,0,1,0,.413l2.226-.807" transform="translate(-170.209 -151.589)" fill="#f5993d"/>
                                                </g>
                                            </g>
                                        </svg>
                                        <span class="text-base">€ 00,00 {{sharePrice}}</span>
                                    </div>
                                    <div class="flex space-x-4 items-center p-3 text-white mb-[5px] rounded-[5px] bg-fund-cardgreen">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="23.328" height="20.409" viewBox="0 0 23.328 20.409">
                                            <defs>
                                                <clipPath id="clip-path">
                                                    <rect id="Rectangle_1097" data-name="Rectangle 1097" width="23.328" height="20.409" fill="#f5993d"/>
                                                </clipPath>
                                            </defs>
                                            <g id="Group_4299" data-name="Group 4299" transform="translate(0 0)">
                                                <g id="Group_1978" data-name="Group 1978" transform="translate(0 0)" clip-path="url(#clip-path)">
                                                    <path id="Path_1589" data-name="Path 1589" d="M26.377,20.409A.709.709,0,0,1,26,19.658a12.812,12.812,0,0,1,1.11-5.078A8.335,8.335,0,0,1,30.7,10.462c.284-.155.586-.276.851-.4a14.286,14.286,0,0,1-1.173-.991,5.188,5.188,0,0,1-1.493-4,5.352,5.352,0,1,1,6.333,5.547,6.879,6.879,0,0,1-1.269.1,5.713,5.713,0,0,0-4.756,2.4,10.252,10.252,0,0,0-2,4.944c-.056.39-.088.784-.13,1.176a.651.651,0,0,0,.007.1H41.41c-.067-.531-.112-1.058-.2-1.576a10.018,10.018,0,0,0-1.6-4.077,16.29,16.29,0,0,0-1.324-1.528.637.637,0,0,1-.243-.556.526.526,0,0,1,.9-.278,8.584,8.584,0,0,1,1.518,1.723,11.809,11.809,0,0,1,1.892,5.283c.051.444.052.894.1,1.34a.676.676,0,0,1-.368.732ZM29.916,5.35A4.3,4.3,0,1,0,34.3,1.06,4.3,4.3,0,0,0,29.916,5.35" transform="translate(-22.67 0)" fill="#f5993d"/>
                                                    <path id="Path_1590" data-name="Path 1590" d="M131.991,24.518c-.025.036-.052.07-.076.107a.525.525,0,0,1-.587.245.541.541,0,0,1-.386-.507c-.056-.711-.076-1.428-.186-2.131a6.989,6.989,0,0,0-1.955-4.023,6.873,6.873,0,0,0-2.363-1.509.676.676,0,0,0-.287-.016c-.256.02-.512.064-.768.073a.5.5,0,0,1-.562-.493.513.513,0,0,1,.539-.547,3.168,3.168,0,0,0,3.159-3.005,3.349,3.349,0,0,0-2.282-3.52,4.572,4.572,0,0,0-1.024-.182c-.353-.035-.58-.225-.575-.541a.523.523,0,0,1,.595-.511,4.4,4.4,0,0,1,4.293,3.79,4.331,4.331,0,0,1-1.462,4.036c-.118.105-.25.195-.389.3.314.2.626.379.918.585a7.46,7.46,0,0,1,2.97,4.368c.189.73.258,1.491.383,2.238.011.065.031.129.046.193Z" transform="translate(-108.663 -6.938)" fill="#f5993d"/>
                                                    <path id="Path_1591" data-name="Path 1591" d="M4.211,16.114a9.845,9.845,0,0,1-1.039-1.152A4.456,4.456,0,0,1,6.248,8.025a4.536,4.536,0,0,1,.5-.041.526.526,0,1,1,.028,1.044,3.375,3.375,0,0,0-.825,6.633,4.554,4.554,0,0,0,.654.076.544.544,0,0,1,.567.553.523.523,0,0,1-.592.493,2.033,2.033,0,0,1-.542-.047,1.326,1.326,0,0,0-1.014.211A6.628,6.628,0,0,0,1.3,21.9c-.143.726-.167,1.475-.243,2.213-.011.106,0,.214-.017.318A.519.519,0,0,1,.47,24.9.512.512,0,0,1,0,24.362a12.568,12.568,0,0,1,.3-2.848,7.762,7.762,0,0,1,2.279-4.143c.518-.472,1.113-.86,1.636-1.257" transform="translate(0 -6.961)" fill="#f5993d"/>
                                                </g>
                                            </g>
                                        </svg>
                                        <span class="text-base">0 {{investor}}</span>
                                    </div>
                                    <div class="flex space-x-4 items-center p-3 text-white mb-[5px] rounded-[5px] bg-fund-cardgreen">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24.198" height="19.366" viewBox="0 0 24.198 19.366">
                                            <defs>
                                                <clipPath id="clip-path">
                                                    <rect id="Rectangle_1099" data-name="Rectangle 1099" width="24.198" height="19.366" fill="#f5993d"/>
                                                </clipPath>
                                            </defs>
                                            <g id="Group_4301" data-name="Group 4301" transform="translate(0 0)">
                                                <g id="Group_1986" data-name="Group 1986" transform="translate(0 0)" clip-path="url(#clip-path)">
                                                    <path id="Path_1598" data-name="Path 1598" d="M9.584,5.407V5.3c0-.831.014-1.662,0-2.492A1.561,1.561,0,0,1,10.2,1.5,4.37,4.37,0,0,1,11.445.818,11.58,11.58,0,0,1,13.809.225,19.927,19.927,0,0,1,16.629,0a20.249,20.249,0,0,1,3.252.209,11.3,11.3,0,0,1,2.592.662,3.946,3.946,0,0,1,1.18.692,1.49,1.49,0,0,1,.529.9,1.168,1.168,0,0,1,.01.165q0,1.959,0,3.918v8.142c0,.635-.013,1.271,0,1.905a1.5,1.5,0,0,1-.566,1.223,4.106,4.106,0,0,1-1.209.692,10.988,10.988,0,0,1-2.252.6,19.608,19.608,0,0,1-3.008.255,20.532,20.532,0,0,1-3.006-.173,12,12,0,0,1-2.746-.659c-.141-.055-.28-.116-.418-.18a.13.13,0,0,0-.137.007,6.815,6.815,0,0,1-2.285.872,7.048,7.048,0,0,1-6.017-1.563A6.846,6.846,0,0,1,.114,13.44a7,7,0,0,1,1.7-6.019A6.957,6.957,0,0,1,5.974,5.1a7.121,7.121,0,0,1,3.482.264l.129.041M7.183,18.43A6.247,6.247,0,1,0,.937,12.183,6.255,6.255,0,0,0,7.183,18.43m9.866-14.1c.175-.006.5-.012.832-.027a16.529,16.529,0,0,0,2.814-.352,7.533,7.533,0,0,0,1.757-.578A2.165,2.165,0,0,0,23.12,2.9a.348.348,0,0,0,0-.552L23.1,2.329a2.278,2.278,0,0,0-.662-.447A7.748,7.748,0,0,0,20.7,1.313,17.165,17.165,0,0,0,17.282.939a18.392,18.392,0,0,0-3.978.327,8.418,8.418,0,0,0-1.84.561,2.479,2.479,0,0,0-.781.5.365.365,0,0,0,0,.6,2.208,2.208,0,0,0,.6.414,7.57,7.57,0,0,0,1.889.62,18.823,18.823,0,0,0,3.879.361M23.257,4a.442.442,0,0,0-.049.021,6.159,6.159,0,0,1-1.42.606,15.949,15.949,0,0,1-3.891.609,21.324,21.324,0,0,1-2.5-.028,16.436,16.436,0,0,1-2.821-.42,7.207,7.207,0,0,1-2.01-.77.173.173,0,0,0-.044-.011c0,.155,0,.3,0,.453a.434.434,0,0,0,.121.324,1.7,1.7,0,0,0,.185.177,3.384,3.384,0,0,0,.923.5,11.765,11.765,0,0,0,2.867.644,19.743,19.743,0,0,0,2.463.123,17.861,17.861,0,0,0,3.447-.34,8.023,8.023,0,0,0,1.89-.6,2.042,2.042,0,0,0,.714-.5.5.5,0,0,0,.121-.246A5.238,5.238,0,0,0,23.257,4M23.246,16.21c-.019.009-.039.017-.057.028a5.905,5.905,0,0,1-1.116.5,13.563,13.563,0,0,1-2.961.61,21.138,21.138,0,0,1-3.225.091A17.4,17.4,0,0,1,12.593,17a.1.1,0,0,0-.108.033c-.206.2-.415.4-.623.606-.014.014-.027.03-.046.051l.093.035a12.694,12.694,0,0,0,2.952.61,19.9,19.9,0,0,0,2.531.094,18.11,18.11,0,0,0,2.824-.275,9.488,9.488,0,0,0,1.965-.55,2.727,2.727,0,0,0,.868-.521.572.572,0,0,0,.187-.274,3.964,3.964,0,0,0,.009-.595M11.7,6.574l-.016.018a.652.652,0,0,1,.06.039,7.489,7.489,0,0,1,1.131,1.16.217.217,0,0,0,.113.071,15.984,15.984,0,0,0,2.877.377,19.242,19.242,0,0,0,4.38-.261A9.161,9.161,0,0,0,22.266,7.4a2.87,2.87,0,0,0,.75-.449.529.529,0,0,0,.225-.309,5.578,5.578,0,0,0,.005-.6.353.353,0,0,0-.043.019,6.287,6.287,0,0,1-1.476.621,16.794,16.794,0,0,1-4.357.609,20.6,20.6,0,0,1-2.844-.126,14.726,14.726,0,0,1-2.2-.4c-.212-.058-.422-.124-.633-.186m1.549,9.477.054.014a15.532,15.532,0,0,0,1.851.261,20.939,20.939,0,0,0,2.9.04,16.791,16.791,0,0,0,2.446-.3,8.209,8.209,0,0,0,1.922-.6,2.209,2.209,0,0,0,.717-.5.469.469,0,0,0,.13-.346c-.006-.124,0-.248,0-.373,0-.023,0-.045-.008-.077l-.14.081c-.043.024-.087.049-.131.071a8.487,8.487,0,0,1-2.174.73,18.735,18.735,0,0,1-3.849.38A19.852,19.852,0,0,1,13.8,15.2a.1.1,0,0,0-.128.065c-.123.238-.254.471-.382.706l-.044.079m.425-6.965c.018.042.03.069.041.1.108.278.219.554.32.834a.128.128,0,0,0,.121.1,20.3,20.3,0,0,0,3.279.176,17.493,17.493,0,0,0,3.042-.324,8.194,8.194,0,0,0,1.954-.612,2.223,2.223,0,0,0,.7-.482.472.472,0,0,0,.137-.352c-.006-.124,0-.248,0-.373,0-.023,0-.046-.007-.074a.338.338,0,0,0-.04.017,6.959,6.959,0,0,1-1.858.73,17.419,17.419,0,0,1-4.132.506c-.51.007-1.022-.005-1.531-.032-.463-.024-.924-.077-1.385-.123-.208-.021-.414-.055-.637-.085m.428,5.081c.038.007.07.015.1.019a18.73,18.73,0,0,0,1.979.166,19.409,19.409,0,0,0,3.894-.244,9.807,9.807,0,0,0,2.08-.564,2.985,2.985,0,0,0,.9-.532.548.548,0,0,0,.207-.474c-.008-.093,0-.188,0-.281v-.122l-.068.033c-.171.089-.339.186-.515.265a10.128,10.128,0,0,1-2.445.695,19.193,19.193,0,0,1-2.99.266,20.613,20.613,0,0,1-2.233-.08c-.211-.02-.423-.042-.634-.066-.057-.007-.093,0-.1.069a1.558,1.558,0,0,1-.04.228c-.045.2-.091.408-.139.623m9.155-4.058a.269.269,0,0,0-.04.014,7.242,7.242,0,0,1-2.018.771,18.1,18.1,0,0,1-4.178.469,20.343,20.343,0,0,1-2.4-.12l-.3-.032c.017.312.032.615.052.917,0,.02.039.051.062.054.274.031.548.063.823.085a20.884,20.884,0,0,0,2.631.04,16.847,16.847,0,0,0,2.733-.337,7.992,7.992,0,0,0,1.77-.563,2.167,2.167,0,0,0,.733-.5.472.472,0,0,0,.126-.235,5.532,5.532,0,0,0,.006-.562" transform="translate(0 0)" fill="#f5993d"/>
                                                </g>
                                            </g>
                                        </svg>
                                        <span class="text-base">€ 00,00 {{minInvesting}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-5 px-2">
                                <div class="flex flex-wrap justify-between mt-3 items-center">
                                    <div class="w-full flex justify-end items-center">
                                        <h3 class="cursor-pointer hover:underline min-h-[40px] flex items-center text-lg font-bold text-terra-khaki-green">Coming Soon</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="group rounded-[10px] absolute top-0 left-0 w-full transition-all duration-600 delay-100 ease-in-out z-20 transition-transform transform group-hover:rotate-[7deg]" style="background: rgb(24,32,26);background: linear-gradient(90deg, rgba(24,32,26,1) 0%, rgba(211,105,0,1) 100%); height: calc(100% - 30px); width: calc(100% - 24px);"></div>
                    </div>
                </div>
                <div class=" w-full md:w-1/2 lg:w-1/3 2xl:w-1/4">
                    <div class="relative group pb-[30px] overflow-hidden mx-3">
                        <div class=" text-white bg-black rounded-[10px] px-4 py-4 z-60 relative group">
                            <div class="max-h-[250px] overflow-hidden cursor-pointer relative">
                                <img
                                    src="/deneme/grid/otel.png"
                                    class="w-full lg:min-h-200p rounded-[10px]"
                                    alt="Coming Soon"
                                    style="filter: blur(6px)"
                                />
                                <img
                                    src="/fund/coming-soon.png"
                                    class="w-[90px] h-[60px] absolute top-5 left-0"
                                    alt="Coming Soon"
                                />
                            </div>
                            <div class="fund-card-body">
                                <h5 class="card-title text-lg font-bold text-terra-khaki-green mt-3 cursor-pointer">
                                    <!--                                    <span class="text-white">CityFUND / </span>-->
                                    <span>Dillenburg Project</span>
                                </h5>
                                <p class="text-[14px]">
                                     Dillenburg Shopping Mall Project
                                </p>
                                <div class="cart-infox">
                                    <div class="flex space-x-4 items-center p-3 text-white mb-[5px] rounded-[5px] bg-fund-cardgreen">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="23.328" height="26.509" viewBox="0 0 23.328 26.509">
                                            <defs>
                                                <clipPath id="clip-path">
                                                    <rect id="Rectangle_1098" data-name="Rectangle 1098" width="23.328" height="26.509" fill="#f5993d"/>
                                                </clipPath>
                                            </defs>
                                            <g id="Group_4300" data-name="Group 4300" transform="translate(0)">
                                                <g id="Group_1982" data-name="Group 1982" transform="translate(0)" clip-path="url(#clip-path)">
                                                    <path id="Path_1592" data-name="Path 1592" d="M12.305,334.819c-.792-.209-1.586-.41-2.376-.628-1.141-.314-2.281-.631-3.416-.965a1.761,1.761,0,0,0-1.46.171c-.109.064-.22.125-.334.18-.248.121-.46.235-.424.592.025.246-.193.405-.451.4q-1.683-.038-3.365-.089c-.337-.011-.488-.195-.479-.557q.053-2.009.111-4.018c.03-1.086.063-2.172.092-3.258.012-.468.147-.6.6-.591l3.121.081c.462.012.606.171.588.643-.006.159,0,.318,0,.488.38-.131.739-.275,1.109-.379a5.553,5.553,0,0,1,3.864.309,14.556,14.556,0,0,0,1.484.577,3.033,3.033,0,0,0,.806.08c.859.03,1.718.062,2.578.071a2.835,2.835,0,0,1,2.538,1.347.416.416,0,0,0,.049.065.371.371,0,0,0,.065.039l2.559-1.205c.278-.131.555-.264.833-.393a2.143,2.143,0,0,1,2.834.832c.***************.1.126v.272a2.219,2.219,0,0,1-.307.295q-3.645,2.373-7.3,4.73a4.916,4.916,0,0,1-1.1.512,11.717,11.717,0,0,1-1.234.277H12.305m-7.933-2.076a.252.252,0,0,0,.071,0,.774.774,0,0,0,.122-.058,2.789,2.789,0,0,1,2.4-.238c1.593.473,3.191.932,4.8,1.356a4.3,4.3,0,0,0,3.563-.531q3.339-2.156,6.674-4.317c.082-.053.162-.109.259-.174a1.5,1.5,0,0,0-.117-.1,1.4,1.4,0,0,0-1.49-.073c-.963.458-1.919.93-2.893,1.363-.344.153-.535.3-.468.722.055.345-.154.519-.5.509q-2.483-.068-4.965-.14a.8.8,0,0,1-.29-.055.389.389,0,0,1-.242-.465.382.382,0,0,1,.372-.336c.09-.005.181,0,.271,0l4.151.114c.095,0,.19,0,.29,0a1.883,1.883,0,0,0-1.854-1.523c-.8-.03-1.592-.067-2.388-.062a6.424,6.424,0,0,1-2.967-.714,4.58,4.58,0,0,0-4.469.175.394.394,0,0,0-.234.38c-.026,1.321-.068,2.642-.1,3.963,0,.068.009.137.014.206m-3.5.9H3.459c.061-2.2.122-4.413.184-6.667H1.059l-.183,6.667" transform="translate(0 -308.31)" fill="#f5993d"/>
                                                    <path id="Path_1593" data-name="Path 1593" d="M109.835,106.4a6.306,6.306,0,1,1-6.292,6.117,6.291,6.291,0,0,1,6.292-6.117m.027.866a5.44,5.44,0,1,0,5.409,5.567,5.418,5.418,0,0,0-5.409-5.567" transform="translate(-97.907 -100.609)" fill="#f5993d"/>
                                                    <path id="Path_1594" data-name="Path 1594" d="M212.18,2.5c0,.651,0,1.3,0,1.952,0,.355-.151.541-.425.541s-.433-.191-.434-.538q0-1.965,0-3.931c0-.408.327-.649.631-.449a.627.627,0,0,1,.218.447c.024.659.01,1.319.01,1.979" transform="translate(-199.818 0)" fill="#f5993d"/>
                                                    <path id="Path_1595" data-name="Path 1595" d="M152.278,41.449c0,.316.006.632,0,.948a.414.414,0,0,1-.424.457.421.421,0,0,1-.43-.457q-.013-.948,0-1.9a.42.42,0,0,1,.43-.458.414.414,0,0,1,.424.458c.008.316,0,.632,0,.948" transform="translate(-143.175 -37.864)" fill="#f5993d"/>
                                                    <path id="Path_1596" data-name="Path 1596" d="M272.012,41.45c0,.307,0,.614,0,.921s-.16.465-.409.473a.425.425,0,0,1-.446-.468q-.012-.934,0-1.868a.424.424,0,0,1,.442-.474c.253.007.408.175.414.47.007.316,0,.632,0,.948" transform="translate(-256.393 -37.853)" fill="#f5993d"/>
                                                    <path id="Path_1597" data-name="Path 1597" d="M183.742,162.094l.153.436c-.368.136-.72.268-1.073.4-.373.136-.751.262-1.119.41a.314.314,0,0,0-.178.215c-.01,1.041,0,2.082,0,3.123,0,.016.013.032.031.073a3.036,3.036,0,0,0,2-.984,3.116,3.116,0,0,0,.84-2.1h.418a3.5,3.5,0,0,1-3.769,3.546v-3.628l-.886.31-.157-.428c.274-.1.522-.215.781-.285a.306.306,0,0,0,.266-.386,3.383,3.383,0,0,1,0-.409l-.886.3-.158-.417c.276-.1.531-.215.8-.291a.308.308,0,0,0,.252-.37c-.015-.424,0-.849,0-1.3h.461v1.394l2.226-.807.165.431-.822.3c-.433.158-.861.33-1.3.468-.212.067-.3.167-.267.385a3.406,3.406,0,0,1,0,.413l2.226-.807" transform="translate(-170.209 -151.589)" fill="#f5993d"/>
                                                </g>
                                            </g>
                                        </svg>
                                        <span class="text-base">€ 00,00 {{sharePrice}}</span>
                                    </div>
                                    <div class="flex space-x-4 items-center p-3 text-white mb-[5px] rounded-[5px] bg-fund-cardgreen">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="23.328" height="20.409" viewBox="0 0 23.328 20.409">
                                            <defs>
                                                <clipPath id="clip-path">
                                                    <rect id="Rectangle_1097" data-name="Rectangle 1097" width="23.328" height="20.409" fill="#f5993d"/>
                                                </clipPath>
                                            </defs>
                                            <g id="Group_4299" data-name="Group 4299" transform="translate(0 0)">
                                                <g id="Group_1978" data-name="Group 1978" transform="translate(0 0)" clip-path="url(#clip-path)">
                                                    <path id="Path_1589" data-name="Path 1589" d="M26.377,20.409A.709.709,0,0,1,26,19.658a12.812,12.812,0,0,1,1.11-5.078A8.335,8.335,0,0,1,30.7,10.462c.284-.155.586-.276.851-.4a14.286,14.286,0,0,1-1.173-.991,5.188,5.188,0,0,1-1.493-4,5.352,5.352,0,1,1,6.333,5.547,6.879,6.879,0,0,1-1.269.1,5.713,5.713,0,0,0-4.756,2.4,10.252,10.252,0,0,0-2,4.944c-.056.39-.088.784-.13,1.176a.651.651,0,0,0,.007.1H41.41c-.067-.531-.112-1.058-.2-1.576a10.018,10.018,0,0,0-1.6-4.077,16.29,16.29,0,0,0-1.324-1.528.637.637,0,0,1-.243-.556.526.526,0,0,1,.9-.278,8.584,8.584,0,0,1,1.518,1.723,11.809,11.809,0,0,1,1.892,5.283c.051.444.052.894.1,1.34a.676.676,0,0,1-.368.732ZM29.916,5.35A4.3,4.3,0,1,0,34.3,1.06,4.3,4.3,0,0,0,29.916,5.35" transform="translate(-22.67 0)" fill="#f5993d"/>
                                                    <path id="Path_1590" data-name="Path 1590" d="M131.991,24.518c-.025.036-.052.07-.076.107a.525.525,0,0,1-.587.245.541.541,0,0,1-.386-.507c-.056-.711-.076-1.428-.186-2.131a6.989,6.989,0,0,0-1.955-4.023,6.873,6.873,0,0,0-2.363-1.509.676.676,0,0,0-.287-.016c-.256.02-.512.064-.768.073a.5.5,0,0,1-.562-.493.513.513,0,0,1,.539-.547,3.168,3.168,0,0,0,3.159-3.005,3.349,3.349,0,0,0-2.282-3.52,4.572,4.572,0,0,0-1.024-.182c-.353-.035-.58-.225-.575-.541a.523.523,0,0,1,.595-.511,4.4,4.4,0,0,1,4.293,3.79,4.331,4.331,0,0,1-1.462,4.036c-.118.105-.25.195-.389.3.314.2.626.379.918.585a7.46,7.46,0,0,1,2.97,4.368c.189.73.258,1.491.383,2.238.011.065.031.129.046.193Z" transform="translate(-108.663 -6.938)" fill="#f5993d"/>
                                                    <path id="Path_1591" data-name="Path 1591" d="M4.211,16.114a9.845,9.845,0,0,1-1.039-1.152A4.456,4.456,0,0,1,6.248,8.025a4.536,4.536,0,0,1,.5-.041.526.526,0,1,1,.028,1.044,3.375,3.375,0,0,0-.825,6.633,4.554,4.554,0,0,0,.654.076.544.544,0,0,1,.567.553.523.523,0,0,1-.592.493,2.033,2.033,0,0,1-.542-.047,1.326,1.326,0,0,0-1.014.211A6.628,6.628,0,0,0,1.3,21.9c-.143.726-.167,1.475-.243,2.213-.011.106,0,.214-.017.318A.519.519,0,0,1,.47,24.9.512.512,0,0,1,0,24.362a12.568,12.568,0,0,1,.3-2.848,7.762,7.762,0,0,1,2.279-4.143c.518-.472,1.113-.86,1.636-1.257" transform="translate(0 -6.961)" fill="#f5993d"/>
                                                </g>
                                            </g>
                                        </svg>
                                        <span class="text-base">0 {{investor}}</span>
                                    </div>
                                    <div class="flex space-x-4 items-center p-3 text-white mb-[5px] rounded-[5px] bg-fund-cardgreen">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24.198" height="19.366" viewBox="0 0 24.198 19.366">
                                            <defs>
                                                <clipPath id="clip-path">
                                                    <rect id="Rectangle_1099" data-name="Rectangle 1099" width="24.198" height="19.366" fill="#f5993d"/>
                                                </clipPath>
                                            </defs>
                                            <g id="Group_4301" data-name="Group 4301" transform="translate(0 0)">
                                                <g id="Group_1986" data-name="Group 1986" transform="translate(0 0)" clip-path="url(#clip-path)">
                                                    <path id="Path_1598" data-name="Path 1598" d="M9.584,5.407V5.3c0-.831.014-1.662,0-2.492A1.561,1.561,0,0,1,10.2,1.5,4.37,4.37,0,0,1,11.445.818,11.58,11.58,0,0,1,13.809.225,19.927,19.927,0,0,1,16.629,0a20.249,20.249,0,0,1,3.252.209,11.3,11.3,0,0,1,2.592.662,3.946,3.946,0,0,1,1.18.692,1.49,1.49,0,0,1,.529.9,1.168,1.168,0,0,1,.01.165q0,1.959,0,3.918v8.142c0,.635-.013,1.271,0,1.905a1.5,1.5,0,0,1-.566,1.223,4.106,4.106,0,0,1-1.209.692,10.988,10.988,0,0,1-2.252.6,19.608,19.608,0,0,1-3.008.255,20.532,20.532,0,0,1-3.006-.173,12,12,0,0,1-2.746-.659c-.141-.055-.28-.116-.418-.18a.13.13,0,0,0-.137.007,6.815,6.815,0,0,1-2.285.872,7.048,7.048,0,0,1-6.017-1.563A6.846,6.846,0,0,1,.114,13.44a7,7,0,0,1,1.7-6.019A6.957,6.957,0,0,1,5.974,5.1a7.121,7.121,0,0,1,3.482.264l.129.041M7.183,18.43A6.247,6.247,0,1,0,.937,12.183,6.255,6.255,0,0,0,7.183,18.43m9.866-14.1c.175-.006.5-.012.832-.027a16.529,16.529,0,0,0,2.814-.352,7.533,7.533,0,0,0,1.757-.578A2.165,2.165,0,0,0,23.12,2.9a.348.348,0,0,0,0-.552L23.1,2.329a2.278,2.278,0,0,0-.662-.447A7.748,7.748,0,0,0,20.7,1.313,17.165,17.165,0,0,0,17.282.939a18.392,18.392,0,0,0-3.978.327,8.418,8.418,0,0,0-1.84.561,2.479,2.479,0,0,0-.781.5.365.365,0,0,0,0,.6,2.208,2.208,0,0,0,.6.414,7.57,7.57,0,0,0,1.889.62,18.823,18.823,0,0,0,3.879.361M23.257,4a.442.442,0,0,0-.049.021,6.159,6.159,0,0,1-1.42.606,15.949,15.949,0,0,1-3.891.609,21.324,21.324,0,0,1-2.5-.028,16.436,16.436,0,0,1-2.821-.42,7.207,7.207,0,0,1-2.01-.77.173.173,0,0,0-.044-.011c0,.155,0,.3,0,.453a.434.434,0,0,0,.121.324,1.7,1.7,0,0,0,.185.177,3.384,3.384,0,0,0,.923.5,11.765,11.765,0,0,0,2.867.644,19.743,19.743,0,0,0,2.463.123,17.861,17.861,0,0,0,3.447-.34,8.023,8.023,0,0,0,1.89-.6,2.042,2.042,0,0,0,.714-.5.5.5,0,0,0,.121-.246A5.238,5.238,0,0,0,23.257,4M23.246,16.21c-.019.009-.039.017-.057.028a5.905,5.905,0,0,1-1.116.5,13.563,13.563,0,0,1-2.961.61,21.138,21.138,0,0,1-3.225.091A17.4,17.4,0,0,1,12.593,17a.1.1,0,0,0-.108.033c-.206.2-.415.4-.623.606-.014.014-.027.03-.046.051l.093.035a12.694,12.694,0,0,0,2.952.61,19.9,19.9,0,0,0,2.531.094,18.11,18.11,0,0,0,2.824-.275,9.488,9.488,0,0,0,1.965-.55,2.727,2.727,0,0,0,.868-.521.572.572,0,0,0,.187-.274,3.964,3.964,0,0,0,.009-.595M11.7,6.574l-.016.018a.652.652,0,0,1,.06.039,7.489,7.489,0,0,1,1.131,1.16.217.217,0,0,0,.113.071,15.984,15.984,0,0,0,2.877.377,19.242,19.242,0,0,0,4.38-.261A9.161,9.161,0,0,0,22.266,7.4a2.87,2.87,0,0,0,.75-.449.529.529,0,0,0,.225-.309,5.578,5.578,0,0,0,.005-.6.353.353,0,0,0-.043.019,6.287,6.287,0,0,1-1.476.621,16.794,16.794,0,0,1-4.357.609,20.6,20.6,0,0,1-2.844-.126,14.726,14.726,0,0,1-2.2-.4c-.212-.058-.422-.124-.633-.186m1.549,9.477.054.014a15.532,15.532,0,0,0,1.851.261,20.939,20.939,0,0,0,2.9.04,16.791,16.791,0,0,0,2.446-.3,8.209,8.209,0,0,0,1.922-.6,2.209,2.209,0,0,0,.717-.5.469.469,0,0,0,.13-.346c-.006-.124,0-.248,0-.373,0-.023,0-.045-.008-.077l-.14.081c-.043.024-.087.049-.131.071a8.487,8.487,0,0,1-2.174.73,18.735,18.735,0,0,1-3.849.38A19.852,19.852,0,0,1,13.8,15.2a.1.1,0,0,0-.128.065c-.123.238-.254.471-.382.706l-.044.079m.425-6.965c.018.042.03.069.041.1.108.278.219.554.32.834a.128.128,0,0,0,.121.1,20.3,20.3,0,0,0,3.279.176,17.493,17.493,0,0,0,3.042-.324,8.194,8.194,0,0,0,1.954-.612,2.223,2.223,0,0,0,.7-.482.472.472,0,0,0,.137-.352c-.006-.124,0-.248,0-.373,0-.023,0-.046-.007-.074a.338.338,0,0,0-.04.017,6.959,6.959,0,0,1-1.858.73,17.419,17.419,0,0,1-4.132.506c-.51.007-1.022-.005-1.531-.032-.463-.024-.924-.077-1.385-.123-.208-.021-.414-.055-.637-.085m.428,5.081c.038.007.07.015.1.019a18.73,18.73,0,0,0,1.979.166,19.409,19.409,0,0,0,3.894-.244,9.807,9.807,0,0,0,2.08-.564,2.985,2.985,0,0,0,.9-.532.548.548,0,0,0,.207-.474c-.008-.093,0-.188,0-.281v-.122l-.068.033c-.171.089-.339.186-.515.265a10.128,10.128,0,0,1-2.445.695,19.193,19.193,0,0,1-2.99.266,20.613,20.613,0,0,1-2.233-.08c-.211-.02-.423-.042-.634-.066-.057-.007-.093,0-.1.069a1.558,1.558,0,0,1-.04.228c-.045.2-.091.408-.139.623m9.155-4.058a.269.269,0,0,0-.04.014,7.242,7.242,0,0,1-2.018.771,18.1,18.1,0,0,1-4.178.469,20.343,20.343,0,0,1-2.4-.12l-.3-.032c.017.312.032.615.052.917,0,.02.039.051.062.054.274.031.548.063.823.085a20.884,20.884,0,0,0,2.631.04,16.847,16.847,0,0,0,2.733-.337,7.992,7.992,0,0,0,1.77-.563,2.167,2.167,0,0,0,.733-.5.472.472,0,0,0,.126-.235,5.532,5.532,0,0,0,.006-.562" transform="translate(0 0)" fill="#f5993d"/>
                                                </g>
                                            </g>
                                        </svg>
                                        <span class="text-base">€ 00,00 {{minInvesting}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-5 px-2">
                                <div class="flex flex-wrap justify-between mt-3 items-center">
                                    <div class="w-full flex justify-end items-center">
                                        <h3 class="cursor-pointer hover:underline min-h-[40px] flex items-center text-lg font-bold text-terra-khaki-green">Coming Soon</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="group rounded-[10px] absolute top-0 left-0 w-full transition-all duration-600 delay-100 ease-in-out z-20 transition-transform transform group-hover:rotate-[7deg]" style="background: rgb(24,32,26);background: linear-gradient(90deg, rgba(24,32,26,1) 0%, rgba(211,105,0,1) 100%); height: calc(100% - 30px); width: calc(100% - 24px);"></div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</template>

<script>
import {EventBus} from "../../app";

export default {
    name: "fundingcategoriescartcomponent",
    props: {
        viewAllUrl:String,
        viewAllText:String,
        categoriesCartTitle:String,
        categoriesProductData:Object|Array,
        investor:String,
        minInvesting:String,
        sharePrice:String,
        readMore:String,
    },
    data() {
        return {}
    },
    mounted() {},
    created() {},
    methods: {
        funtionURLRedirect: function( slug ) {
            console.log(slug);
            window.location.href = 'https://marketplace.test/hotel-project-at-frankfurt-airport_fund';
        },
        functionGridCalculation( value ) {
            const x = 12 / value.length;
            return x <= 3 ? 3 : Math.round(x);
        }
    }
}
</script>
