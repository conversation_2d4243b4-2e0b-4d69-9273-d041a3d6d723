<style scoped>
    .font-poppinx {
        font-family:"Poppins", sans-serif!important;
    }
    .bg-orange-fund {
        background-color:rgba(245, 153, 61, 1)!important;
    }
    .text-20-fund {
        font-size: 18px!important;
    }
    .p-5-fund {
        padding-left:40px!important;
        padding-right:40px!important;
    }
    .border-dotted-fund {
        border-width:1px!important;
        border-style:dashed!important;
        border-color:#C3C3C3!important;
    }
    .border-none-fund {
        border: none!important;
    }
    .border-orange-500-fund {
        border-color: #F5993D!important;
    }
    .pr-5-fund {
        padding-right: 20px!important;
    }
    .fund-text-area-bg-white {
        display: flex!important;
        padding: 15px!important;
        border-radius: 5px!important;
        background-color: #ffffff!important;
    }
</style>

<template>
    <div>
        <div class="mt-12 rounded-2lg">
            <div class="flex justify-around mb-4 rounded-2lg overflow-x-scroll lg:overflow-x-visible group ">
                <button
                    v-for="( item, index ) in detailTabItem"
                    @click="functionOpenTab( index, item?.code )"
                    :class="`flex ${parseInt(detailTabIndex, 10) === parseInt(index, 10) ? 'bg-orange-fund border-none-fund': ''} text-20-fund text-white px-4 2xl:px-8 py-2 rounded-2lg items-center active:outline-none focus:outline-none font-poppinx border-dotted-fund mr-5`"
                    class="group group-hover:border-terra-orange"
                >
                    <span
                        v-html="item?.icon"
                        style="display:flex;margin-right:15px;width:34px;height:34px;">
                    </span>
                    {{ item?.name }}
                </button>
            </div>
            <div
                v-for="(itemx, index) in detailTabItem"
                :id="itemx?.code"
                :class="`border border-orange-500-fund p-4 tabcontent rounded-2lg ${detailTabIndex === index ? 'block': 'hidden'}`"
            >
                <div v-if="itemx?.fundingTextarea?.length > 0" class="fund-text-area-bg-white">
                    <p v-html="itemx?.fundingTextarea[0].text_value"></p>
                </div>
                <div v-else-if="itemx?.fundingFiles?.length > 0">
                    <funding-detail-file-slider-component
                        :description="itemx?.fundingFiles"
                    ></funding-detail-file-slider-component>
                </div>
                <div v-else-if="itemx?.textRepeater?.length > 0">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-terra-dark-gray text-white text-lg leading-relaxed tracking-wider group">
                                <th class="py-4 group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">
                                    {{ textRepeaterName }}
                                </th>
                                <th class="py-4 group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">
                                    {{ textRepeaterLink }}
                                </th>
                                <th class="py-4 group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">
                                    {{ textRepeaterAddress }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item, index) in JSON.parse(itemx?.textRepeater[0].json_value)" class="border-b border-terra-dark-gray text-white text-lg leading-relaxed tracking-wider group">
                                <td class="py-4 font-bold group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">
                                    {{ item?.name }}
                                </td>
                                <td class="py-4 font-bold group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">
                                    <a :href="item?.link" target="_blank">{{ item?.link }}</a>
                                </td>
                                <td class="py-4 font-bold group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">
                                    {{ item?.address }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div v-else-if="itemx?.tabMenuValue?.length > 0">
                    <table class="w-full">
                        <tbody>
                            <tr v-for="(item, index) in JSON.parse(itemx?.tabMenuValue[0].json_value)" class="border-b border-terra-dark-gray text-white text-lg leading-relaxed tracking-wider group">
                                <td class="py-4 group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">{{ item?.key }}</td>
                                <td class="py-4 text-right font-bold group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">{{ item?.value }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div v-else-if="itemx?.tabMenuDetail?.length > 0">
                    <table class="w-full">
                        <tbody>
                            <tr v-for="(item, index) in JSON.parse(itemx?.tabMenuDetail[0].json_value)" class="border-b border-terra-dark-gray text-white text-lg leading-relaxed tracking-wider group">
                                <td class="py-4 group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">{{ item?.key }}</td>
                                <td class="py-4 text-right font-bold group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">{{ item?.value }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <table v-else class="w-full">
                    <tbody>
                        <tr class="border-b border-terra-dark-gray text-white text-lg leading-relaxed tracking-wider group">
                            <td class="py-4 group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">{{minInvesting}}</td>
                            <td class="py-4 text-right font-bold group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">€ 500.00</td>
                        </tr>
                        <tr class="border-b border-terra-dark-gray text-white text-lg leading-relaxed tracking-wider group">
                            <td class="py-4 group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">{{pricePerUnit}}</td>
                            <td class="py-4 text-right font-bold group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">500,00 €</td>
                        </tr>
                        <tr class="border-b border-terra-dark-gray text-white text-lg leading-relaxed tracking-wider group">
                            <td class="py-4 group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">{{availableShares}}</td>
                            <td class="py-4 text-right font-bold group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">15</td>
                        </tr>
                        <tr class="border-b border-terra-dark-gray text-white text-lg leading-relaxed tracking-wider group">
                            <td class="py-4 group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">{{totalUnits}}</td>
                            <td class="py-4 text-right font-bold group group-hover:text-xl cursor-default transition-all duration-300 delay-100 ease-in-out">1,500</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>

<script>
import {EventBus} from "../../app";
export default {
    name: "fundingdetailtabmenucomponent",
    props: {
        minInvesting:String,
        pricePerUnit:String,
        availableShares:String,
        totalUnits:String,
        textRepeaterName:String,
        textRepeaterLink:String,
        textRepeaterAddress:String,
    },
    data() {
        return {
            detailTabItem: [],
            detailTabIndex: 0,
            detailTabButton: null,
        }
    },
    mounted() {
        EventBus.$on('detailTabItem', ( value ) => {
            this.detailTabItem = value;
        });
    },
    created() {},

    methods: {
        functionOpenTab: function( index, tabId ) {
            this.detailTabIndex = index;
            this.detailTabButton = tabId;
        }
    }
}
</script>
