<style scoped>

</style>
<template>
        <div class="flex w-full mb-4">
            <h1 class="text-white text-xl lg:text-30p font-poppins font-bold">
                {{ detailTitleText ?? detailTitleItem }}
            </h1>
        </div>
</template>

<script>
import {EventBus} from "../../app";

export default {
    name: "fundingdetailtitlecomponent",
    props: {
        detailTitleText:String
    },
    data() {
        return {
            detailTitleItem: ''
        }
    },
    mounted() {
        EventBus.$on('detailTitleItem', ( value ) => {
            this.detailTitleItem = value;
        });
    },
    created() {},
    methods: {}
}
</script>
