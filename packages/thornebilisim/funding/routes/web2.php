<?php

use Illuminate\Support\Facades\Route;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

/** Project-specific Controller class files **/
use Thorne\Funding\Http\Controllers\HomeController;

Route::prefix('fund')->group(function () {
    Route::controller(HomeController::class)->group(function () {
        Route::get('/funding2', 'index2')->name('fund.funding2');
    });
});
