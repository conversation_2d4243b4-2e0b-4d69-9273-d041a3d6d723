<?php

namespace Thorne\Funding\Http\Controllers\Api;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Thorne\Funding\Http\Controllers\ApiController as ApiController;
use Webkul\Customer\Models\ComminicationPreferences;

class CustomerApiController extends ApiController
{
    public function __construct(
        protected ComminicationPreferences $comminicationPreferences
    ) {
        parent::__construct();
    }

    public function index( Request $request ) {

        $data = $this->comminicationPreferences->where([
            'customer_id' => $request->user()->id
        ]);

        if($data->first()) {
            if(isset($data->first()->email_send) && $data->first()->email_send == '1970-01-01') {
                $emailSend = false;
            } else {
                $emailSend = true;
            }
            if(isset($data->first()->phone_call) && $data->first()->phone_call == '1970-01-01') {
                $phoneCall = false;
            } else {
                $phoneCall = true;
            }
            if(isset($data->first()->sms_send) && $data->first()->sms_send == '1970-01-01') {
                $smsSend = false;
            } else {
                $smsSend = true;
            }
            return response()->json([
                'data' => [
                    'emailSend'  => $emailSend,
                    'phoneCall' => $phoneCall,
                    'smsSend'    => $smsSend,
                ],
            ]);
        } else {
            return response()->json([
                'data' => null,
            ]);
        }

    }

    public function customerPreferences( Request $request ) {

        $this->comminicationPreferences->updateOrCreate([
            'customer_id' => $request->user()->id,
        ],[
            'customer_id' => $request->user()->id,
            'sms_send' => $request->smsSend === true ? Carbon::now()->format('Y-m-d H:i:s'): '1970-01-01',
            'email_send' => $request->emailSend === true ? Carbon::now()->format('Y-m-d H:i:s'): '1970-01-01',
            'phone_call' => $request->phoneCall === true ? Carbon::now()->format('Y-m-d H:i:s'): '1970-01-01',
        ]);

        /*if($request->emailSend === true) {
            $this->comminicationPreferences->create([
                'customer_id' => $request->user()->id,
                'email_send' => Carbon::now()->format('Y-m-d H:i:s'),
            ]);
        } else {
            $this->comminicationPreferences->where([
                'customer_id' => $request->user()->id
            ])->update([
                'email_send' => null,
            ]);
        }
        if($request->smsSend === true) {
            $this->comminicationPreferences->create([
                'customer_id' => $request->user()->id,
                'sms_send' => Carbon::now()->format('Y-m-d H:i:s'),
            ]);
        } else {
            $this->comminicationPreferences->where([
                'customer_id' => $request->user()->id
            ])->update([
                'sms_send' => null,
            ]);
        }
        if($request->phoneCall === true) {
            $this->comminicationPreferences->create([
                'customer_id' => $request->user()->id,
                'phone_call' => Carbon::now()->format('Y-m-d H:i:s'),
            ]);
        } else {
            $this->comminicationPreferences->where([
                'customer_id' => $request->user()->id
            ])->update([
                'phone_call' => null,
            ]);
        }*/

    }

}
