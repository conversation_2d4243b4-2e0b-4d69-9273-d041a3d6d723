<?php

namespace Thorne\Funding\Repositories\Eloquent;

use Illuminate\Support\Facades\DB;
use Thorne\Funding\Models\Orders\OrderSubItems;
use Thorne\Funding\Models\Attributes\AttributeGroupItems;
use Thorne\Funding\Models\Products\ProductAttributeValueItems;
use Thorne\Funding\Models\Products\ProductFlatItems;

/** Project-specific Models class files **/


class ProductRepository
{
    public function __construct(
        protected OrderSubItems  $orderSubItems,
        protected ProductFlatItems $productFlatItems,
        protected AttributeGroupItems $attributeGroupItems,
        protected ProductAttributeValueItems $attributeValueItems,
    ) {}

    public function valueAttributeArray( $id = null ): mixed {

        $data = $this->attributeValueItems
                     ->where(['product_id' => $id])
                     ->get();

        return collect($data);

    }

    public function investmentMade( $product_id = null ): mixed {

        $itemOrder = $this->orderSubItems?->selectRaw('SUM(total) as total')
            ?->with('products')
            ?->whereHas('products', function ($q) use ($product_id) {
                $q->where('parent_id', $product_id);
            })
            ?->first();
        return $itemOrder->total;

    }

    public function monthsMenuButton($product_id = null): mixed
    {
        $data = $this->attributeValueItems
            ->select(DB::Raw(
                "product_id, CAST(text_value AS UNSIGNED) AS months,\n
                        (SELECT
                            pav.text_value
                         FROM product_attribute_values AS pav
                         WHERE pav.product_id = product_attribute_values.product_id AND pav.attribute_id = 22
                         LIMIT 1
                        ) AS pa,\n
                        (SELECT
                            x.boolean_value
                         FROM product_attribute_values AS x
                         WHERE x.product_id = product_attribute_values.product_id AND x.boolean_value = true AND x.attribute_id = 8
                         LIMIT 1
                        ) AS status
                        "
            ))
            ->with('productFlats')
            ->whereHas('productFlats', function ($q) use ($product_id) {
                $q->where('parent_id', $product_id);
            })
            ->where([
                'attribute_id' => 2,
                'locale' => app()->getLocale(),
                'channel' => getCurrentChannel('string')->code,
            ])->get()->makeHidden('productFlats');

        $array = [];
        foreach ($data as $item) {
            if($item->status !== null) {
                $array[] = [
                    'months'     => $item->months,
                    'pa'         => $item->pa,
                    'product_id' => $item->product_id,
                ];
            }
        }

        return $array;

    }

    public function tabMenuButton($product_id = null) {
        $tabButton = [];
        $data = $this->attributeGroupItems
            ->select('id', 'code', 'name', 'icon')
            ->where([
                'is_tab' => true,
                'attribute_family_id' => 2
            ])
            ->with('attributeGroupMappings')
            ->orderBy('position', 'ASC')
            ->get();
        if ($data) {
            foreach ($data as $key => $value) {
                if($value?->attributeGroupMappings?->attribute_id === 28) {
                    $tabButton[] = [
                        'name' => $value->name,
                        'icon' => $value->icon,
                        'fundingFiles' => $this->tabMenuButtonSubFunction($product_id, $value?->attributeGroupMappings?->attribute_id),
                    ];
                } elseif($value?->attributeGroupMappings?->attribute_id === 37) {
                    $tabButton[] = [
                        'name' => $value->name,
                        'icon' => $value->icon,
                        'fundingTextarea' => $this->tabMenuButtonSubFunction($product_id, $value?->attributeGroupMappings?->attribute_id),
                    ];
                } elseif($value?->attributeGroupMappings?->attribute_id === 70) {
                    $tabButton[] = [
                        'name' => $value->name,
                        'icon' => $value->icon,
                        'textRepeater' => $this->tabMenuButtonSubFunction($product_id, $value?->attributeGroupMappings?->attribute_id),
                    ];
                } elseif($value?->attributeGroupMappings?->attribute_id === 71) {
                    $tabButton[] = [
                        'name' => $value->name,
                        'icon' => $value->icon,
                        'tabMenuValue' => $this->tabMenuButtonSubFunction($product_id, $value?->attributeGroupMappings?->attribute_id),
                    ];
                } elseif($value?->attributeGroupMappings?->attribute_id === 72) {
                    $tabButton[] = [
                        'name' => $value->name,
                        'icon' => $value->icon,
                        'tabMenuDetail' => $this->tabMenuButtonSubFunction($product_id, $value?->attributeGroupMappings?->attribute_id),
                    ];
                } else {
                    $tabButton[] = [
                        'name' => $value->name,
                        'icon' => $value->icon,
                    ];
                }
            }
        }

        return $tabButton;
    }

    public function tabMenuButtonSubFunction($product_id = null, $attribute_id = null): mixed {

        return $this->attributeValueItems
            ->select('id', 'text_value', 'json_value')
            ->where([
                'product_id' => $product_id,
                'attribute_id' => $attribute_id,
                'locale' => app()->getLocale(),
            ])->get();

    }

    public function firstProduct($funding = null): mixed
    {

        return $this->productFlatItems
            ->select(
                'id',
                'sku',
                'name',
                'price',
                'weight',
                'url_key',
                'min_price',
                'max_price',
                'product_id',
                'description',
                'product_number'
            )
            ->with('productImages')
            ->where([
                'url_key' => $funding,
                'status' => true,
                'locale' => app()->getLocale(),
            ])
            ->first();
    }

}   /** end class ProductFlatRepository **/
