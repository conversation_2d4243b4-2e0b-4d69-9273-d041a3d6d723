<?php

namespace Thorne\Funding\Models\Traits;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

trait GlobalFilters
{
    public function scopeFilter($builder, $request)
    {

        if ($request->all()) {
            foreach ($request->all() as $key => $value) {
                $clomnType = Schema::getColumnType($this->table, $key);
                if ($clomnType == 'float') {
                    $par = explode(',', $value);
                    $builder->whereBetween(DB::raw("FORMAT($key, 2)"), [number_format($par[0], 2, '.', ','), number_format($par[1], 2, '.', ',')]);
                } elseif ($clomnType == 'decimal') {
                    $par = explode(',', $value);
                    $builder->whereBetween(DB::raw("FORMAT($key, 4)"), [number_format($par[0], 4, '.', ','), number_format($par[1], 2, '.', ',')]);
                } elseif ($clomnType == 'integer') {
                    $builder->where($key, '=', $value);
                } else {
                    $builder->where($key, '=', $value);
                }
            }
        }

        return $builder;
    }
}   /** end trait GlobalFilters **/
