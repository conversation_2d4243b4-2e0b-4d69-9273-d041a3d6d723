<?php

namespace Thorne\Funding\Models\Orders;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OrderPayments extends Model {

    use HasFactory, Notifiable;

    protected $connection = 'mysql';
    protected $primaryKey = 'id';
    protected $table      = 'order_payment';
    protected $guarded    = [];
    protected $appends    = [];

    protected $casts      = [
        'created_at'        => 'datetime:d-m-Y H:i:s',
        'updated_at'        => 'datetime:d-m-Y H:i:s',
    ];

    /** ------------------------------------------------------------------------------------------------------------ **/
    /**  -------------------------- Models Attribute Area ---------------------------------------------------------- **/
    /**  ----------------------------------------------------------------------------------------------------------- **/

    /** ------------------------------------------------------------------------------------------------------------ **/
    /**  -------------------------- Models Scope Area -------------------------------------------------------------- **/
    /**  ----------------------------------------------------------------------------------------------------------- **/

    /** ------------------------------------------------------------------------------------------------------------ **/
    /**  -------------------------- Models Relationships Area ------------------------------------------------------ **/
    /**  ----------------------------------------------------------------------------------------------------------- **/

}   /** end class OrderPayments extends Model **/
