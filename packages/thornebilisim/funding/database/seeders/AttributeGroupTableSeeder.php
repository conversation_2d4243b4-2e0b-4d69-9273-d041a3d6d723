<?php

namespace Thorne\Funding\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AttributeGroupTableSeeder extends Seeder
{
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        DB::table('attribute_groups')->insertOrIgnore([
            [
                'id' => '6',
                'name' => 'Funding Family',
                'code' => 'funding_family',
                'position' => '1',
                'is_tab' => '0',
                'is_rich_editable' => '0',
                'is_user_defined' => '0',
                'attribute_family_id' => '2',
                'icon' => null,
            ], [
                'id' => '7',
                'name' => 'Values',
                'code' => 'values',
                'position' => '2',
                'is_tab' => '1',
                'is_rich_editable' => '0',
                'is_user_defined' => '1',
                'attribute_family_id' => '2',
                'icon' => '<svg width="34.546" height="34.555" viewBox="0 0 34.546 34.555">\r\n<g id="Group_7084" data-name="Group 7084" transform="translate(0)">\r\n<g id="Group_7081" data-name="Group 7081" transform="translate(0 0)">\r\n<path id="Path_1873" data-name="Path 1873" d="M29.823,34.555H23.346a.729.729,0,0,0-.15-.062,5.345,5.345,0,0,1-4.145-3.22,5.883,5.883,0,0,1-.427-2.733c.007-.2,0-.392,0-.582h-2.7c0,.407,0,.8,0,1.188a5.4,5.4,0,0,1-4.044,5.245c-.225.059-.452.11-.677.164H4.725a1.015,1.015,0,0,0-.184-.066A5.392,5.392,0,0,1,0,29.2Q0,26.606,0,24.009a5.382,5.382,0,0,1,5.38-5.379h1.21v-2.7c-.447,0-.873,0-1.3,0A5.391,5.391,0,0,1,0,10.629Q0,7.964,0,5.3A5.386,5.386,0,0,1,5.3.006C7.075,0,8.851-.006,10.627.013a5.278,5.278,0,0,1,1.294.178,5.359,5.359,0,0,1,4,5.242c0,.389,0,.778,0,1.157h2.7c0-.42,0-.813,0-1.206A5.382,5.382,0,0,1,24,.005q2.58,0,5.16,0a5.4,5.4,0,0,1,5.228,4.076c.054.215.1.431.155.646v6.478a.731.731,0,0,0-.062.15,5.342,5.342,0,0,1-3.22,4.146,5.9,5.9,0,0,1-2.733.428c-.2-.008-.392,0-.581,0v2.7h1.222a5.4,5.4,0,0,1,5.219,4.077c.054.214.1.43.155.645v6.478a.777.777,0,0,0-.062.15,5.332,5.332,0,0,1-3.952,4.4c-.233.071-.472.119-.708.178M15.956,25.219h2.669c0-.428,0-.831,0-1.235a5.243,5.243,0,0,1,1.908-4.074,5.176,5.176,0,0,1,3.494-1.28c.4,0,.8,0,1.191,0v-2.7H24.063a5.381,5.381,0,0,1-5.438-5.455q0-.567,0-1.134h-2.7c0,.439.008.854,0,1.268a5.267,5.267,0,0,1-2.172,4.251,5.14,5.14,0,0,1-3.231,1.07c-.4,0-.8,0-1.189,0V18.6c.7.034,1.387.022,2.061.108a5.335,5.335,0,0,1,4.459,4.476c.085.665.073,1.342.106,2.038m-6.645-12c.48,0,.928.027,1.372-.005a2.677,2.677,0,0,0,2.536-2.659q.036-2.6,0-5.195a2.664,2.664,0,0,0-2.643-2.654q-2.613-.043-5.227,0A2.666,2.666,0,0,0,2.707,5.367c-.025,1.731-.021,3.464,0,5.2a2.6,2.6,0,0,0,2.012,2.554,13.129,13.129,0,0,0,1.9.158v-.439c0-1.214,0-2.429,0-3.643a1.347,1.347,0,1,1,2.694,0c0,1.181,0,2.361,0,3.542v.487M21.282,6.618h.445q1.788,0,3.575,0a1.349,1.349,0,1,1,.01,2.7q-1.8,0-3.609,0h-.381c0,.444-.01.847,0,1.249a2.685,2.685,0,0,0,2.644,2.657q2.613.045,5.227,0a2.656,2.656,0,0,0,2.641-2.62q.051-2.63,0-5.262a2.655,2.655,0,0,0-2.632-2.628c-1.742-.03-3.485-.024-5.227,0a2.62,2.62,0,0,0-2.559,2.052,14.094,14.094,0,0,0-.137,1.857M13.227,25.243c0-.4,0-.752,0-1.1a2.72,2.72,0,0,0-2.835-2.815q-2.428-.007-4.857,0A2.718,2.718,0,0,0,2.7,24.177Q2.695,26.589,2.7,29a2.723,2.723,0,0,0,2.863,2.855c1.608,0,3.216.006,4.823,0a2.978,2.978,0,0,0,.831-.1c1.838-.553,2.2-2.125,1.952-3.808-1.325,0-2.651,0-3.978,0a1.347,1.347,0,1,1,0-2.694c1.046,0,2.091,0,3.137,0h.9m12.009-3.916c-.419,0-.789,0-1.159,0a2.711,2.711,0,0,0-2.754,2.759q-.016,2.513,0,5.026a2.7,2.7,0,0,0,2.736,2.741q2.529.021,5.059,0a2.693,2.693,0,0,0,2.725-2.717c.017-1.7.009-3.4,0-5.094a2.675,2.675,0,0,0-.548-1.631c-.867-1.151-2.09-1.146-3.364-1.059v.392c0,1.214,0,2.429,0,3.643a1.347,1.347,0,0,1-2.693-.011c0-.585,0-1.169,0-1.754V21.327" transform="translate(0 0)" fill="#fff"/>\r\n<path id="Path_1874" data-name="Path 1874" d="M83.343,82.035a1.348,1.348,0,1,1-1.324-1.363,1.334,1.334,0,0,1,1.324,1.363" transform="translate(-64.721 -64.741)" fill="#fff"/>\r\n</g>\r\n</g>\r\n</svg>',
            ], [
                'id' => '8',
                'name' => 'Details',
                'code' => 'details',
                'position' => '3',
                'is_tab' => '1',
                'is_rich_editable' => '0',
                'is_user_defined' => '1',
                'attribute_family_id' => '2',
                'icon' => '<svg id="Group_7082" data-name="Group 7082" height="33.153" viewBox="0 0 33.147 33.153">\r\n<path id="Path_1875" data-name="Path 1875" d="M16.581,0c2.232,0,4.465-.005,6.7,0a9.908,9.908,0,0,1,9.766,8.56,24.024,24.024,0,0,1,.087,3.17A1.068,1.068,0,0,1,32,12.706a1.093,1.093,0,0,1-1.057-1.062c-.022-.828,0-1.659-.065-2.482a7.66,7.66,0,0,0-5.916-6.736,9.018,9.018,0,0,0-1.844-.207q-6.541-.026-13.083-.007A7.7,7.7,0,0,0,2.393,8.348a7.906,7.906,0,0,0-.177,1.675q-.021,6.543-.006,13.085a7.7,7.7,0,0,0,6.128,7.644,8.037,8.037,0,0,0,1.709.181q6.524.022,13.049.006a7.7,7.7,0,0,0,7.659-6.153,8.552,8.552,0,0,0,.174-1.745c.021-1.968.006-3.936.009-5.9a1.1,1.1,0,0,1,2.148-.358,1.308,1.308,0,0,1,.056.405c0,2.049.01,4.1,0,6.145a9.91,9.91,0,0,1-7.528,9.518,11.016,11.016,0,0,1-2.456.287c-4.384.027-8.768.015-13.152.012A9.931,9.931,0,0,1,.095,24.623,9.693,9.693,0,0,1,0,23.178Q-.005,16.584,0,9.99A9.934,9.934,0,0,1,9.919.008C12.139-.006,14.36,0,16.581,0" transform="translate(0 0)" fill="#fff"/>\r\n<path id="Path_1876" data-name="Path 1876" d="M58.406,49.35q2.9,0,5.8,0a1.1,1.1,0,0,1,.31,2.157,1.435,1.435,0,0,1-.407.047q-5.714,0-11.428,0a1.106,1.106,0,1,1-.006-2.206q2.866,0,5.731,0" transform="translate(-38.228 -36.64)" fill="#fff"/>\r\n<path id="Path_1877" data-name="Path 1877" d="M29.947,30.105q-2.572,0-5.144,0a1.108,1.108,0,1,1,.012-2.206H35.1a1.108,1.108,0,1,1-.012,2.207H29.947" transform="translate(-17.524 -20.713)" fill="#fff"/>\r\n<path id="Path_1878" data-name="Path 1878" d="M67.714,94.465q-2.158,0-4.316,0a1.106,1.106,0,1,1,.011-2.206q4.333,0,8.665,0a1.106,1.106,0,1,1-.01,2.206q-2.175,0-4.35,0" transform="translate(-46.192 -68.499)" fill="#fff"/>\r\n<path id="Path_1879" data-name="Path 1879" d="M71.456,73.012q-1.882,0-3.763,0a1.106,1.106,0,1,1,0-2.206q3.78,0,7.561,0a1.106,1.106,0,1,1,0,2.206q-1.9,0-3.8,0" transform="translate(-49.377 -52.57)" fill="#fff"/>\r\n<path id="Path_1880" data-name="Path 1880" d="M88.293,30.1c-.46,0-.921,0-1.381,0a1.1,1.1,0,1,1,0-2.2q1.381-.009,2.762,0a1.1,1.1,0,1,1,0,2.2c-.46.006-.921,0-1.381,0" transform="translate(-63.711 -20.705)" fill="#fff"/>\r\n<path id="Path_1881" data-name="Path 1881" d="M36.8,73c-.46,0-.921.006-1.381,0a1.1,1.1,0,0,1,0-2.2q1.4-.012,2.8,0a1.1,1.1,0,0,1,0,2.2c-.472.008-.944,0-1.415,0" transform="translate(-25.487 -52.562)" fill="#fff"/>\r\n</svg>',
            ], [
                'id' => '9',
                'name' => 'Permissions',
                'code' => 'permissions',
                'position' => '4',
                'is_tab' => '1',
                'is_rich_editable' => '0',
                'is_user_defined' => '1',
                'attribute_family_id' => '2',
                'icon' => '<svg width="30.208" height="30.259" viewBox="0 0 30.208 30.259">\r\n<g id="Group_7086" data-name="Group 7086" transform="translate(-0.001 0.001)">\r\n<g id="Group_7083" data-name="Group 7083" transform="translate(0.001 -0.001)">\r\n<path id="Path_3544" data-name="Path 3544" d="M4.088,30.258a1.84,1.84,0,0,1-1.234-1.172c-.047-.118-.124-.224-.168-.343a.859.859,0,0,0-.665-.608,1.278,1.278,0,0,1-.938-.975C.722,25.772.364,24.383.039,22.988A1.513,1.513,0,0,1,1.59,21.172c1.772-.008,3.544,0,5.318,0H7.28a1.973,1.973,0,0,1,.4-1.516,1.758,1.758,0,0,1,1.46-.5c-.143-.551-.279-1.07-.415-1.59-.823-3.144-1.685-6.277-2.46-9.432A6.714,6.714,0,0,1,17.364,1.761c.234.206.385.209.572-.041a1.476,1.476,0,0,0,.2-.452A1.714,1.714,0,0,1,20.994.49c.493.464.775.462,1.284-.01a1.715,1.715,0,0,1,2.849.8c.174.608.453.769,1.076.618A1.718,1.718,0,0,1,28.311,4c-.154.646,0,.9.636,1.088a1.719,1.719,0,0,1,.787,2.839,1.546,1.546,0,0,0-.323.448.738.738,0,0,0,.272.811,1.714,1.714,0,0,1-.758,2.888c-.617.173-.767.446-.616,1.075a1.718,1.718,0,0,1-2.131,2.1c-.592-.145-.885.026-1.051.616a1.713,1.713,0,0,1-2.885.775.744.744,0,0,0-1.22,0,1.713,1.713,0,0,1-2.895-.8.755.755,0,0,0-.564-.623c-.343,1.305-.685,2.607-1.036,3.943a1.745,1.745,0,0,1,1.48.519,1.949,1.949,0,0,1,.377,1.5h.373q2.6,0,5.2,0a1.545,1.545,0,0,1,1.627,2.068q-.467,1.887-.943,3.772a1.54,1.54,0,0,1-1.2,1.206.572.572,0,0,0-.3.232,3.21,3.21,0,0,0-.28.552,2.038,2.038,0,0,1-1.227,1.259ZM16.474,15.334l-.3-.083a1.706,1.706,0,0,1-1.217-2.087c.16-.641.007-.912-.623-1.1a1.713,1.713,0,0,1-.765-2.873c.446-.467.443-.767,0-1.235A1.675,1.675,0,0,1,13.2,6.077,1.714,1.714,0,0,1,14.44,5.043a.686.686,0,0,0,.555-.884,1.73,1.73,0,0,1,.89-2.171c.012-.007.012-.033.026-.07A5.586,5.586,0,0,0,12,1.082a5.688,5.688,0,0,0-4.68,7.065c.685,2.7,1.413,5.394,2.121,8.092q.38,1.449.761,2.9h5.276c.335-1.274.666-2.534,1-3.806m7.561,9.877c.207-.822.424-1.607.589-2.4a.694.694,0,0,0-.223-.518c-.1-.093-.312-.083-.475-.083q-5.776-.006-11.551,0H1.68c-.547,0-.733.235-.6.761q.449,1.816.907,3.629c.143.57.239.639.838.639H22.85a2.634,2.634,0,0,0,.325-.009.492.492,0,0,0,.45-.369c.055-.2.1-.394.152-.621H7.057a2.029,2.029,0,0,1-.353-.012.505.505,0,0,1-.422-.528.475.475,0,0,1,.446-.469,3.178,3.178,0,0,1,.383-.013H24.035Zm5.16-14.91a2.183,2.183,0,0,0-.281-.446,1.724,1.724,0,0,1-.006-2.549.709.709,0,0,0-.346-1.275A1.706,1.706,0,0,1,27.3,3.852c.177-.69-.26-1.122-.953-.941a1.707,1.707,0,0,1-2.167-1.247.711.711,0,0,0-1.286-.359,1.711,1.711,0,0,1-2.524,0c-.51-.5-1.09-.339-1.283.361a1.6,1.6,0,0,1-1.244,1.247,3.2,3.2,0,0,1-1.075-.038.706.706,0,0,0-.831.869,3,3,0,0,1,.082.373A1.7,1.7,0,0,1,14.7,6.034a.706.706,0,0,0-.379,1.236A1.719,1.719,0,0,1,14.3,9.9.7.7,0,0,0,14.63,11.1a1.715,1.715,0,0,1,1.309,2.3.684.684,0,0,0,.156.657.789.789,0,0,0,.867.18,1.694,1.694,0,0,1,2.13,1.263.706.706,0,0,0,1.27.352,1.712,1.712,0,0,1,2.545,0,.708.708,0,0,0,1.266-.331,1.708,1.708,0,0,1,2.221-1.277.71.71,0,0,0,.92-.912,2.211,2.211,0,0,1-.08-.667,1.682,1.682,0,0,1,1.347-1.556.774.774,0,0,0,.615-.813M3.6,28.262a3.664,3.664,0,0,1,.149.335.933.933,0,0,0,1.054.667c5.394-.02,10.789-.012,16.183-.012a.967.967,0,0,0,.294-.007.812.812,0,0,0,.4-.209,6.314,6.314,0,0,0,.412-.773Zm13.751-7.109c.1-.791-.06-.974-.81-.974H9.1c-.108,0-.217,0-.324.008a.456.456,0,0,0-.452.426,4.693,4.693,0,0,0,0,.539Z" transform="translate(0 0)" fill="#fff"/>\r\n<path id="Path_3545" data-name="Path 3545" d="M12.449,1.97a1.992,1.992,0,0,1,.207,0,.52.52,0,0,1,.476.511.492.492,0,0,1-.463.488A3.805,3.805,0,0,0,9.676,4.441a.484.484,0,0,1-.71.07.5.5,0,0,1-.052-.734A4.654,4.654,0,0,1,12.449,1.97" transform="translate(0.214 0.048)" fill="#fff"/>\r\n<path id="Path_3546" data-name="Path 3546" d="M7.945,7.009c.007-.365.186-.575.437-.6a.505.505,0,0,1,.577.423c.049.239.088.48.124.722a.5.5,0,0,1-.389.572.478.478,0,0,1-.6-.344c-.077-.282-.116-.574-.154-.77" transform="translate(0.193 0.157)" fill="#fff"/>\r\n<path id="Path_3547" data-name="Path 3547" d="M4.423,25.656a2.492,2.492,0,0,1-.458-.084.495.495,0,0,1,.113-.938A3.275,3.275,0,0,1,4.7,24.62a.48.48,0,0,1,.486.517.507.507,0,0,1-.489.494c-.088.008-.177,0-.265,0l0,.024" transform="translate(0.089 0.6)" fill="#fff"/>\r\n<path id="Path_3548" data-name="Path 3548" d="M15.7,8.5a5.551,5.551,0,1,1,5.55,5.556A5.549,5.549,0,0,1,15.7,8.5m10.08,0a4.536,4.536,0,1,0-4.514,4.542A4.539,4.539,0,0,0,25.782,8.5" transform="translate(0.382 0.072)" fill="#fff"/>\r\n<path id="Path_3549" data-name="Path 3549" d="M20.6,9.558q.678-1.069,1.356-2.138c.252-.4.5-.8.759-1.194.207-.322.473-.406.743-.243a.518.518,0,0,1,.125.77q-1.211,1.921-2.431,3.835c-.25.392-.561.423-.9.1q-.7-.661-1.381-1.333a.511.511,0,1,1,.7-.733c.313.327.6.677.9,1.016l.134-.08" transform="translate(0.454 0.144)" fill="#fff"/>\r\n</g>\r\n</g>\r\n</svg>',
            ], [
                'id' => '10',
                'name' => 'Documents',
                'code' => 'documents',
                'position' => '5',
                'is_tab' => '1',
                'is_rich_editable' => '0',
                'is_user_defined' => '1',
                'attribute_family_id' => '2',
                'icon' => '<svg width="29.835" height="27.972" viewBox="0 0 29.835 27.972">\r\n<g id="Group_7089" data-name="Group 7089" transform="translate(0 0)">\r\n<path id="Path_3550" data-name="Path 3550" d="M29.835,6.65c-.118.467-.2.946-.362,1.4A4.967,4.967,0,0,1,28.251,9.9q-6.994,6.991-13.986,13.984c-.345.345-.67.4-.982.183a.646.646,0,0,1-.179-.9,2.119,2.119,0,0,1,.281-.331c4.571-4.573,9.111-9.179,13.732-13.7a4.425,4.425,0,0,0-.112-6.422,4.649,4.649,0,0,0-3.069-1.358,3.964,3.964,0,0,0-3.113,1.244Q14.273,9.174,7.708,15.729c-1.8,1.8-3.567,3.629-5.405,5.387a3.137,3.137,0,0,0,.167,4.574,3.082,3.082,0,0,0,3.491.655,2.838,2.838,0,0,0,.842-.608q7.548-7.528,15.085-15.068a1.823,1.823,0,0,0,.18-2.432,1.909,1.909,0,0,0-2.442-.65,2.559,2.559,0,0,0-.514.423Q13.958,13.156,8.806,18.3a1.491,1.491,0,0,1-.578.351.555.555,0,0,1-.625-.263.607.607,0,0,1-.053-.685A1.476,1.476,0,0,1,7.8,17.39Q13.01,12.176,18.221,6.965a2.951,2.951,0,0,1,3.312-.754,3.28,3.28,0,0,1,2.245,2.762,3,3,0,0,1-.953,2.68c-4.995,4.988-10,9.96-14.964,14.983a4.484,4.484,0,0,1-6.49-.111,4.477,4.477,0,0,1-1.125-4.7,3.96,3.96,0,0,1,1.03-1.6Q10.585,10.92,19.89,1.606a5.633,5.633,0,0,1,7.391-.43,6.069,6.069,0,0,1,2.508,4.2.442.442,0,0,0,.046.108Z" transform="translate(0 0)" fill="#fff"/>\r\n</g>\r\n</svg>',
            ], [
                'id' => '11',
                'name' => 'General',
                'code' => 'general',
                'position' => '6',
                'is_tab' => '0',
                'is_rich_editable' => '0',
                'is_user_defined' => '0',
                'attribute_family_id' => '2',
                'icon' => null,
            ], [
                'id' => '12',
                'name' => 'Description',
                'code' => 'description',
                'position' => '7',
                'is_tab' => '0',
                'is_rich_editable' => '0',
                'is_user_defined' => '0',
                'attribute_family_id' => '2',
                'icon' => null,
            ], [
                'id' => '13',
                'name' => 'Meta Description',
                'code' => 'meta-description',
                'position' => '8',
                'is_tab' => '0',
                'is_rich_editable' => '0',
                'is_user_defined' => '0',
                'attribute_family_id' => '2',
                'icon' => null,
            ],
        ]);

        DB::table('attribute_group_mappings')->insertOrIgnore([
            [
                'attribute_id' => '1',
                'attribute_group_id' => '11',
                'position' => '1',
            ], [
                'attribute_id' => '2',
                'attribute_group_id' => '11',
                'position' => '3',
            ], [
                'attribute_id' => '3',
                'attribute_group_id' => '11',
                'position' => '4',
            ], [
                'attribute_id' => '4',
                'attribute_group_id' => '11',
                'position' => '5',
            ], [
                'attribute_id' => '5',
                'attribute_group_id' => '11',
                'position' => '6',
            ], [
                'attribute_id' => '6',
                'attribute_group_id' => '11',
                'position' => '7',
            ], [
                'attribute_id' => '7',
                'attribute_group_id' => '11',
                'position' => '8',
            ], [
                'attribute_id' => '8',
                'attribute_group_id' => '11',
                'position' => '10',
            ], [
                'attribute_id' => '9',
                'attribute_group_id' => '12',
                'position' => '1',
            ], [
                'attribute_id' => '10',
                'attribute_group_id' => '12',
                'position' => '2',
            ], [
                'attribute_id' => '16',
                'attribute_group_id' => '13',
                'position' => '1',
            ], [
                'attribute_id' => '17',
                'attribute_group_id' => '13',
                'position' => '2',
            ], [
                'attribute_id' => '18',
                'attribute_group_id' => '13',
                'position' => '3',
            ], [
                'attribute_id' => '26',
                'attribute_group_id' => '11',
                'position' => '9',
            ], [
                'attribute_id' => '27',
                'attribute_group_id' => '11',
                'position' => '2',
            ], [
                'attribute_id' => '28',
                'attribute_group_id' => '10',
                'position' => '1',
            ], [
                'attribute_id' => '29',
                'attribute_group_id' => '6',
                'position' => '1',
            ], [
                'attribute_id' => '30',
                'attribute_group_id' => '6',
                'position' => '2',
            ], [
                'attribute_id' => '32',
                'attribute_group_id' => '6',
                'position' => '3',
            ], [
                'attribute_id' => '33',
                'attribute_group_id' => '6',
                'position' => '4',
            ], [
                'attribute_id' => '34',
                'attribute_group_id' => '6',
                'position' => '5',
            ], [
                'attribute_id' => '36',
                'attribute_group_id' => '6',
                'position' => '6',
            ], [
                'attribute_id' => '11',
                'attribute_group_id' => '6',
                'position' => '7',
            ], [
                'attribute_id' => '22',
                'attribute_group_id' => '6',
                'position' => '8',
            ], [
                'attribute_id' => '37',
                'attribute_group_id' => '9',
                'position' => '7',
            ], [
                'attribute_id' => '38',
                'attribute_group_id' => '6',
                'position' => '9',
            ], [
                'attribute_id' => '39',
                'attribute_group_id' => '6',
                'position' => '10',
            ], [
                'attribute_id' => '40',
                'attribute_group_id' => '6',
                'position' => '11',
            ], [
                'attribute_id' => '41',
                'attribute_group_id' => '6',
                'position' => '12',
            ],
        ]);

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

    }
}
