
@if(\Illuminate\Support\Facades\Route::getCurrentRoute()->uri() == '/')
<div class="hidden">
    <div class="absolute w-full top-[10px] lg:top-1/4 left-[10px] lg:left-20 right-0 px-4 h-full ">
        <div class="flex flex-row lg:!flex-col items-end lg:items-start pb-10 lg:pb-0 justify-center lg:justify-start h-full  w-full lg:w-1/2 space-x-3 space-y-3">
            <div>
                <div class="text-left lg:text-center font-kiralabunuthin font-bold text-lg lg:text-4xl text-white font-bold drop-shadow-lg shadow-black tracking-wider">Gayrimenku<PERSON> Sahib<PERSON>lman<PERSON>n</div>
                <div class="text-left lg:text-center font-kiralabunuthin font-bold text-lg lg:text-4xl text-white font-bold drop-shadow-lg shadow-black tracking-wider">Dijital Yolu</div>
            </div>
            <button class="hidden lg:flex py-2 lg:py-3 px-4 lg:px-12 mt-10 text-sm lg:text-base font-kiralabunuthin font-bold text-black lg:text-terra-khaki-green bg-white bg-opacity-30 rounded-full border-3 border-terra-khaki-green focus:ring-transparent hover:bg-opacity-50" type="button">BAŞLAYIN</button></div>
    </div>
</div>
@include('subscriber')
<section class="py-15 bg-off-white-2 my-10">
    <div class="container py-8">
        <div class="flex flex-wrap justify-around space-y-8 lg:space-y-0 px-5 lg:px-0">
            <div class="rounded-[59px] bg-white pb-2 w-full lg:w-3/12 flex flex-col items-center justify-between drop-shadow-fundshadow hover:drop-shadow-purpleshadow transition-all duration-700 cursor-default">
                    <img class="rounded-t-[59px] grayscale" src="/deneme/first.png" alt="" style="filter: drop-shadow(rgba(0, 0, 0, 0.16) 0px 3px 6px);">

                <div class="flex h-full w-full flex-column">
                    <div class="pl-4 lg:pl-6 py-4">
                        <div class="font-terramirum text-2xl mt-3">
                            CityFUND
                        </div>
                        <div class="font-terramirum font-bold text-xl text-[#8472AB]">
                            Istanbul-Turkey
                        </div>
                    </div>
                    <div class="font-terramirum text-xl flex flex-row items-center space-x-2 py-4 border-t-1p border-bordergray pl-4 lg:pl-6 group">
                        <svg id="Group_2835" data-name="Group 2835" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="35" height="40" class="transition-all duration-300 ease-in-out group-hover:scale-125" viewBox="0 0 48.388 54.986">
                            <g id="Group_1982" data-name="Group 1982">
                                <path id="Path_1592" data-name="Path 1592" d="M25.524,344.234c-1.643-.433-3.29-.851-4.928-1.3-2.366-.652-4.732-1.308-7.086-2a3.654,3.654,0,0,0-3.027.354c-.226.133-.457.259-.693.374-.515.251-.955.487-.88,1.228.052.511-.4.841-.936.829q-3.49-.079-6.98-.185c-.7-.022-1.013-.4-.993-1.155q.109-4.167.229-8.334c.063-2.253.131-4.5.19-6.757.026-.971.305-1.252,1.25-1.227q3.237.086,6.474.168c.958.024,1.258.354,1.221,1.333-.012.33,0,.661,0,1.012.788-.272,1.532-.571,2.3-.786a11.518,11.518,0,0,1,8.014.641,30.205,30.205,0,0,0,3.078,1.2,6.3,6.3,0,0,0,1.672.165c1.782.062,3.564.129,5.347.148a5.882,5.882,0,0,1,5.264,2.793.863.863,0,0,0,.1.135.765.765,0,0,0,.135.081l5.308-2.5c.577-.272,1.151-.548,1.729-.816a4.446,4.446,0,0,1,5.879,1.726c.059.092.133.174.2.261v.563a4.611,4.611,0,0,1-.637.611Q40.191,337.71,32.61,342.6a10.19,10.19,0,0,1-2.275,1.061,24.278,24.278,0,0,1-2.559.574H25.524M9.069,339.928a.521.521,0,0,0,.147,0,1.6,1.6,0,0,0,.253-.121,5.785,5.785,0,0,1,4.972-.493c3.3.981,6.618,1.934,9.951,2.812a8.925,8.925,0,0,0,7.391-1.1q6.925-4.472,13.843-8.954c.17-.11.336-.226.537-.361a3.151,3.151,0,0,0-.242-.21,2.909,2.909,0,0,0-3.091-.151c-2,.95-3.981,1.929-6,2.826-.713.316-1.109.629-.971,1.5.113.715-.32,1.076-1.039,1.056q-5.15-.14-10.3-.291a1.657,1.657,0,0,1-.6-.113.807.807,0,0,1-.5-.964.792.792,0,0,1,.771-.7c.187-.011.375-.01.563,0l8.611.237c.2.005.394,0,.6,0a3.905,3.905,0,0,0-3.845-3.158c-1.65-.063-3.3-.139-4.953-.129a13.326,13.326,0,0,1-6.155-1.481,9.5,9.5,0,0,0-9.27.364.818.818,0,0,0-.486.788c-.054,2.741-.141,5.481-.214,8.221,0,.141.018.283.029.427M1.817,341.8H7.174c.126-4.57.253-9.154.382-13.829H2.2L1.817,341.8" transform="translate(0 -289.248)" fill="#8472ab"/>
                                <path id="Path_1593" data-name="Path 1593" d="M116.6,106.4a13.081,13.081,0,1,1-13.05,12.688A13.05,13.05,0,0,1,116.6,106.4m.056,1.8a11.284,11.284,0,1,0,11.219,11.547A11.239,11.239,0,0,0,116.652,108.2" transform="translate(-91.854 -94.389)" fill="#8472ab"/>
                                <path id="Path_1594" data-name="Path 1594" d="M213.105,5.18c0,1.35,0,2.7,0,4.049,0,.736-.313,1.121-.882,1.121s-.9-.4-.9-1.116q-.006-4.077,0-8.154c0-.846.679-1.347,1.308-.931a1.3,1.3,0,0,1,.452.926c.049,1.367.021,2.736.021,4.1" transform="translate(-187.463 0)" fill="#8472ab"/>
                                <path id="Path_1595" data-name="Path 1595" d="M153.2,42.959c0,.655.013,1.311,0,1.966a.858.858,0,0,1-.88.948.873.873,0,0,1-.892-.948q-.027-1.965,0-3.931a.872.872,0,0,1,.892-.95.859.859,0,0,1,.88.95c.016.655,0,1.31,0,1.966" transform="translate(-134.323 -35.523)" fill="#8472ab"/>
                                <path id="Path_1596" data-name="Path 1596" d="M272.939,42.972c0,.637.01,1.273,0,1.91-.013.611-.331.966-.849.981a.881.881,0,0,1-.926-.972q-.025-1.937,0-3.875a.88.88,0,0,1,.917-.984c.526.015.845.363.858.974.014.655,0,1.31,0,1.966" transform="translate(-240.54 -35.513)" fill="#8472ab"/>
                                <path id="Path_1597" data-name="Path 1597" d="M187.757,164.007l.318.9c-.764.283-1.494.557-2.227.824-.775.282-1.558.543-2.322.85a.652.652,0,0,0-.369.445c-.02,2.159-.008,4.319,0,6.478,0,.033.028.066.065.151a6.3,6.3,0,0,0,4.158-2.041,6.463,6.463,0,0,0,1.743-4.353h.866c.318,3.5-3.034,7.682-7.818,7.356v-7.526l-1.838.644-.326-.888c.569-.212,1.082-.446,1.62-.592a.634.634,0,0,0,.551-.8,7,7,0,0,1-.006-.848l-1.837.627-.328-.864c.572-.213,1.1-.446,1.652-.6a.639.639,0,0,0,.523-.767c-.032-.88-.01-1.762-.01-2.687h.956v2.892l4.618-1.674.343.894c-.6.22-1.149.426-1.7.629-.9.328-1.786.684-2.7.97-.44.138-.613.347-.553.8a7.069,7.069,0,0,1,.006.857l4.617-1.674" transform="translate(-159.685 -142.217)" fill="#8472ab"/>
                            </g>
                        </svg>
                        <div class="transition-all duration-300 ease-in-out group-hover:drop-shadow-lg">₺777.000 Raised</div>
                    </div>
                    <div class="font-terramirum text-xl flex flex-row items-center space-x-2 py-4 border-t-1p border-bordergray pl-4 lg:pl-6 group">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="35" height="31" viewBox="0 0 45.721 40" class="transition-all duration-300 ease-in-out group-hover:scale-125">
                            <g id="Group_2832" data-name="Group 2832" transform="translate(0 3)">
                                <g id="Group_1978" data-name="Group 1978" transform="translate(0 -3)">
                                    <path id="Path_1589" data-name="Path 1589" d="M26.737,40A1.389,1.389,0,0,1,26,38.528a25.11,25.11,0,0,1,2.176-9.952A16.336,16.336,0,0,1,35.2,20.505c.556-.3,1.148-.541,1.668-.784a28,28,0,0,1-2.3-1.942,10.167,10.167,0,0,1-2.926-7.831A10.489,10.489,0,1,1,44.055,20.82a13.483,13.483,0,0,1-2.487.2,11.2,11.2,0,0,0-9.321,4.7,20.093,20.093,0,0,0-3.912,9.689c-.109.765-.173,1.537-.255,2.306a1.275,1.275,0,0,0,.013.2H56.2c-.132-1.041-.22-2.073-.4-3.089a19.634,19.634,0,0,0-3.134-7.991,31.928,31.928,0,0,0-2.595-2.995,1.249,1.249,0,0,1-.477-1.089,1.031,1.031,0,0,1,1.758-.545,16.824,16.824,0,0,1,2.975,3.376,23.145,23.145,0,0,1,3.707,10.355c.1.871.1,1.752.188,2.626A1.324,1.324,0,0,1,57.5,40Zm6.936-29.514a8.436,8.436,0,1,0,8.6-8.409,8.418,8.418,0,0,0-8.6,8.409" transform="translate(-19.471 0)" fill="#8472ab"/>
                                    <path id="Path_1590" data-name="Path 1590" d="M139.051,40.415c-.05.07-.1.138-.148.21a1.03,1.03,0,0,1-1.151.48A1.06,1.06,0,0,1,137,40.11c-.11-1.394-.149-2.8-.365-4.176A13.7,13.7,0,0,0,132.8,28.05a13.47,13.47,0,0,0-4.631-2.957,1.325,1.325,0,0,0-.563-.032c-.5.04-1,.125-1.506.143a.983.983,0,0,1-1.1-.966,1.006,1.006,0,0,1,1.056-1.072,6.21,6.21,0,0,0,6.192-5.89,6.564,6.564,0,0,0-4.473-6.9,8.961,8.961,0,0,0-2.008-.356c-.691-.069-1.136-.44-1.127-1.061a1.024,1.024,0,0,1,1.166-1,8.62,8.62,0,0,1,8.413,7.428,8.488,8.488,0,0,1-2.865,7.91c-.231.207-.49.382-.762.591.616.391,1.226.743,1.8,1.147a14.62,14.62,0,0,1,5.821,8.561c.371,1.43.506,2.922.75,4.385.022.127.06.252.09.378Z" transform="translate(-93.331 -5.959)" fill="#8472ab"/>
                                    <path id="Path_1591" data-name="Path 1591" d="M8.253,23.918a19.3,19.3,0,0,1-2.036-2.257,8.734,8.734,0,0,1,6.029-13.6,8.891,8.891,0,0,1,.977-.08,1.031,1.031,0,1,1,.054,2.046,6.505,6.505,0,0,0-5.623,3.322c-2.083,3.532-.444,8.73,4.007,9.677a8.925,8.925,0,0,0,1.282.148,1.066,1.066,0,0,1,1.111,1.084,1.025,1.025,0,0,1-1.16.966,3.984,3.984,0,0,1-1.062-.092,2.6,2.6,0,0,0-1.987.413,12.989,12.989,0,0,0-7.3,9.709c-.28,1.422-.327,2.89-.477,4.338-.021.207,0,.419-.034.624a1.018,1.018,0,0,1-1.109.927A1,1,0,0,1,0,40.083,24.631,24.631,0,0,1,.58,34.5a15.212,15.212,0,0,1,4.467-8.12c1.016-.925,2.182-1.685,3.207-2.464" transform="translate(0 -5.978)" fill="#8472ab"/>
                                </g>
                            </g>
                        </svg>
                        <div class="transition-all duration-300 ease-in-out group-hover:drop-shadow-lg">250 investors</div>
                    </div>
                    <div class="font-terramirum text-xl flex flex-row items-center space-x-2 py-4 border-t-1p border-bordergray pl-4 lg:pl-5 group">
                        <svg id="Group_2838" data-name="Group 2838" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="37.486" height="30" viewBox="0 0 37.486 30" class="transition-all duration-300 ease-in-out group-hover:scale-125">
                            <g id="Group_1986" data-name="Group 1986" transform="translate(0 0)">
                                <path id="Path_1598" data-name="Path 1598" d="M14.848,8.377V8.209c0-1.287.022-2.575-.008-3.861A2.419,2.419,0,0,1,15.8,2.324,6.77,6.77,0,0,1,17.73,1.267,17.939,17.939,0,0,1,21.392.348,30.87,30.87,0,0,1,25.761,0,31.369,31.369,0,0,1,30.8.326a17.506,17.506,0,0,1,4.016,1.026,6.113,6.113,0,0,1,1.827,1.072,2.308,2.308,0,0,1,.819,1.4,1.809,1.809,0,0,1,.016.256q0,3.034,0,6.069V22.76c0,.984-.021,1.968.006,2.951a2.319,2.319,0,0,1-.876,1.895,6.361,6.361,0,0,1-1.873,1.072,17.022,17.022,0,0,1-3.489.923,30.376,30.376,0,0,1-4.66.4,31.807,31.807,0,0,1-4.657-.268,18.584,18.584,0,0,1-4.254-1.021c-.219-.085-.434-.179-.647-.279a.2.2,0,0,0-.212.011,10.557,10.557,0,0,1-3.54,1.351,10.919,10.919,0,0,1-9.322-2.421A10.606,10.606,0,0,1,.177,20.82,10.841,10.841,0,0,1,2.805,11.5,10.777,10.777,0,0,1,9.255,7.9a11.031,11.031,0,0,1,5.394.409l.2.064m-3.72,20.174a9.677,9.677,0,1,0-9.676-9.677,9.69,9.69,0,0,0,9.676,9.677M26.412,6.7c.271-.009.78-.019,1.289-.041a25.606,25.606,0,0,0,4.359-.545,11.669,11.669,0,0,0,2.721-.9A3.354,3.354,0,0,0,35.817,4.5a.539.539,0,0,0,0-.855l-.035-.037a3.529,3.529,0,0,0-1.026-.692,12,12,0,0,0-2.686-.881,26.591,26.591,0,0,0-5.295-.581,28.493,28.493,0,0,0-6.162.507,13.041,13.041,0,0,0-2.851.869,3.84,3.84,0,0,0-1.21.781.566.566,0,0,0,0,.93,3.42,3.42,0,0,0,.923.642,11.727,11.727,0,0,0,2.927.961,29.16,29.16,0,0,0,6.01.559m9.617-.5a.686.686,0,0,0-.075.032,9.542,9.542,0,0,1-2.2.939,24.708,24.708,0,0,1-6.028.944,33.034,33.034,0,0,1-3.872-.043,25.461,25.461,0,0,1-4.371-.651,11.165,11.165,0,0,1-3.114-1.193.268.268,0,0,0-.068-.017c0,.241.007.472,0,.7a.672.672,0,0,0,.188.5,2.632,2.632,0,0,0,.287.274,5.243,5.243,0,0,0,1.43.768,18.226,18.226,0,0,0,4.442,1,30.584,30.584,0,0,0,3.815.191A27.669,27.669,0,0,0,31.8,9.12,12.429,12.429,0,0,0,34.728,8.2a3.163,3.163,0,0,0,1.105-.774.781.781,0,0,0,.187-.382,8.115,8.115,0,0,0,.009-.84m-.018,18.911c-.03.014-.061.026-.089.043a9.148,9.148,0,0,1-1.728.776,21.011,21.011,0,0,1-4.588.945,32.747,32.747,0,0,1-5,.141,26.954,26.954,0,0,1-5.1-.688.155.155,0,0,0-.168.05c-.319.316-.643.626-.964.94-.022.021-.042.046-.071.078l.145.055a19.665,19.665,0,0,0,4.573.945,30.825,30.825,0,0,0,3.921.145,28.055,28.055,0,0,0,4.374-.426,14.7,14.7,0,0,0,3.044-.852,4.225,4.225,0,0,0,1.345-.807.886.886,0,0,0,.29-.424,6.141,6.141,0,0,0,.013-.921M18.12,10.184l-.025.027a1.011,1.011,0,0,1,.093.06,11.6,11.6,0,0,1,1.752,1.8.336.336,0,0,0,.175.109,24.762,24.762,0,0,0,4.458.584,29.809,29.809,0,0,0,6.785-.4,14.192,14.192,0,0,0,3.136-.9,4.445,4.445,0,0,0,1.162-.7A.819.819,0,0,0,36,10.282a8.641,8.641,0,0,0,.008-.923.547.547,0,0,0-.066.03,9.739,9.739,0,0,1-2.287.962,26.016,26.016,0,0,1-6.75.943,31.906,31.906,0,0,1-4.406-.2,22.813,22.813,0,0,1-3.4-.627c-.329-.089-.654-.192-.981-.288m2.4,14.681c.042.011.063.017.083.021a24.06,24.06,0,0,0,2.868.4,32.436,32.436,0,0,0,4.485.061,26.011,26.011,0,0,0,3.789-.468,12.717,12.717,0,0,0,2.978-.931,3.423,3.423,0,0,0,1.111-.769.726.726,0,0,0,.2-.537c-.009-.192,0-.385,0-.577,0-.035-.007-.07-.012-.12l-.217.125c-.067.038-.134.075-.2.111a13.148,13.148,0,0,1-3.368,1.13,29.024,29.024,0,0,1-5.962.589,30.754,30.754,0,0,1-4.894-.355.158.158,0,0,0-.2.1c-.19.368-.393.73-.592,1.093l-.068.123m.658-10.79c.027.064.047.107.063.15.167.43.339.858.5,1.292a.2.2,0,0,0,.188.151,31.443,31.443,0,0,0,5.08.273,27.1,27.1,0,0,0,4.713-.5,12.694,12.694,0,0,0,3.026-.948,3.444,3.444,0,0,0,1.08-.747.731.731,0,0,0,.212-.546c-.009-.192,0-.385,0-.577,0-.036-.007-.071-.011-.115a.523.523,0,0,0-.062.026,10.78,10.78,0,0,1-2.878,1.132,26.985,26.985,0,0,1-6.4.784c-.79.012-1.583-.008-2.372-.05-.717-.038-1.431-.12-2.146-.191-.322-.032-.642-.085-.987-.132m.663,7.871c.06.012.109.023.159.03a29.015,29.015,0,0,0,3.066.257,30.068,30.068,0,0,0,6.032-.378,15.192,15.192,0,0,0,3.223-.874,4.624,4.624,0,0,0,1.4-.824.849.849,0,0,0,.321-.734c-.013-.144,0-.291,0-.436V18.8l-.1.051c-.266.137-.525.288-.8.41a15.689,15.689,0,0,1-3.788,1.076,29.733,29.733,0,0,1-4.633.412,31.933,31.933,0,0,1-3.46-.124c-.328-.031-.655-.066-.982-.1-.089-.01-.144-.006-.151.107a2.414,2.414,0,0,1-.062.353c-.069.317-.141.633-.215.964m14.182-6.287a.417.417,0,0,0-.061.022,11.219,11.219,0,0,1-3.126,1.194,28.031,28.031,0,0,1-6.472.726,31.515,31.515,0,0,1-3.726-.187l-.458-.049c.026.484.049.953.08,1.421,0,.031.06.079.1.083.425.048.85.1,1.276.131a32.353,32.353,0,0,0,4.075.062,26.1,26.1,0,0,0,4.233-.521,12.381,12.381,0,0,0,2.743-.872,3.358,3.358,0,0,0,1.135-.776.731.731,0,0,0,.2-.364,8.57,8.57,0,0,0,.009-.87" transform="translate(0 0)" fill="#8472ab"/>
                            </g>
                        </svg>

                        <div class="transition-all duration-300 ease-in-out group-hover:drop-shadow-lg">₺1000 min. investment</div>
                    </div>
                </div>
            </div>
            <div class="rounded-[59px] bg-white pb-2 w-full lg:w-3/12 flex flex-col items-center justify-between drop-shadow-fundshadow hover:drop-shadow-blueshadow transition-all duration-700 cursor-default">
                <div class="relative">
                    <img class="rounded-t-[59px]" src="/deneme/second.png" alt="" style="filter: drop-shadow(rgba(0, 0, 0, 0.16) 0px 3px 6px);">
                    <div class="font-terramirum text-lg text-[#5AA5FC] absolute right-3 bottom-1">
                        Frankfurt
                    </div>
                </div>
                <div class="flex h-full w-full flex-column">
                    <div class="pl-4 lg:pl-6 py-4">
                        <div class="font-terramirum text-2xl mt-3">
                            CityFUND
                        </div>
                        <div class="font-terramirum font-bold text-xl text-[#5AA5FC]">
                            Frankfurt-Germany
                        </div>
                    </div>
                    <div
                        class="font-terramirum text-xl flex flex-row items-center space-x-2 py-4 border-t-1p border-bordergray pl-4 lg:pl-6 group">
                        <svg id="Group_2835" data-name="Group 2835" xmlns="http://www.w3.org/2000/svg"
                             xmlns:xlink="http://www.w3.org/1999/xlink" width="35" height="40"
                             viewBox="0 0 48.388 54.986" class="transition-all duration-300 ease-in-out group-hover:scale-125">
                            <g id="Group_1982" data-name="Group 1982">
                                <path id="Path_1592" data-name="Path 1592"
                                      d="M25.524,344.234c-1.643-.433-3.29-.851-4.928-1.3-2.366-.652-4.732-1.308-7.086-2a3.654,3.654,0,0,0-3.027.354c-.226.133-.457.259-.693.374-.515.251-.955.487-.88,1.228.052.511-.4.841-.936.829q-3.49-.079-6.98-.185c-.7-.022-1.013-.4-.993-1.155q.109-4.167.229-8.334c.063-2.253.131-4.5.19-6.757.026-.971.305-1.252,1.25-1.227q3.237.086,6.474.168c.958.024,1.258.354,1.221,1.333-.012.33,0,.661,0,1.012.788-.272,1.532-.571,2.3-.786a11.518,11.518,0,0,1,8.014.641,30.205,30.205,0,0,0,3.078,1.2,6.3,6.3,0,0,0,1.672.165c1.782.062,3.564.129,5.347.148a5.882,5.882,0,0,1,5.264,2.793.863.863,0,0,0,.1.135.765.765,0,0,0,.135.081l5.308-2.5c.577-.272,1.151-.548,1.729-.816a4.446,4.446,0,0,1,5.879,1.726c.059.092.133.174.2.261v.563a4.611,4.611,0,0,1-.637.611Q40.191,337.71,32.61,342.6a10.19,10.19,0,0,1-2.275,1.061,24.278,24.278,0,0,1-2.559.574H25.524M9.069,339.928a.521.521,0,0,0,.147,0,1.6,1.6,0,0,0,.253-.121,5.785,5.785,0,0,1,4.972-.493c3.3.981,6.618,1.934,9.951,2.812a8.925,8.925,0,0,0,7.391-1.1q6.925-4.472,13.843-8.954c.17-.11.336-.226.537-.361a3.151,3.151,0,0,0-.242-.21,2.909,2.909,0,0,0-3.091-.151c-2,.95-3.981,1.929-6,2.826-.713.316-1.109.629-.971,1.5.113.715-.32,1.076-1.039,1.056q-5.15-.14-10.3-.291a1.657,1.657,0,0,1-.6-.113.807.807,0,0,1-.5-.964.792.792,0,0,1,.771-.7c.187-.011.375-.01.563,0l8.611.237c.2.005.394,0,.6,0a3.905,3.905,0,0,0-3.845-3.158c-1.65-.063-3.3-.139-4.953-.129a13.326,13.326,0,0,1-6.155-1.481,9.5,9.5,0,0,0-9.27.364.818.818,0,0,0-.486.788c-.054,2.741-.141,5.481-.214,8.221,0,.141.018.283.029.427M1.817,341.8H7.174c.126-4.57.253-9.154.382-13.829H2.2L1.817,341.8"
                                      transform="translate(0 -289.248)" fill="#5AA5FC"/>
                                <path id="Path_1593" data-name="Path 1593"
                                      d="M116.6,106.4a13.081,13.081,0,1,1-13.05,12.688A13.05,13.05,0,0,1,116.6,106.4m.056,1.8a11.284,11.284,0,1,0,11.219,11.547A11.239,11.239,0,0,0,116.652,108.2"
                                      transform="translate(-91.854 -94.389)" fill="#5AA5FC"/>
                                <path id="Path_1594" data-name="Path 1594"
                                      d="M213.105,5.18c0,1.35,0,2.7,0,4.049,0,.736-.313,1.121-.882,1.121s-.9-.4-.9-1.116q-.006-4.077,0-8.154c0-.846.679-1.347,1.308-.931a1.3,1.3,0,0,1,.452.926c.049,1.367.021,2.736.021,4.1"
                                      transform="translate(-187.463 0)" fill="#5AA5FC"/>
                                <path id="Path_1595" data-name="Path 1595"
                                      d="M153.2,42.959c0,.655.013,1.311,0,1.966a.858.858,0,0,1-.88.948.873.873,0,0,1-.892-.948q-.027-1.965,0-3.931a.872.872,0,0,1,.892-.95.859.859,0,0,1,.88.95c.016.655,0,1.31,0,1.966"
                                      transform="translate(-134.323 -35.523)" fill="#5AA5FC"/>
                                <path id="Path_1596" data-name="Path 1596"
                                      d="M272.939,42.972c0,.637.01,1.273,0,1.91-.013.611-.331.966-.849.981a.881.881,0,0,1-.926-.972q-.025-1.937,0-3.875a.88.88,0,0,1,.917-.984c.526.015.845.363.858.974.014.655,0,1.31,0,1.966"
                                      transform="translate(-240.54 -35.513)" fill="#5AA5FC"/>
                                <path id="Path_1597" data-name="Path 1597"
                                      d="M187.757,164.007l.318.9c-.764.283-1.494.557-2.227.824-.775.282-1.558.543-2.322.85a.652.652,0,0,0-.369.445c-.02,2.159-.008,4.319,0,6.478,0,.033.028.066.065.151a6.3,6.3,0,0,0,4.158-2.041,6.463,6.463,0,0,0,1.743-4.353h.866c.318,3.5-3.034,7.682-7.818,7.356v-7.526l-1.838.644-.326-.888c.569-.212,1.082-.446,1.62-.592a.634.634,0,0,0,.551-.8,7,7,0,0,1-.006-.848l-1.837.627-.328-.864c.572-.213,1.1-.446,1.652-.6a.639.639,0,0,0,.523-.767c-.032-.88-.01-1.762-.01-2.687h.956v2.892l4.618-1.674.343.894c-.6.22-1.149.426-1.7.629-.9.328-1.786.684-2.7.97-.44.138-.613.347-.553.8a7.069,7.069,0,0,1,.006.857l4.617-1.674"
                                      transform="translate(-159.685 -142.217)" fill="#5AA5FC"/>
                            </g>
                        </svg>
                        <div class="transition-all duration-300 ease-in-out group-hover:drop-shadow-lg">$115.000 Raised</div>
                    </div>
                    <div
                        class="font-terramirum text-xl flex flex-row items-center space-x-2 py-4 border-t-1p border-bordergray pl-4 lg:pl-6 group">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="35"
                             height="31" viewBox="0 0 45.721 40" class="transition-all duration-300 ease-in-out group-hover:scale-125">
                            <g id="Group_2832" data-name="Group 2832" transform="translate(0 3)">
                                <g id="Group_1978" data-name="Group 1978" transform="translate(0 -3)">
                                    <path id="Path_1589" data-name="Path 1589"
                                          d="M26.737,40A1.389,1.389,0,0,1,26,38.528a25.11,25.11,0,0,1,2.176-9.952A16.336,16.336,0,0,1,35.2,20.505c.556-.3,1.148-.541,1.668-.784a28,28,0,0,1-2.3-1.942,10.167,10.167,0,0,1-2.926-7.831A10.489,10.489,0,1,1,44.055,20.82a13.483,13.483,0,0,1-2.487.2,11.2,11.2,0,0,0-9.321,4.7,20.093,20.093,0,0,0-3.912,9.689c-.109.765-.173,1.537-.255,2.306a1.275,1.275,0,0,0,.013.2H56.2c-.132-1.041-.22-2.073-.4-3.089a19.634,19.634,0,0,0-3.134-7.991,31.928,31.928,0,0,0-2.595-2.995,1.249,1.249,0,0,1-.477-1.089,1.031,1.031,0,0,1,1.758-.545,16.824,16.824,0,0,1,2.975,3.376,23.145,23.145,0,0,1,3.707,10.355c.1.871.1,1.752.188,2.626A1.324,1.324,0,0,1,57.5,40Zm6.936-29.514a8.436,8.436,0,1,0,8.6-8.409,8.418,8.418,0,0,0-8.6,8.409"
                                          transform="translate(-19.471 0)" fill="#5AA5FC"/>
                                    <path id="Path_1590" data-name="Path 1590"
                                          d="M139.051,40.415c-.05.07-.1.138-.148.21a1.03,1.03,0,0,1-1.151.48A1.06,1.06,0,0,1,137,40.11c-.11-1.394-.149-2.8-.365-4.176A13.7,13.7,0,0,0,132.8,28.05a13.47,13.47,0,0,0-4.631-2.957,1.325,1.325,0,0,0-.563-.032c-.5.04-1,.125-1.506.143a.983.983,0,0,1-1.1-.966,1.006,1.006,0,0,1,1.056-1.072,6.21,6.21,0,0,0,6.192-5.89,6.564,6.564,0,0,0-4.473-6.9,8.961,8.961,0,0,0-2.008-.356c-.691-.069-1.136-.44-1.127-1.061a1.024,1.024,0,0,1,1.166-1,8.62,8.62,0,0,1,8.413,7.428,8.488,8.488,0,0,1-2.865,7.91c-.231.207-.49.382-.762.591.616.391,1.226.743,1.8,1.147a14.62,14.62,0,0,1,5.821,8.561c.371,1.43.506,2.922.75,4.385.022.127.06.252.09.378Z"
                                          transform="translate(-93.331 -5.959)" fill="#5AA5FC"/>
                                    <path id="Path_1591" data-name="Path 1591"
                                          d="M8.253,23.918a19.3,19.3,0,0,1-2.036-2.257,8.734,8.734,0,0,1,6.029-13.6,8.891,8.891,0,0,1,.977-.08,1.031,1.031,0,1,1,.054,2.046,6.505,6.505,0,0,0-5.623,3.322c-2.083,3.532-.444,8.73,4.007,9.677a8.925,8.925,0,0,0,1.282.148,1.066,1.066,0,0,1,1.111,1.084,1.025,1.025,0,0,1-1.16.966,3.984,3.984,0,0,1-1.062-.092,2.6,2.6,0,0,0-1.987.413,12.989,12.989,0,0,0-7.3,9.709c-.28,1.422-.327,2.89-.477,4.338-.021.207,0,.419-.034.624a1.018,1.018,0,0,1-1.109.927A1,1,0,0,1,0,40.083,24.631,24.631,0,0,1,.58,34.5a15.212,15.212,0,0,1,4.467-8.12c1.016-.925,2.182-1.685,3.207-2.464"
                                          transform="translate(0 -5.978)" fill="#5AA5FC"/>
                                </g>
                            </g>
                        </svg>
                        <div class="transition-all duration-300 ease-in-out group-hover:drop-shadow-lg">92 investors</div>
                    </div>
                    <div
                        class="font-terramirum text-xl flex flex-row items-center space-x-2 py-4 border-t-1p border-bordergray pl-4 lg:pl-5 group">
                        <svg id="Group_2838" data-name="Group 2838" xmlns="http://www.w3.org/2000/svg"
                             xmlns:xlink="http://www.w3.org/1999/xlink" width="37.486" height="30" class="transition-all duration-300 ease-in-out group-hover:scale-125"
                             viewBox="0 0 37.486 30">
                            <g id="Group_1986" data-name="Group 1986" transform="translate(0 0)">
                                <path id="Path_1598" data-name="Path 1598"
                                      d="M14.848,8.377V8.209c0-1.287.022-2.575-.008-3.861A2.419,2.419,0,0,1,15.8,2.324,6.77,6.77,0,0,1,17.73,1.267,17.939,17.939,0,0,1,21.392.348,30.87,30.87,0,0,1,25.761,0,31.369,31.369,0,0,1,30.8.326a17.506,17.506,0,0,1,4.016,1.026,6.113,6.113,0,0,1,1.827,1.072,2.308,2.308,0,0,1,.819,1.4,1.809,1.809,0,0,1,.016.256q0,3.034,0,6.069V22.76c0,.984-.021,1.968.006,2.951a2.319,2.319,0,0,1-.876,1.895,6.361,6.361,0,0,1-1.873,1.072,17.022,17.022,0,0,1-3.489.923,30.376,30.376,0,0,1-4.66.4,31.807,31.807,0,0,1-4.657-.268,18.584,18.584,0,0,1-4.254-1.021c-.219-.085-.434-.179-.647-.279a.2.2,0,0,0-.212.011,10.557,10.557,0,0,1-3.54,1.351,10.919,10.919,0,0,1-9.322-2.421A10.606,10.606,0,0,1,.177,20.82,10.841,10.841,0,0,1,2.805,11.5,10.777,10.777,0,0,1,9.255,7.9a11.031,11.031,0,0,1,5.394.409l.2.064m-3.72,20.174a9.677,9.677,0,1,0-9.676-9.677,9.69,9.69,0,0,0,9.676,9.677M26.412,6.7c.271-.009.78-.019,1.289-.041a25.606,25.606,0,0,0,4.359-.545,11.669,11.669,0,0,0,2.721-.9A3.354,3.354,0,0,0,35.817,4.5a.539.539,0,0,0,0-.855l-.035-.037a3.529,3.529,0,0,0-1.026-.692,12,12,0,0,0-2.686-.881,26.591,26.591,0,0,0-5.295-.581,28.493,28.493,0,0,0-6.162.507,13.041,13.041,0,0,0-2.851.869,3.84,3.84,0,0,0-1.21.781.566.566,0,0,0,0,.93,3.42,3.42,0,0,0,.923.642,11.727,11.727,0,0,0,2.927.961,29.16,29.16,0,0,0,6.01.559m9.617-.5a.686.686,0,0,0-.075.032,9.542,9.542,0,0,1-2.2.939,24.708,24.708,0,0,1-6.028.944,33.034,33.034,0,0,1-3.872-.043,25.461,25.461,0,0,1-4.371-.651,11.165,11.165,0,0,1-3.114-1.193.268.268,0,0,0-.068-.017c0,.241.007.472,0,.7a.672.672,0,0,0,.188.5,2.632,2.632,0,0,0,.287.274,5.243,5.243,0,0,0,1.43.768,18.226,18.226,0,0,0,4.442,1,30.584,30.584,0,0,0,3.815.191A27.669,27.669,0,0,0,31.8,9.12,12.429,12.429,0,0,0,34.728,8.2a3.163,3.163,0,0,0,1.105-.774.781.781,0,0,0,.187-.382,8.115,8.115,0,0,0,.009-.84m-.018,18.911c-.03.014-.061.026-.089.043a9.148,9.148,0,0,1-1.728.776,21.011,21.011,0,0,1-4.588.945,32.747,32.747,0,0,1-5,.141,26.954,26.954,0,0,1-5.1-.688.155.155,0,0,0-.168.05c-.319.316-.643.626-.964.94-.022.021-.042.046-.071.078l.145.055a19.665,19.665,0,0,0,4.573.945,30.825,30.825,0,0,0,3.921.145,28.055,28.055,0,0,0,4.374-.426,14.7,14.7,0,0,0,3.044-.852,4.225,4.225,0,0,0,1.345-.807.886.886,0,0,0,.29-.424,6.141,6.141,0,0,0,.013-.921M18.12,10.184l-.025.027a1.011,1.011,0,0,1,.093.06,11.6,11.6,0,0,1,1.752,1.8.336.336,0,0,0,.175.109,24.762,24.762,0,0,0,4.458.584,29.809,29.809,0,0,0,6.785-.4,14.192,14.192,0,0,0,3.136-.9,4.445,4.445,0,0,0,1.162-.7A.819.819,0,0,0,36,10.282a8.641,8.641,0,0,0,.008-.923.547.547,0,0,0-.066.03,9.739,9.739,0,0,1-2.287.962,26.016,26.016,0,0,1-6.75.943,31.906,31.906,0,0,1-4.406-.2,22.813,22.813,0,0,1-3.4-.627c-.329-.089-.654-.192-.981-.288m2.4,14.681c.042.011.063.017.083.021a24.06,24.06,0,0,0,2.868.4,32.436,32.436,0,0,0,4.485.061,26.011,26.011,0,0,0,3.789-.468,12.717,12.717,0,0,0,2.978-.931,3.423,3.423,0,0,0,1.111-.769.726.726,0,0,0,.2-.537c-.009-.192,0-.385,0-.577,0-.035-.007-.07-.012-.12l-.217.125c-.067.038-.134.075-.2.111a13.148,13.148,0,0,1-3.368,1.13,29.024,29.024,0,0,1-5.962.589,30.754,30.754,0,0,1-4.894-.355.158.158,0,0,0-.2.1c-.19.368-.393.73-.592,1.093l-.068.123m.658-10.79c.027.064.047.107.063.15.167.43.339.858.5,1.292a.2.2,0,0,0,.188.151,31.443,31.443,0,0,0,5.08.273,27.1,27.1,0,0,0,4.713-.5,12.694,12.694,0,0,0,3.026-.948,3.444,3.444,0,0,0,1.08-.747.731.731,0,0,0,.212-.546c-.009-.192,0-.385,0-.577,0-.036-.007-.071-.011-.115a.523.523,0,0,0-.062.026,10.78,10.78,0,0,1-2.878,1.132,26.985,26.985,0,0,1-6.4.784c-.79.012-1.583-.008-2.372-.05-.717-.038-1.431-.12-2.146-.191-.322-.032-.642-.085-.987-.132m.663,7.871c.06.012.109.023.159.03a29.015,29.015,0,0,0,3.066.257,30.068,30.068,0,0,0,6.032-.378,15.192,15.192,0,0,0,3.223-.874,4.624,4.624,0,0,0,1.4-.824.849.849,0,0,0,.321-.734c-.013-.144,0-.291,0-.436V18.8l-.1.051c-.266.137-.525.288-.8.41a15.689,15.689,0,0,1-3.788,1.076,29.733,29.733,0,0,1-4.633.412,31.933,31.933,0,0,1-3.46-.124c-.328-.031-.655-.066-.982-.1-.089-.01-.144-.006-.151.107a2.414,2.414,0,0,1-.062.353c-.069.317-.141.633-.215.964m14.182-6.287a.417.417,0,0,0-.061.022,11.219,11.219,0,0,1-3.126,1.194,28.031,28.031,0,0,1-6.472.726,31.515,31.515,0,0,1-3.726-.187l-.458-.049c.026.484.049.953.08,1.421,0,.031.06.079.1.083.425.048.85.1,1.276.131a32.353,32.353,0,0,0,4.075.062,26.1,26.1,0,0,0,4.233-.521,12.381,12.381,0,0,0,2.743-.872,3.358,3.358,0,0,0,1.135-.776.731.731,0,0,0,.2-.364,8.57,8.57,0,0,0,.009-.87"
                                      transform="translate(0 0)" fill="#5AA5FC"/>
                            </g>
                        </svg>
                        <div class="transition-all duration-300 ease-in-out group-hover:drop-shadow-lg">$500 min. investment</div>
                    </div>
                </div>
            </div>
            <div class="rounded-[59px] bg-white pb-2 w-full lg:w-3/12 flex flex-col items-center justify-between drop-shadow-fundshadow hover:drop-shadow-greenshadow transition-all duration-700 cursor-default">
                <div class="relative">
                    <img class="rounded-t-[59px]" src="/deneme/third.png" alt="" style="filter: drop-shadow(rgba(0, 0, 0, 0.16) 0px 3px 6px);">
                    <div class="font-terramirum text-lg text-[#7EA391] absolute right-3 bottom-1">Bournemouth</div>
                </div>

                <div class="flex h-full w-full flex-column">
                    <div class="pl-4 lg:pl-6 py-4">
                        <div class="font-terramirum text-2xl mt-3">
                            CityFUND
                        </div>
                        <div class="font-terramirum font-bold text-xl text-[#7EA391]">
                            Bournemouth-England
                        </div>
                    </div>
                    <div
                        class="font-terramirum text-xl flex flex-row items-center space-x-2 py-4 border-t-1p border-bordergray pl-4 lg:pl-6 group">
                        <svg id="Group_2835" data-name="Group 2835" xmlns="http://www.w3.org/2000/svg"
                             xmlns:xlink="http://www.w3.org/1999/xlink" width="35" height="40" class="transition-all duration-300 ease-in-out group-hover:scale-125"
                             viewBox="0 0 48.388 54.986">
                            <g id="Group_1982" data-name="Group 1982">
                                <path id="Path_1592" data-name="Path 1592"
                                      d="M25.524,344.234c-1.643-.433-3.29-.851-4.928-1.3-2.366-.652-4.732-1.308-7.086-2a3.654,3.654,0,0,0-3.027.354c-.226.133-.457.259-.693.374-.515.251-.955.487-.88,1.228.052.511-.4.841-.936.829q-3.49-.079-6.98-.185c-.7-.022-1.013-.4-.993-1.155q.109-4.167.229-8.334c.063-2.253.131-4.5.19-6.757.026-.971.305-1.252,1.25-1.227q3.237.086,6.474.168c.958.024,1.258.354,1.221,1.333-.012.33,0,.661,0,1.012.788-.272,1.532-.571,2.3-.786a11.518,11.518,0,0,1,8.014.641,30.205,30.205,0,0,0,3.078,1.2,6.3,6.3,0,0,0,1.672.165c1.782.062,3.564.129,5.347.148a5.882,5.882,0,0,1,5.264,2.793.863.863,0,0,0,.1.135.765.765,0,0,0,.135.081l5.308-2.5c.577-.272,1.151-.548,1.729-.816a4.446,4.446,0,0,1,5.879,1.726c.059.092.133.174.2.261v.563a4.611,4.611,0,0,1-.637.611Q40.191,337.71,32.61,342.6a10.19,10.19,0,0,1-2.275,1.061,24.278,24.278,0,0,1-2.559.574H25.524M9.069,339.928a.521.521,0,0,0,.147,0,1.6,1.6,0,0,0,.253-.121,5.785,5.785,0,0,1,4.972-.493c3.3.981,6.618,1.934,9.951,2.812a8.925,8.925,0,0,0,7.391-1.1q6.925-4.472,13.843-8.954c.17-.11.336-.226.537-.361a3.151,3.151,0,0,0-.242-.21,2.909,2.909,0,0,0-3.091-.151c-2,.95-3.981,1.929-6,2.826-.713.316-1.109.629-.971,1.5.113.715-.32,1.076-1.039,1.056q-5.15-.14-10.3-.291a1.657,1.657,0,0,1-.6-.113.807.807,0,0,1-.5-.964.792.792,0,0,1,.771-.7c.187-.011.375-.01.563,0l8.611.237c.2.005.394,0,.6,0a3.905,3.905,0,0,0-3.845-3.158c-1.65-.063-3.3-.139-4.953-.129a13.326,13.326,0,0,1-6.155-1.481,9.5,9.5,0,0,0-9.27.364.818.818,0,0,0-.486.788c-.054,2.741-.141,5.481-.214,8.221,0,.141.018.283.029.427M1.817,341.8H7.174c.126-4.57.253-9.154.382-13.829H2.2L1.817,341.8"
                                      transform="translate(0 -289.248)" fill="#7EA391"/>
                                <path id="Path_1593" data-name="Path 1593"
                                      d="M116.6,106.4a13.081,13.081,0,1,1-13.05,12.688A13.05,13.05,0,0,1,116.6,106.4m.056,1.8a11.284,11.284,0,1,0,11.219,11.547A11.239,11.239,0,0,0,116.652,108.2"
                                      transform="translate(-91.854 -94.389)" fill="#7EA391"/>
                                <path id="Path_1594" data-name="Path 1594"
                                      d="M213.105,5.18c0,1.35,0,2.7,0,4.049,0,.736-.313,1.121-.882,1.121s-.9-.4-.9-1.116q-.006-4.077,0-8.154c0-.846.679-1.347,1.308-.931a1.3,1.3,0,0,1,.452.926c.049,1.367.021,2.736.021,4.1"
                                      transform="translate(-187.463 0)" fill="#7EA391"/>
                                <path id="Path_1595" data-name="Path 1595"
                                      d="M153.2,42.959c0,.655.013,1.311,0,1.966a.858.858,0,0,1-.88.948.873.873,0,0,1-.892-.948q-.027-1.965,0-3.931a.872.872,0,0,1,.892-.95.859.859,0,0,1,.88.95c.016.655,0,1.31,0,1.966"
                                      transform="translate(-134.323 -35.523)" fill="#7EA391"/>
                                <path id="Path_1596" data-name="Path 1596"
                                      d="M272.939,42.972c0,.637.01,1.273,0,1.91-.013.611-.331.966-.849.981a.881.881,0,0,1-.926-.972q-.025-1.937,0-3.875a.88.88,0,0,1,.917-.984c.526.015.845.363.858.974.014.655,0,1.31,0,1.966"
                                      transform="translate(-240.54 -35.513)" fill="#7EA391"/>
                                <path id="Path_1597" data-name="Path 1597"
                                      d="M187.757,164.007l.318.9c-.764.283-1.494.557-2.227.824-.775.282-1.558.543-2.322.85a.652.652,0,0,0-.369.445c-.02,2.159-.008,4.319,0,6.478,0,.033.028.066.065.151a6.3,6.3,0,0,0,4.158-2.041,6.463,6.463,0,0,0,1.743-4.353h.866c.318,3.5-3.034,7.682-7.818,7.356v-7.526l-1.838.644-.326-.888c.569-.212,1.082-.446,1.62-.592a.634.634,0,0,0,.551-.8,7,7,0,0,1-.006-.848l-1.837.627-.328-.864c.572-.213,1.1-.446,1.652-.6a.639.639,0,0,0,.523-.767c-.032-.88-.01-1.762-.01-2.687h.956v2.892l4.618-1.674.343.894c-.6.22-1.149.426-1.7.629-.9.328-1.786.684-2.7.97-.44.138-.613.347-.553.8a7.069,7.069,0,0,1,.006.857l4.617-1.674"
                                      transform="translate(-159.685 -142.217)" fill="#7EA391"/>
                            </g>
                        </svg>
                        <div class="transition-all duration-300 ease-in-out group-hover:drop-shadow-lg">$19.000 Raised</div>
                    </div>
                    <div
                        class="font-terramirum text-xl flex flex-row items-center space-x-2 py-4 border-t-1p border-bordergray pl-4 lg:pl-6 group">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="35"
                             height="31" viewBox="0 0 45.721 40" class="transition-all duration-300 ease-in-out group-hover:scale-125">
                            <g id="Group_2832" data-name="Group 2832" transform="translate(0 3)">
                                <g id="Group_1978" data-name="Group 1978" transform="translate(0 -3)">
                                    <path id="Path_1589" data-name="Path 1589"
                                          d="M26.737,40A1.389,1.389,0,0,1,26,38.528a25.11,25.11,0,0,1,2.176-9.952A16.336,16.336,0,0,1,35.2,20.505c.556-.3,1.148-.541,1.668-.784a28,28,0,0,1-2.3-1.942,10.167,10.167,0,0,1-2.926-7.831A10.489,10.489,0,1,1,44.055,20.82a13.483,13.483,0,0,1-2.487.2,11.2,11.2,0,0,0-9.321,4.7,20.093,20.093,0,0,0-3.912,9.689c-.109.765-.173,1.537-.255,2.306a1.275,1.275,0,0,0,.013.2H56.2c-.132-1.041-.22-2.073-.4-3.089a19.634,19.634,0,0,0-3.134-7.991,31.928,31.928,0,0,0-2.595-2.995,1.249,1.249,0,0,1-.477-1.089,1.031,1.031,0,0,1,1.758-.545,16.824,16.824,0,0,1,2.975,3.376,23.145,23.145,0,0,1,3.707,10.355c.1.871.1,1.752.188,2.626A1.324,1.324,0,0,1,57.5,40Zm6.936-29.514a8.436,8.436,0,1,0,8.6-8.409,8.418,8.418,0,0,0-8.6,8.409"
                                          transform="translate(-19.471 0)" fill="#7EA391"/>
                                    <path id="Path_1590" data-name="Path 1590"
                                          d="M139.051,40.415c-.05.07-.1.138-.148.21a1.03,1.03,0,0,1-1.151.48A1.06,1.06,0,0,1,137,40.11c-.11-1.394-.149-2.8-.365-4.176A13.7,13.7,0,0,0,132.8,28.05a13.47,13.47,0,0,0-4.631-2.957,1.325,1.325,0,0,0-.563-.032c-.5.04-1,.125-1.506.143a.983.983,0,0,1-1.1-.966,1.006,1.006,0,0,1,1.056-1.072,6.21,6.21,0,0,0,6.192-5.89,6.564,6.564,0,0,0-4.473-6.9,8.961,8.961,0,0,0-2.008-.356c-.691-.069-1.136-.44-1.127-1.061a1.024,1.024,0,0,1,1.166-1,8.62,8.62,0,0,1,8.413,7.428,8.488,8.488,0,0,1-2.865,7.91c-.231.207-.49.382-.762.591.616.391,1.226.743,1.8,1.147a14.62,14.62,0,0,1,5.821,8.561c.371,1.43.506,2.922.75,4.385.022.127.06.252.09.378Z"
                                          transform="translate(-93.331 -5.959)" fill="#7EA391"/>
                                    <path id="Path_1591" data-name="Path 1591"
                                          d="M8.253,23.918a19.3,19.3,0,0,1-2.036-2.257,8.734,8.734,0,0,1,6.029-13.6,8.891,8.891,0,0,1,.977-.08,1.031,1.031,0,1,1,.054,2.046,6.505,6.505,0,0,0-5.623,3.322c-2.083,3.532-.444,8.73,4.007,9.677a8.925,8.925,0,0,0,1.282.148,1.066,1.066,0,0,1,1.111,1.084,1.025,1.025,0,0,1-1.16.966,3.984,3.984,0,0,1-1.062-.092,2.6,2.6,0,0,0-1.987.413,12.989,12.989,0,0,0-7.3,9.709c-.28,1.422-.327,2.89-.477,4.338-.021.207,0,.419-.034.624a1.018,1.018,0,0,1-1.109.927A1,1,0,0,1,0,40.083,24.631,24.631,0,0,1,.58,34.5a15.212,15.212,0,0,1,4.467-8.12c1.016-.925,2.182-1.685,3.207-2.464"
                                          transform="translate(0 -5.978)" fill="#7EA391"/>
                                </g>
                            </g>
                        </svg>
                        <div class="transition-all duration-300 ease-in-out group-hover:drop-shadow-lg">7 investors</div>
                    </div>
                    <div
                        class="font-terramirum text-xl flex flex-row items-center space-x-2 py-4 border-t-1p border-bordergray pl-4 lg:pl-5 group">
                        <svg id="Group_2838" data-name="Group 2838" xmlns="http://www.w3.org/2000/svg"
                             xmlns:xlink="http://www.w3.org/1999/xlink" width="37.486" height="30" class="transition-all duration-300 ease-in-out group-hover:scale-125"
                             viewBox="0 0 37.486 30">
                            <g id="Group_1986" data-name="Group 1986" transform="translate(0 0)">
                                <path id="Path_1598" data-name="Path 1598"
                                      d="M14.848,8.377V8.209c0-1.287.022-2.575-.008-3.861A2.419,2.419,0,0,1,15.8,2.324,6.77,6.77,0,0,1,17.73,1.267,17.939,17.939,0,0,1,21.392.348,30.87,30.87,0,0,1,25.761,0,31.369,31.369,0,0,1,30.8.326a17.506,17.506,0,0,1,4.016,1.026,6.113,6.113,0,0,1,1.827,1.072,2.308,2.308,0,0,1,.819,1.4,1.809,1.809,0,0,1,.016.256q0,3.034,0,6.069V22.76c0,.984-.021,1.968.006,2.951a2.319,2.319,0,0,1-.876,1.895,6.361,6.361,0,0,1-1.873,1.072,17.022,17.022,0,0,1-3.489.923,30.376,30.376,0,0,1-4.66.4,31.807,31.807,0,0,1-4.657-.268,18.584,18.584,0,0,1-4.254-1.021c-.219-.085-.434-.179-.647-.279a.2.2,0,0,0-.212.011,10.557,10.557,0,0,1-3.54,1.351,10.919,10.919,0,0,1-9.322-2.421A10.606,10.606,0,0,1,.177,20.82,10.841,10.841,0,0,1,2.805,11.5,10.777,10.777,0,0,1,9.255,7.9a11.031,11.031,0,0,1,5.394.409l.2.064m-3.72,20.174a9.677,9.677,0,1,0-9.676-9.677,9.69,9.69,0,0,0,9.676,9.677M26.412,6.7c.271-.009.78-.019,1.289-.041a25.606,25.606,0,0,0,4.359-.545,11.669,11.669,0,0,0,2.721-.9A3.354,3.354,0,0,0,35.817,4.5a.539.539,0,0,0,0-.855l-.035-.037a3.529,3.529,0,0,0-1.026-.692,12,12,0,0,0-2.686-.881,26.591,26.591,0,0,0-5.295-.581,28.493,28.493,0,0,0-6.162.507,13.041,13.041,0,0,0-2.851.869,3.84,3.84,0,0,0-1.21.781.566.566,0,0,0,0,.93,3.42,3.42,0,0,0,.923.642,11.727,11.727,0,0,0,2.927.961,29.16,29.16,0,0,0,6.01.559m9.617-.5a.686.686,0,0,0-.075.032,9.542,9.542,0,0,1-2.2.939,24.708,24.708,0,0,1-6.028.944,33.034,33.034,0,0,1-3.872-.043,25.461,25.461,0,0,1-4.371-.651,11.165,11.165,0,0,1-3.114-1.193.268.268,0,0,0-.068-.017c0,.241.007.472,0,.7a.672.672,0,0,0,.188.5,2.632,2.632,0,0,0,.287.274,5.243,5.243,0,0,0,1.43.768,18.226,18.226,0,0,0,4.442,1,30.584,30.584,0,0,0,3.815.191A27.669,27.669,0,0,0,31.8,9.12,12.429,12.429,0,0,0,34.728,8.2a3.163,3.163,0,0,0,1.105-.774.781.781,0,0,0,.187-.382,8.115,8.115,0,0,0,.009-.84m-.018,18.911c-.03.014-.061.026-.089.043a9.148,9.148,0,0,1-1.728.776,21.011,21.011,0,0,1-4.588.945,32.747,32.747,0,0,1-5,.141,26.954,26.954,0,0,1-5.1-.688.155.155,0,0,0-.168.05c-.319.316-.643.626-.964.94-.022.021-.042.046-.071.078l.145.055a19.665,19.665,0,0,0,4.573.945,30.825,30.825,0,0,0,3.921.145,28.055,28.055,0,0,0,4.374-.426,14.7,14.7,0,0,0,3.044-.852,4.225,4.225,0,0,0,1.345-.807.886.886,0,0,0,.29-.424,6.141,6.141,0,0,0,.013-.921M18.12,10.184l-.025.027a1.011,1.011,0,0,1,.093.06,11.6,11.6,0,0,1,1.752,1.8.336.336,0,0,0,.175.109,24.762,24.762,0,0,0,4.458.584,29.809,29.809,0,0,0,6.785-.4,14.192,14.192,0,0,0,3.136-.9,4.445,4.445,0,0,0,1.162-.7A.819.819,0,0,0,36,10.282a8.641,8.641,0,0,0,.008-.923.547.547,0,0,0-.066.03,9.739,9.739,0,0,1-2.287.962,26.016,26.016,0,0,1-6.75.943,31.906,31.906,0,0,1-4.406-.2,22.813,22.813,0,0,1-3.4-.627c-.329-.089-.654-.192-.981-.288m2.4,14.681c.042.011.063.017.083.021a24.06,24.06,0,0,0,2.868.4,32.436,32.436,0,0,0,4.485.061,26.011,26.011,0,0,0,3.789-.468,12.717,12.717,0,0,0,2.978-.931,3.423,3.423,0,0,0,1.111-.769.726.726,0,0,0,.2-.537c-.009-.192,0-.385,0-.577,0-.035-.007-.07-.012-.12l-.217.125c-.067.038-.134.075-.2.111a13.148,13.148,0,0,1-3.368,1.13,29.024,29.024,0,0,1-5.962.589,30.754,30.754,0,0,1-4.894-.355.158.158,0,0,0-.2.1c-.19.368-.393.73-.592,1.093l-.068.123m.658-10.79c.027.064.047.107.063.15.167.43.339.858.5,1.292a.2.2,0,0,0,.188.151,31.443,31.443,0,0,0,5.08.273,27.1,27.1,0,0,0,4.713-.5,12.694,12.694,0,0,0,3.026-.948,3.444,3.444,0,0,0,1.08-.747.731.731,0,0,0,.212-.546c-.009-.192,0-.385,0-.577,0-.036-.007-.071-.011-.115a.523.523,0,0,0-.062.026,10.78,10.78,0,0,1-2.878,1.132,26.985,26.985,0,0,1-6.4.784c-.79.012-1.583-.008-2.372-.05-.717-.038-1.431-.12-2.146-.191-.322-.032-.642-.085-.987-.132m.663,7.871c.06.012.109.023.159.03a29.015,29.015,0,0,0,3.066.257,30.068,30.068,0,0,0,6.032-.378,15.192,15.192,0,0,0,3.223-.874,4.624,4.624,0,0,0,1.4-.824.849.849,0,0,0,.321-.734c-.013-.144,0-.291,0-.436V18.8l-.1.051c-.266.137-.525.288-.8.41a15.689,15.689,0,0,1-3.788,1.076,29.733,29.733,0,0,1-4.633.412,31.933,31.933,0,0,1-3.46-.124c-.328-.031-.655-.066-.982-.1-.089-.01-.144-.006-.151.107a2.414,2.414,0,0,1-.062.353c-.069.317-.141.633-.215.964m14.182-6.287a.417.417,0,0,0-.061.022,11.219,11.219,0,0,1-3.126,1.194,28.031,28.031,0,0,1-6.472.726,31.515,31.515,0,0,1-3.726-.187l-.458-.049c.026.484.049.953.08,1.421,0,.031.06.079.1.083.425.048.85.1,1.276.131a32.353,32.353,0,0,0,4.075.062,26.1,26.1,0,0,0,4.233-.521,12.381,12.381,0,0,0,2.743-.872,3.358,3.358,0,0,0,1.135-.776.731.731,0,0,0,.2-.364,8.57,8.57,0,0,0,.009-.87"
                                      transform="translate(0 0)" fill="#7EA391"/>
                            </g>
                        </svg>

                        <div class="transition-all duration-300 ease-in-out group-hover:drop-shadow-lg">$100 min. investment</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="py-8 bg-white my-10">
    <div class="container">
        <div class="font-terramirum font-semibold text-3xl text-terra-khaki-green text-center" style="filter:drop-shadow( 0 3px 6px rgb(0 0 0 / 16%))">
            {{ __('velocity::app-static.homepage.drsp')}}
            {{-- Dijital Gayrimenkul Pay Sertifikası (DGPS) --}}
        </div>
        <div class="font-terramirum text-27p text-center mt-3" style="filter:drop-shadow( 0 3px 6px rgb(0 0 0 / 16%))">
            {{ __('velocity::app-static.homepage.easy-buy-sell')}}
            {{-- Kolay Alım-Satım --}}
        </div>
        <div class="font-terramirum font-light text-22p text-center mt-3">
            {{ __('velocity::app-static.homepage.content')}}
            {{-- Ülkemizin her bölgesindeki gayrimenkul yatırımcıları,Hızlı, güvenli, <br>
            kullanıcı dostu ve 7/24 hizmet alabilecekleri yatırım platformumuzu çok sevecekler. --}}
        </div>
        <div class="font-terramirum text-2xl text-center mt-2">
            {{ __('velocity::app-static.homepage.content-line')}}
            {{-- Uzman kadromuzla her daim yanınızdayız! --}}
        </div>
    </div>
</section>
<section class="py-15 bg-off-white-2 my-10">
    <div class="container py-8">
        <div class="flex flex-wrap justify-around space-y-4 lg:space-y-0">
            <div class="rounded-34p bg-white border-2 border-bordergray px-3 pt-4 pb-8 w-full lg:w-3/12 flex flex-col items-center justify-between">
                <div class="w-full flex flex-col items-center justify-center">
                    <img src="/deneme/svg/certificate.svg" alt="">
                    <div class="font-terramirum font-bold text-lg text-center mt-3">
                        {{ __('velocity::app-static.homepage.drsp')}}
                        {{-- Dijital Gayrimenkul <br>Pay Sertifikası (DGPS) --}}
                    </div>
                </div>
                <div class="flex h-full w-full flex-column justify-between">
                        <div class="mt-2 cursor-pointer font-terramirum bg-white text-terra-mid-khaki-green border-2 border-terra-light-soft-gray rounded-full px-2 self-center text-base hover:bg-terra-light-soft-gray hover:text-white hover:shadow-none uppercase">
                            {{ __('velocity::app-static.homepage.buy-sell')}}
                            {{-- Satın al/Sat --}}
                        </div>
                    <div class="font-terramirum text-base leading-6 text-center mt-1">
                        {{ __('velocity::app-static.homepage.drsp-card')}}
                        {{-- Gayrimenkullerinizi; dilediğiniz gibi ‘dijital gayrimenkul pay sertifikaları’ yoluyla anında satın alabilir veya satabilirsiniz. --}}
                    </div>
                </div>
            </div>
            <div class="rounded-34p bg-white border-2 border-bordergray px-3 pt-4 pb-8 w-full lg:w-3/12 flex flex-col items-center justify-between">
                <div class="w-full flex flex-col items-center justify-center">
                    <img src="/deneme/svg/mfnft.svg" alt="">
                    <div class="font-terramirum font-bold text-lg text-center mt-3">
                        {{ __('velocity::app-static.homepage.mfnft-title')}}
                        {{-- Çok Amaçlı Dijital Varlık Sertifikaları (Multi-Fractional Non-Fungible Token/ MFNFT) --}}
                    </div>
                </div>
                <div class="flex h-full w-full flex-column justify-between">
                    <div class="mt-2 cursor-pointer font-terramirum bg-white text-terra-mid-khaki-green border-2 border-terra-light-soft-gray rounded-full px-2 self-center text-base hover:bg-terra-light-soft-gray hover:text-white hover:shadow-none uppercase">
                        {{ __('velocity::app-static.homepage.buy-sell')}}
                        {{-- Satın al/Sat --}}
                    </div>
                    <div class="font-terramirum text-base leading-6 text-center mt-1">
                        {{ __('velocity::app-static.homepage.mfnft-card')}}
                        {{-- Tokenlerin hak sahipleri, gayrimenkullerden elde edilen kira vb. gelirlerden hak sahibi olur ya da gayrimenkul için alınan topluluk kararlarında oy kullanabilirler. --}}
                    </div>
                </div>
            </div>
            <div class="rounded-34p bg-white border-2 border-bordergray px-3 pt-4 pb-8 w-full lg:w-3/12 flex flex-col items-center justify-between">
                <div class="w-full flex flex-col items-center justify-center">
                    <img src="/deneme/svg/tel.svg" style="max-height: 100px;" alt="">
                    <div class="font-terramirum font-bold text-lg text-center mt-3">
                        {{ __('velocity::app-static.homepage.nft-title')}}
                        {{-- NFT (Non-Fungible Token) Belgit --}}
                    </div>
                </div>
                <div class="flex h-full w-full flex-column justify-between">
                    <div class="mt-2 cursor-pointer font-terramirum bg-white text-terra-mid-khaki-green border-2 border-terra-light-soft-gray rounded-full px-2 self-center text-base hover:bg-terra-light-soft-gray hover:text-white hover:shadow-none uppercase">
                        {{ __('velocity::app-static.homepage.buy-sell')}}
                        {{-- Satın al/Sat --}}
                    </div>
                    <div class="font-terramirum text-base leading-6 text-center mt-1">
                        {{ __('velocity::app-static.homepage.nft-card')}}
                        {{-- Gayrimenkullerinizi; dilediğiniz gibi mintleyerek ulusal ve uluslararası pazaryerlerinde dilediğiniz gibi nakde çevirebilirsiniz. --}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="pb-0 lg:pb-[60px] mb-10">
    <div class="container rounded-50p py-8 lg:px-15 bg-white -mt-250p">
        <div class="font-terramirum font-semibold text-3xl text-center" style="filter:drop-shadow( 0 3px 6px rgb(0 0 0 / 16%))">
            {{ __('velocity::app-static.homepage.ensure')}}
            {{-- Bunu Nasıl Sağlıyoruz? --}}
        </div>
        <div class="font-terramirum text-xl leading-6 text-center mt-5">
            {{ __('velocity::app-static.homepage.ensure-comment')}}
            {{-- Thorne, blok zincir teknolojisini kullanarak sizleri,
            <br>Daha hızlı, güvenli ve karlı gayrimenkul yatırımları için hazırlar. --}}
        </div>
        <div class="font-terramirum text-xl leading-6 text-center mt-4">
            {{ __('velocity::app-static.homepage.ensure-text')}}
            {{-- Blockchain, NFT, Web 3, DeFi gibi başlıklarla her gün karşılaştığınız <br>
            bu yeni teknolojik dünyada, size rehberlik etmeyi hedefliyoruz. --}}
        </div>
        <div class="mt-16">
            <div class="w-11/12 mx-auto flex justify-center flex-wrap">
                <div class="w-2/12 lg:w-1/12 px-2"><img src="/deneme/svg/house.svg" alt=""></div>
                <div class="w-2/12 lg:w-1/12 px-2 flex items-center"><img src="/deneme/svg/arrow-left-long.svg" alt=""></div>
                <div class="w-2/12 lg:w-1/12 px-2"><img src="/deneme/svg/bank.svg" alt=""></div>
                <div class="w-2/12 lg:w-1/12 px-2 flex items-center"><img src="/deneme/svg/arrow-left-long.svg" alt=""></div>
                <div class="w-2/12 lg:w-1/12 px-2"><img src="/deneme/svg/house-hand.svg" alt=""></div>
                <div class="w-2/12 lg:w-1/12 px-2 flex items-center"><img src="/deneme/svg/arrow-left-long.svg" alt=""></div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center mb-4">
                    {{ __('velocity::app-static.homepage.tenant')}}
                    {{-- Kiracı --}}
                </div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center mb-4"></div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center mb-4">
                    {{-- Geliri --}}
                    {{ __('velocity::app-static.homepage.income')}}
                </div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center mb-4"></div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center mb-4">
                    {{-- Thorne --}}
                    {{ __('velocity::app-static.homepage.thorne')}}
                </div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center mb-4"></div>
                <div class="w-2/12 lg:w-1/12 px-2"><img src="/deneme/svg/token.svg" alt=""></div>
                <div class="w-2/12 lg:w-1/12 px-2 flex items-center"><img src="/deneme/svg/arrow-left-long.svg" alt=""></div>
                <div class="w-2/12 lg:w-1/12 px-2"><img src="/deneme/svg/token-hand.svg" alt=""></div>
                <div class="w-2/12 lg:w-1/12 px-2 flex items-center"><img src="/deneme/svg/arrow-left-long.svg" alt=""></div>
                <div class="w-2/12 lg:w-1/12 px-2"><img src="/deneme/svg/main-bank.svg" alt=""></div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center"></div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center">
                    {{-- Tokene Dönüşür --}}
                    {{ __('velocity::app-static.homepage.convert')}}
                </div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center"></div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center">
                    {{ __('velocity::app-static.homepage.token')}}
                    {{-- Token Sahipleri Alır --}}
                </div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center"></div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center">
                    {{ __('velocity::app-static.homepage.send-bank')}}
                    {{-- Banka Hesabınıza Gönderir --}}
                </div>
                <div class="block lg:hidden w-2/12 font-terramirum font-bold text-sm text-center"></div>

            </div>
            <div class="mt-1 w-11/12 mx-auto hidden lg:flex justify-center">
                <div class="w-1/12 font-terramirum font-bold text-sm text-center">
                    {{ __('velocity::app-static.homepage.tenant')}}
                    {{-- Kiracı --}}
                </div>
                <div class="w-1/12 font-terramirum font-bold text-sm text-center"></div>
                <div class="w-1/12 font-terramirum font-bold text-sm text-center">
                    {{-- Geliri --}}
                    {{ __('velocity::app-static.homepage.income')}}
                </div>
                <div class="w-1/12 font-terramirum font-bold text-sm text-center"></div>
                <div class="w-1/12 font-terramirum font-bold text-sm text-center">
                    {{-- Thorne --}}
                    {{ __('velocity::app-static.homepage.thorne')}}
                </div>
                <div class="w-1/12 font-terramirum font-bold text-sm text-center"></div>
                <div class="w-1/12 font-terramirum font-bold text-sm text-center">
                    {{-- Tokene Dönüşür --}}
                    {{ __('velocity::app-static.homepage.convert')}}
                </div>
                <div class="w-1/12 font-terramirum font-bold text-sm text-center"></div>
                <div class="w-1/12 font-terramirum font-bold text-sm text-center">
                    {{ __('velocity::app-static.homepage.token')}}
                    {{-- Token Sahipleri Alır --}}
                </div>
                <div class="w-1/12 font-terramirum font-bold text-sm text-center"></div>
                <div class="w-1/12 font-terramirum font-bold text-sm text-center">
                    {{ __('velocity::app-static.homepage.send-bank')}}
                    {{-- Banka Hesabınıza Gönderir --}}
                </div>
            </div>
        </div>
        <div class=" text-2xl font-bold text-center mt-8">
            {{ __('velocity::app-static.homepage.text')}}
            {{-- Gayrimenkulleri satın alır, satar ve kira da elde edebilirsiniz. --}}
        </div>
        <div class="w-full flex justify-center mt-8">
            <button class=" font-terramirum bg-white text-terra-mid-khaki-green border-2 border-terra-light-soft-gray rounded-full py-2 px-4 self-center text-xl w-full max-w-224p hover:bg-terra-light-soft-gray hover:text-white hover:shadow-none uppercase" type="submit" style="box-shadow: 0 3px 6px rgb(0 0 0 / 16%)">
                {{-- BAŞLAYIN --}}
                {{ __('velocity::app-static.homepage.start-button')}}
            </button>
        </div>
    </div>
</section>

<section class="dgps py-15 bg-off-white-2">
    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:!max-w-7xl mx-auto">
        <div class="flex items-center justify-center flex-wrap">
            <div class="w-full lg:w-4/12 mb-4 lg:mb-0">
                <img class="w-full max-w-full" src="/deneme/dgps-image.png" alt="">
            </div>
            <div class="w-full lg:w-8/12  lg:px-10">
                <div class=" text-3xl text-center font-bold">
                    {{ __('velocity::app-static.homepage.portfolio')}}
                    {{-- Portföyümüzdeki ürünlerin, <br>
                    ‘dijital gayrimenkul pay sertifikaları (DGPS)’ <br>
                    halinin alım ve satımını yapanlardan olun! --}}
                </div>
                <div class="font-terramirum text-lg text-justify mt-8">
                     {{ __('velocity::app-static.homepage.terramirum-text')}}
                    {{-- Pazaryerimiz (TerraMirum); blok zincir teknolojisine
                    dayanan ve yaklaşık 3 trilyon USD işlem hacmine ulaşmış
                    sanal dünyadaki yatırımcılara, reel dünyadan kopmalarını
                    engelleyecek çağdaş yatırım alanı olarak sunulmaktadır. --}}
                </div>
                <div class="w-full flex justify-center mt-8">
                    <button class=" font-terramirum bg-white text-terra-mid-khaki-green border-2 border-terra-light-soft-gray rounded-full py-2 px-4 self-center text-xl w-full max-w-224p hover:bg-terra-light-soft-gray hover:text-white hover:shadow-none uppercase" type="submit" style="box-shadow: 0 3px 6px rgb(0 0 0 / 16%)">
                        {{-- BAŞLAYIN --}}
                        {{ __('velocity::app-static.homepage.start-button')}}
                    </button>
                </div>

            </div>
        </div>
    </div>
</section>

@endif