@inject ('reviewHelper', 'Webkul\Product\Helpers\Review')

@extends('paywithcrypto::layouts.master')

@section('page_title')
    {{-- Sepetim --}}
     {{ __('velocity::app-static.cart.page-title')}}
@stop

@section('content-wrapper')
    <cart-component></cart-component>
@endsection

@push('css')
    <style type="text/css">
        @media only screen and (max-width: 600px) {
            .rango-delete {
                margin-top: 10px;
                margin-left: -10px !important;
            }
        }
    </style>
@endpush

@push('scripts')
    @include('paywithcrypto::checkout.cart.coupon')

    <script type="text/x-template" id="cart-template">
        <div class="container">
            <section class="cart-details row no-margin col-12">
                <h2 class="fw6 col-12 font-terramirum font-bold text-27p !mb-0 lg:mb-6 tracking-wide text-terra-khaki-green pb-4 border-b-1 border-bordergray uppercase">
                   <!-- SEPETİM -->
                     {{ __('velocity::app-static.cart.cart-text')}}

                </h2>


                @if ($cart)
                    <div class="cart-details-header col-lg-7 col-md-12">
                       <!-- <div class="row cart-header col-12 no-padding">
                            <span class="col-8 fw6 fs16 pr0">
                                {{ __('velocity::app.checkout.items') }}
                            </span>

                            <span class="col-2 fw6 fs16 no-padding text-right">
                                {{ __('velocity::app.checkout.qty') }}
                            </span>

                            <span class="col-2 fw6 fs16 text-right pr0">
                                {{ __('velocity::app.checkout.subtotal') }}
                            </span>
                        </div> -->


                        <div class="cart-content w-full">
                            <form
                                method="POST"
                                @submit.prevent="onSubmit"
                                action="{{ route('shop.checkout.cart.update') }}">

                                <div class="cart-item-list w-full flex-wrap items-start border-2 py-5 px-4 border-bordergray rounded-14p">
                                    @csrf

                                    @php
                                        $showUpdateCartButton = false;
                                    @endphp

                                    @foreach ($cart->items as $key => $item)
                                        @php
                                            $productBaseImage = $item->product->getTypeInstance()->getBaseImage($item);

                                            $product = $item->product;

                                            $productPrice = $product->getTypeInstance()->getProductPrices();

                                            if ($product->getTypeInstance()->showQuantityBox()) {
                                                $showUpdateCartButton = true;
                                            }

                                            if (is_null ($product->url_key)) {
                                                if (! is_null($product->parent)) {
                                                    $url_key = $product->parent->url_key;
                                                }
                                            } else {
                                                $url_key = $product->url_key;
                                            }
                                        @endphp
                                    <div class="relative">
                                        <div class="flex pb-2 border-b-1p border-bordergray mb-2 mr-14 lg:mr-0">
                                            <span class=" ml-2 font-terramirum font-semibold text-black text-lg tracking-normal mt-1">{{ $product->name }}</span>
                                        </div>

                                        <div class="flex justify-end w-11/12 ">
                                            <div class="d-inline-block absolute top-0 trash-right z-50">
                                                <a
                                                    class="unset"
                                                    href="{{ route('shop.checkout.cart.remove', ['id' => $item->id]) }}"
                                                    @click="removeLink('{{ __('shop::app.checkout.cart.cart-remove-action') }}')">

                                                    {{--<span class="font-terramirum font-semibold text-xl text-terra-red tracking-wider leading-none border-b-1 border-terra-red pb-0 mr-2">
                                                        <!-- Sil -->
                                                        __('velocity::app.products.remove')
                                                    </span>--}}
                                                    <img src="/deneme/svg/trash.svg" alt="">

                                                </a>
                                            </div>
                                        </div>


                                        <div class="flex flex-wrap w-full items-center justify-between ">

                                            <a
                                                title="{{ $product->name }}"
                                                class="w-full lg:w-4/12 px-2"
                                                href="{{ route('shop.productOrCategory.index', $url_key) }}">

                                                <img
                                                    class="card-img-top rounded-50p mb-5 lg:mb-0"
                                                    style="box-shadow: 0 3px 6px rgb(0 0 0 / 16%)"
                                                    alt="{{ $product->name }}"
                                                    src="{{ $productBaseImage['large_image_url'] }}"
                                                    :onerror="`this.src='${this.$root.baseUrl}/vendor/webkul/ui/assets/images/product/large-product-placeholder.png'`">
                                            </a>



                                            <div class="product-details-content pr0 !pl-0 lg:!pl-5  w-full lg:w-8/12">
                                                <div class="rounded-3xl bg-off-white px-3 py-2 mt-1">
                                                    <div class="flex justify-between">
                                                        <div class="text-sm text-black font-terramirum font-semibold">
                                                           <!-- Pay Hisse Miktarı -->
                                                            {{ __('velocity::app-static.cart.share-amount') }}
                                                        </div>
                                                        <div class="product-quantity no-padding w-1/4 lg:w-5/12">
                                                            @if ($item->product->getTypeInstance()->showQuantityBox() === true)
                                                                <quantity-changer
                                                                    :control-name="'qty[{{$item->id}}]'"
                                                                    quantity="{{ $item->quantity }}"
                                                                    quantity-text="{{ __('shop::app.products.quantity') }}">
                                                                </quantity-changer>
                                                            @else
                                                                <p class="fw6 fs16 no-padding text-center ml15">--</p>
                                                            @endif
                                                        </div>

                                                    </div>
                                                    <div class="flex justify-between mt-2 pt-2 border-t-1p border-bordergray">
                                                        <div class="text-sm text-black font-terramirum font-semibold">
                                                           <!-- Kalan Hisse Miktarı/ Unit -->
                                                            {{ __('velocity::app-static.cart.remainig-share') }}

                                                        </div>
                                                        <div class="group relative w-1/3 lg:w-3/12">
                                                            @php
                                                                if (!is_null($product->deger)){
                                                                    $string = str_replace(".","",$product->deger);
                                                                }else{
                                                                    $string = 0;
                                                                }
                                                            @endphp
                                                            <input type="text" class="font-terramirum font-bold w-full rounded-xl text-base p-0 h-6.5 border-0 text-center" readonly value="{{ number_format($string) }}" style="box-shadow:0px 0px 6px rgb(0 0 0/40%) inset">
                                                        </div>
                                                    </div>

                                                </div>

                                                <div class="rounded-3xl bg-off-white px-3 py-2 mt-2">

                                                    <div class="flex justify-between">
                                                        <div class="text-sm text-black font-terramirum font-semibold">
                                                           <!-- Toplam Hisse Miktarı -->
                                                            {{ __('velocity::app-static.cart.total-amount') }}
                                                        </div>
                                                        <div class="group relative w-1/3 lg:w-3/12">
                                                            <input type="text" class="font-terramirum font-bold w-full rounded-xl text-base p-0 h-6.5 border-0 text-center" readonly value="{{ core()->getCurrentCurrency()->symbol }} {{ thorne_money_format($product->price * $item->quantity) }}" style="box-shadow:0px 0px 6px rgb(0 0 0/40%) inset">
                                                        </div>
                                                    </div>

                                                    @php
                                                    if(isset($dizi) && !empty($dizi)){
                                                        $filteredDizi = array_filter($dizi, function ($element) {
                                                            return !is_null($element);
                                                        });
                                                    }
                                                    @endphp

                                                    @if (isset($filteredDizi) && !empty($filteredDizi))
                                                        @foreach ($filteredDizi as $p)
                                                            @foreach ($catalog_rules as $key)
                                                                    @if ($p->catalog_rule_id == $key->id && $p->product_id == $product->id)
                                                                        <div class="flex justify-between mt-2 pt-2 border-t-1p border-bordergray">
                                                                            <div class="flex justify-between w-full">
                                                                                <div class="text-sm text-black font-terramirum font-semibold">
                                                                                    {{-- Discount --}}
                                                                                    {{ __('velocity::app-static.cart.discount') }}

                                                                                </div>
                                                                                <div class="group relative w-1/3 lg:w-3/12">
                                                                                    <input type="text" class="font-terramirum font-bold w-full rounded-xl text-base p-0 h-6.5 border-0 text-center" readonly value="{{ $key->action_type == "by_percent" ? "%" : core()->getCurrentCurrency()->symbol }} {{ (int)$key->discount_amount }}" style="box-shadow:0px 0px 6px rgb(0 0 0/40%) inset">
                                                                                </div>
                                                                            </div>
                                                                            </div>
                                                                            <div class="flex justify-between mt-2 pt-2 border-t-1p border-bordergray">
                                                                            <div class="flex justify-between w-full">
                                                                                <div class="text-sm text-black font-terramirum font-semibold">
                                                                                    {{-- Discounted Amount --}}
                                                                                    {{ __('velocity::app-static.cart.discount-amount') }}
                                                                                </div>
                                                                                <div class="group relative w-1/3 lg:w-3/12">
                                                                                    <input type="text" class="font-terramirum font-bold w-full rounded-xl text-base p-0 h-6.5 border-0 text-center" readonly value="{{ core()->getCurrentCurrency()->symbol }} {{  thorne_money_format($item->base_total) }}" style="box-shadow:0px 0px 6px rgb(0 0 0/40%) inset">
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div></div>
                                                                    @endif
                                                            @endforeach

                                                        @endforeach

                                                    @endif

                                                </div>

                                                {{--  <div class="rounded-3xl bg-off-white px-3 py-2 mt-1">--}}
                                                {{--      <div class="text-sm text-terra-khaki-green font-terramirum font-bold">Cüzdanım</div>--}}
                                                {{--      <div class="flex justify-between items-center mt-1 pt-2 border-t-1 border-bordergray">--}}
                                                {{--          <div class="text-sm text-black font-terramirum font-semibold">Pay Hisse Miktarım</div>--}}
                                                {{--          <div class="group relative w-1/6">--}}
                                                {{--              <input type="number" class="font-terramirum font-bold w-full rounded-xl text-sm/2 p-0 h-8 border-0 text-center" readonly value="3" style="box-shadow:0px 0px 6px rgb(0 0 0/40%) inset">--}}
                                                {{--          </div>--}}
                                                {{--          <button class="font-terramirum font-bold bg-terra-orange text-white rounded-xl h-8 self-center text-xs w-1/4 tracking-wider" type="submit" style="box-shadow: 0 0px 6px rgb(0 0 0 / 42%)">İlana Koy</button>--}}
                                                {{--      </div>--}}
                                                {{--  </div>--}}

                                                @if ($showUpdateCartButton)
                                                <button
                                                    type="submit"
                                                    style="box-shadow: 0 0px 6px rgb(0 0 0 / 42%)"
                                                    class="float-right max-w-[170px] unset w-full font-terramirum font-bold bg-terra-orange text-white rounded-xl h-8 self-center mt-2 hover:opacity-90 hover:underline">
                                                   <!-- Sepeti Güncelle -->
                                                    {{ __('shop::app.checkout.cart.update-cart') }}
                                                </button>
                                            @endif


                                                <div class="row item-title no-margin">
                                                    <a
                                                        href="{{ route('shop.productOrCategory.index', $url_key) }}"
                                                        title="{{ $product->name }}"
                                                        class="unset col-12 no-padding">

                                                    </a>
                                                </div>

                                                @if (isset($item->additional['attributes']))
                                                    @foreach ($item->additional['attributes'] as $attribute)
                                                        <div class="row col-12 no-padding no-margin display-block item-attribute">
                                                            <label class="no-margin">
                                                                {{ $attribute['attribute_name'] }}:
                                                            </label>
                                                            <span>
                                                                {{ $attribute['option_label'] }}
                                                            </span>
                                                        </div>
                                                    @endforeach
                                                @endif

                                                <!-- <div class="row col-12 no-padding no-margin item-price">
                                                    <div class="product-price">
                                                        <span>{{ core()->currency($item->base_price) }}</span>
                                                    </div>
                                                </div> -->

                                               <!--  @php
                                                    $moveToWishlist = trans('shop::app.checkout.cart.move-to-wishlist');

                                                    $showWishlist = core()->getConfigData('general.content.shop.wishlist_option') == "1" ? true : false;
                                                @endphp -->

                                               <!--  <div class="no-padding col-12 cursor-pointer fs16 item-actions">
                                                    @auth('customer')
                                                        @if ($showWishlist)
                                                            @if (
                                                                $item->parent_id != 'null'
                                                                || $item->parent_id != null
                                                            )
                                                                <div class="d-inline-block">
                                                                    @include('paywithcrypto::products.wishlist', [
                                                                        'route' => route('shop.movetowishlist', $item->id),
                                                                        'text' => "<span class='align-vertical-super'>$moveToWishlist</span>"
                                                                    ])
                                                                </div>
                                                            @else
                                                                <div class="d-inline-block">
                                                                    @include('paywithcrypto::products.wishlist', [
                                                                        'route' => route('shop.movetowishlist', $item->child->id),
                                                                        'text' => "<span class='align-vertical-super'>$moveToWishlist</span>"
                                                                    ])
                                                                </div>
                                                            @endif
                                                        @endif
                                                    @endauth

                                                    <div class="d-inline-block">
                                                        <a
                                                            class="unset"
                                                            href="{{ route('shop.checkout.cart.remove', ['id' => $item->id]) }}"
                                                            @click="removeLink('{{ __('shop::app.checkout.cart.cart-remove-action') }}')">

                                                            <span class="rango-delete fs24"></span>
                                                            <span class="align-vertical-super">{{ __('shop::app.checkout.cart.remove') }}</span>
                                                        </a>
                                                    </div>
                                                </div> -->
                                            </div>




                                            <!-- <div class="product-price fs18 col-1">
                                                <span class="card-current-price fw6 mr10">
                                                    {{ core()->currency( $item->base_total) }}
                                                </span>
                                            </div> -->

                                            @if (! cart()->isItemHaveQuantity($item))
                                                <div class="control-error mt-4 fs16 fw6">
                                                    * {{ __('shop::app.checkout.cart.quantity-error') }}
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                    @endforeach
                                </div>

                                {!! view_render_event('bagisto.shop.checkout.cart.controls.after', ['cart' => $cart]) !!}
                               <!-- <div class="misc">
                                    <a
                                        class="theme-btn light fs16 text-center"
                                        href="{{ route('shop.home.index') }}">
                                        {{ __('shop::app.checkout.cart.continue-shopping') }}
                                    </a>

                                    <form
                                        method="POST"
                                        @submit.prevent="onSubmit"
                                        action="{{ route('velocity.cart.remove.all.items') }}">
                                        @csrf
                                        <button
                                            type="submit"
                                            onclick="return confirm('{{ __('shop::app.checkout.cart.confirm-action') }}')"
                                            class="theme-btn light unset">

                                            {{ __('shop::app.checkout.cart.remove-all-items') }}
                                        </button>
                                    </form>

                                    @if ($showUpdateCartButton)
                                        <button
                                            type="submit"
                                            class="theme-btn light unset">

                                            {{ __('shop::app.checkout.cart.update-cart') }}
                                        </button>
                                    @endif
                                </div> -->

                                {!! view_render_event('bagisto.shop.checkout.cart.controls.after', ['cart' => $cart]) !!}
                            </form>
                        </div>


                        @include ('paywithcrypto::products.view.cross-sells')
                    </div>
                @endif


                {!! view_render_event('bagisto.shop.checkout.cart.summary.after', ['cart' => $cart]) !!}


                    @if ($cart)
                        <div class="col-lg-4 col-md-12 offset-lg-1 flex flex-wrap pl0 pr0 -mr-[15px] order-summary-container">
                            @include('paywithcryptocheckout.total.summary', ['cart' => $cart])

                            <coupon-component></coupon-component>

                            <!-- <div class="border-2 border-terra-light-gray flex justify-between items-center px-7 py-4 rounded-[36px] mt-6">
                                <img src="/deneme/svg/ticket.svg" alt="">
                                <div class="font-terramirum font-bold text-2xl text-center tracking-wide">
                                    {{ __('velocity::app.products.add-coupon-code') }}

                                </div>
                                <img src="/deneme/svg/right-arrow-big.svg" class="-rotate-90" alt="">
                            </div> -->

                        </div>
                    @else


                        <div class="fs16 col-12 empty-cart-message">
                            <!-- Sepetiniz Boş -->
                            {{ __('shop::app.checkout.cart.empty') }}
                        </div>

                        <a
                        class="fs16 mt15 col-auto remove-decoration bg-terra-soft-khaki-green font-terramirum text-white font-bold text-sm text-truncate px-4 py-2 rounded-full"
                        href="{{ route('shop.home.index') }}">
                        <!-- İncelemeye Devam Et -->
                        {{ __('velocity::app-static.cart.empty-btn-msg') }}

                        </a>

                        <!-- <a
                            class="fs16 mt15 col-12 remove-decoration continue-shopping"
                            href="{{ route('shop.home.index') }}">

                            <button type="button" class="theme-btn remove-decoration">
                                Alışverişe Devam Et
                            </button>
                        </a> -->
                    @endif

                {!! view_render_event('bagisto.shop.checkout.cart.summary.after', ['cart' => $cart]) !!}
            </section>

            <!-- <section class="favorite my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:!max-w-7xl mx-auto relative">
                <div class="relative w-full">
                    <div class="flex space-x-16 relative z-10">
                        <div class="font-terramirum font-bold text-base text-terra-khaki-green text-center border-b-4 border-terra-khaki-green pb-4">FAVORİ LİSTENİZ</div>
                        <div class="font-terramirum text-base text-center pb-4">ÖNCEDEN EKLEDİKLERİNİZ</div>
                    </div>
                    <div class="border-t-1 border-terra-dark-gray absolute bottom-0.5 left-0 w-full"></div>
                </div>
                <div class="pt-10 pb-14 bg-off-white rounded-50p mt-5 flex flex-col justify-center items-center space-y-7">
                    <div class="p-5 bg-terra-khaki-green rounded-2xl">
                        <img src="/deneme/svg/green-star.svg" alt="">
                    </div>
                    <div class="font-terramirum font-bold text-2xl text-center">Favorilerinizde ürün bulunmamaktadır.</div>
                    <div class="font-terramirum font-bold text-lg text-center text-terra-via-gray">Birbirinden özel gayrimenkullerin, binlerce hisseye ayrılmış tokenlerini keşfedin.</div>
                    <div class="font-terramirum font-bold text-2xl text-center text-terra-khaki-green">
                        <a href="#" class="flex">
                            <u>Keşfet</u>
                            <img src="/deneme/svg/arrow.svg" class="ml-2" alt="">
                        </a>
                    </div>
                </div>
            </section> -->


            @include ('paywithcrypto::home.featured-products')



        </div>
    </script>

    <script type="text/javascript" id="cart-template">
        (() => {
            Vue.component('cart-component', {
                template: '#cart-template',

                data: function () {
                    return {
                        isMobileDevice: this.isMobile(),
                    }
                },

                methods: {
                    removeLink(message) {
                        if (! confirm(message)) {
                            event.preventDefault();
                        }
                    }
                }
            })
        })();
    </script>
    <style>
        .trash-right{
            right: 0%;
        }
        @media (max-width: 720px) {
            .trash-right{
                right: 0%;
            }
        }
    </style>
@endpush