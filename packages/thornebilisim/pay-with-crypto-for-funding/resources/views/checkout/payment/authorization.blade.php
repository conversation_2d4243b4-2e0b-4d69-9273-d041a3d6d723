@extends('paywithcryptoforfunding::layouts.master')

@section('page_title')
    {{ __('ui.pay-with-crypto-for-funding.authorization.page-title') }}
@stop

@section('full-content-wrapper')
    <!-- breadcrumb-area -->
    <section class="breadcrumb-area breadcrumb-bg">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="breadcrumb-content">
                        <h2 class="title">
                            {{ __('ui.pay-with-crypto-for-funding.authorization.heading') }}
                        </h2>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- breadcrumb-area-end -->

    <div class="area-bg" style="background-position: top;">
        <section class="blog-area pt-130 pb-130">
            <div class="container">
                <crypto_authorization></crypto_authorization>
            </div>
        </section>
    </div>
@endsection

@push('scripts')
    <style>
        select {
            background: #0B1D33;
            border: 1px solid rgba(255, 255, 255, 0.07);
            border-radius: 5px;
            font-size: 16px;
            color: #fff;
            font-weight: 400;
            padding: 6px 20px;
            height: 60px;
            width: 220px!important;
            margin-left: 20px;
        }
        select:first-child {
            margin-left: 0!important;
        }
    </style>
    <script type="text/x-template" id="crypto_authorization_template">
        <form action="{{ route('funding.crypto.payment.confirmation') }}" method="POST" class="gap-2">
            @csrf
            @method('POST')
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <section class="contact-aera">
                        <div class="container custom-container-four">
                            <div class="contact-info-wrap-two wow fadeInLeft" data-wow-delay=".2s" style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInLeft;">
                                <div class="row">

                                    <div class="col-md-12 mb-4">
                                        <div class="wallet-list-item transfer-details">
                                            <div>
                                                <div class=" row select-btns w-100 pay-detail">
                                                    <div class="col-md-12">
                                                        <h4 class="sub-title d-inline-block pb-3 mb-4" style="border-bottom: 4px solid #212a32;">{{ __('ui.pay-with-crypto-for-funding.authorization.show-payment-details') }}</h4>
                                                    </div>
                                                    <div class="col-md-12 mb-3">
                                                        <h5 class="chain">{{ __('ui.pay-with-crypto-for-funding.authorization.chain') }}</h5>
                                                        <input class="ml-0 w-100" type="text" disabled v-model="selectedChainValue">
                                                    </div>
                                                    <div class="col-md-12 mb-3">
                                                        <h5 class="chain">{{ __('ui.pay-with-crypto-for-funding.authorization.wallet-balance') }}</h5>
                                                        <div class="position-relative">
                                                            <input class="ml-0 w-100" type="text" disabled v-model="transaction.walletBalance">
                                                            <div class="position-absolute w-100 h-100 d-flex align-items-center justify-content-end" style=" padding-right:10px; top:0; right:0;">
                                                                <h5 class="wallet mb-0 text-white" v-text="transaction.currency"></h5>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-6 mb-3">
                                                        <h5 class="chain">{{ __('ui.pay-with-crypto-for-funding.authorization.processing-amount') }}</h5>
                                                        <div class="position-relative">
                                                            <input class="ml-0 w-100" type="text" disabled v-model="transaction.requiredAmount">
                                                            <div class="position-absolute w-100 h-100 d-flex align-items-center justify-content-end" style=" padding-right:10px; top:0; right:0;">
                                                                <h5 class="wallet mb-0 text-white" v-text="transaction.currency"></h5>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class=" col-md-6 mb-3">
                                                        <h5 class="chain">{{ __('ui.pay-with-crypto-for-funding.authorization.wallet-gas-fee') }}</h5>
                                                        <div class="position-relative">
                                                            <input class="ml-0 w-100" type="text" disabled v-model="transaction.minRequiredGasFee">
                                                            <div class="position-absolute w-100 h-100 d-flex align-items-center justify-content-end" style=" padding-right:10px; top:0; right:0;">
                                                                <h5 class="wallet mb-0 text-white" v-text="transaction.gasCoin"></h5>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="align-items-center row flex-column select-btns w-100 pay-detail">
                                                    <div class="col-md-6 mb-3">
                                                        <h5 class="">{{ __('ui.pay-with-crypto-for-funding.authorization.transaction-amount') }}</h5>
                                                        <div class="position-relative">
                                                            <input class="ml-0 w-100" disabled type="text" v-model="transaction.totalRequiredAmount">
                                                            <div class="position-absolute w-100 h-100 d-flex align-items-center justify-content-end" style=" padding-right:10px; top:0; right:0;">
                                                                <h5 class="wallet mb-0 text-white" v-text="transaction.currency"></h5>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <h5 class="">{{ __('ui.pay-with-crypto-for-funding.authorization.expected-fee') }}</h5>
                                                        <div class="position-relative">
                                                            <input class="ml-0 w-100" type="text" disabled v-model="transaction.minRequiredGasFee">
                                                            <div class="position-absolute w-100 h-100 d-flex align-items-center justify-content-end" style=" padding-right:10px; top:0; right:0;">
                                                                <h5 class="wallet mb-0 text-white" v-text="transaction.gasCoin"></h5>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="w-100 mb-5">
                                                    <div class="cart-content d-flex checkbox-wrapper-6">
                                                        <div class="checkbox-wrapper-14 d-flex align-items-center">
                                                            <input id="s1-14" type="checkbox" class="switch me-2" required v-model="acceptRules" @change="updateAcceptRules">
                                                            <label for="s1-14">
                                                                {!! __('ui.pay-with-crypto-for-funding.authorization.risk-clauses') !!}
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div>
                                                    <input type="hidden" name="txCryptoPayment" value="{{ base64_encode(json_encode($txCryptoPayment)) }}">
                                                    <input type="hidden" name="transactionStatus" v-model="transactionStatus">
                                                    <input type="hidden" name="transactionTxId" v-model="transactionTxId">
                                                    <input type="hidden" name="transactionMessage" v-model="transactionMessage">
                                                    <div v-if="!isLoading" class="row justify-content-center">
                                                        <button class="@if(session()->has('error')) disabled-btn @endif btn font-terramirum bg-terra-orange text-white self-center text-xl tracking" style="box-shadow: 0 3px 6px rgb(0 0 0 /16%);"
                                                                @click="this.retryCount = 0"
                                                                type="submit">
                                                            {{ __('ui.pay-with-crypto-for-funding.authorization.pay-now') }}
                                                        </button>

                                                        @if($txCryptoPayment['hasAvailableBalance'] === false)
                                                            <a class="font-terramirum bg-terra-soft-khaki-green text-white rounded-2xl py-2 px-10 self-center text-xl tracking ml-4" style="box-shadow: 0 3px 6px rgb(0 0 0 /16%);"
                                                               href="#">
                                                                   {{ __('ui.pay-with-crypto-for-funding.authorization.add-balance') }}
                                                            </a>
                                                        @endif
                                                    </div>
                                                    <div v-else class="row justify-content-center">
                                                        <div class="position-relative">
                                                            <div class="spinner my-0">
                                                                <div class="rect1"></div>
                                                                <div class="rect2"></div>
                                                                <div class="rect3"></div>
                                                                <div class="rect4"></div>
                                                                <div class="rect5"></div>
                                                            </div>
                                                        </div>
                                                        <div v-if="stepMessage !== ''" class="col-md-12 text-center mt-2">
                                                            <span>
                                                                @{{ stepMessage }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>

                <div class="col-lg-4 ">
                    <div class="cart-item">
                        <div class="cart-content">
                            <h2 class="title pb-3" style="border-bottom: 4px solid #212a32;"><a href="#">My Cart</a></h2>
                        </div>
                        <div class="d-flex align-items-center mb-4">
                            <div class="cart-icon mb-0">
                                <img src="{{ asset('assets/img/icon/choose_icon01.svg') }}" alt="">
                            </div>
                            <div class="cart-content" style="margin-left:10px;">
                                <span class="text-white font-bold fs-4">
                                    {{ $cartQuantity }} {{ $cart->items->first()->product->name }}
                                </span>
                            </div>
                        </div>
                        <div class="cart-content">
                            <div class="total">
                                {{ __('ui.pay-with-crypto-for-funding.authorization.total') }}
                                <span class="text-white">
                                    {{ core()->formatPrice($cartTotal, $cartCurrency, 4) }}
                                </span>
                            </div>
                        </div>
                        <div v-if="responseMessage !== '' && typeof responseMessage !== 'object'">
                            <div class="alert alert-danger" role="alert">
                                @{{ responseMessage }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="isModalRiskClauses" class="modal__overlay" style="z-index:99999;" tabindex="-1" data-micromodal-close>
                <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="box-shadow: 0px 34px 35px rgba(160, 171, 191, 0.21); width: 100%;">
                    <header class="modal__header" style="height: 20px;">
                        <h2 class="modal__title" id="modal-1-title"></h2>
                        <button @click="functionCloseRiskClauses()" class="modal__close" aria-label="Close modal" data-micromodal-close></button>
                    </header>
                    <main class="modal__content popup-info-wrap" id="modal-1-content" style="margin-top:0;">
                        <div class="popup-info-item">
                            <div class="d-flex align-items-center mb-5">
                                <div class="icon">
                                    <span class="icon-background"></span>
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="content">
                                    <h4>Risk Clauses</h4>
                                </div>
                            </div>
                            <div class="content">
                                <div style="border-bottom: 1px solid #212a32;padding: 10px;"></div>
                                <div class="position-relative mt-4 text-left text-white">
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc non purus efficitur nunc scelerisque convallis. Mauris congue vehicula diam vel viverra. Integer tristique interdum nibh, ut laoreet lorem interdum viverra. Duis aliquet tempus viverra. Maecenas interdum ex sit amet efficitur tempor. Cras tempus, risus nec pulvinar tristique, arcu sem molestie ex, ac fermentum leo sapien at magna. Proin ligula felis, aliquet et mauris ac, finibus egestas erat. Aliquam eleifend consequat risus, ac vehicula magna sodales in.</p>
                                    <p>Vivamus eu nulla urna. Phasellus rutrum suscipit diam, id rutrum urna rhoncus ut. Nam ut imperdiet sem. Donec semper magna nibh, vitae fermentum nunc vehicula vel. In vitae ornare ante. Nam quis tortor rutrum, faucibus orci quis, commodo leo. Donec eget lobortis quam. Etiam congue ultrices ex, sit amet semper massa pulvinar a. Donec in consequat ipsum. Vivamus in fermentum felis, fringilla pretium metus. Donec congue eros turpis, in auctor orci molestie sed. Donec metus tellus, euismod non metus id, bibendum ultricies felis. Curabitur ultricies malesuada sapien, vel venenatis magna fermentum non. Praesent ut rhoncus tortor. Duis vitae diam vel leo cursus pretium in lobortis mauris.</p>
                                    <p>In pellentesque lacus non quam ultricies, at consequat urna gravida. Maecenas eu magna tempus, fermentum velit eu, porta lacus. Aliquam a hendrerit orci. Duis eros erat, fermentum in pharetra ut, fermentum vitae mauris. Etiam ut sem iaculis, aliquam felis non, egestas velit. Nulla sed nulla odio. Pellentesque sagittis varius nisl sit amet aliquam. In in mollis quam. Sed rutrum eget urna porta faucibus. Cras auctor, risus molestie ultrices dignissim, magna ex ultricies enim, non interdum sem nisl eleifend libero.</p>

                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc non purus efficitur nunc scelerisque convallis. Mauris congue vehicula diam vel viverra. Integer tristique interdum nibh, ut laoreet lorem interdum viverra. Duis aliquet tempus viverra. Maecenas interdum ex sit amet efficitur tempor. Cras tempus, risus nec pulvinar tristique, arcu sem molestie ex, ac fermentum leo sapien at magna. Proin ligula felis, aliquet et mauris ac, finibus egestas erat. Aliquam eleifend consequat risus, ac vehicula magna sodales in.</p>
                                    <p>Vivamus eu nulla urna. Phasellus rutrum suscipit diam, id rutrum urna rhoncus ut. Nam ut imperdiet sem. Donec semper magna nibh, vitae fermentum nunc vehicula vel. In vitae ornare ante. Nam quis tortor rutrum, faucibus orci quis, commodo leo. Donec eget lobortis quam. Etiam congue ultrices ex, sit amet semper massa pulvinar a. Donec in consequat ipsum. Vivamus in fermentum felis, fringilla pretium metus. Donec congue eros turpis, in auctor orci molestie sed. Donec metus tellus, euismod non metus id, bibendum ultricies felis. Curabitur ultricies malesuada sapien, vel venenatis magna fermentum non. Praesent ut rhoncus tortor. Duis vitae diam vel leo cursus pretium in lobortis mauris.</p>
                                    <p>In pellentesque lacus non quam ultricies, at consequat urna gravida. Maecenas eu magna tempus, fermentum velit eu, porta lacus. Aliquam a hendrerit orci. Duis eros erat, fermentum in pharetra ut, fermentum vitae mauris. Etiam ut sem iaculis, aliquam felis non, egestas velit. Nulla sed nulla odio. Pellentesque sagittis varius nisl sit amet aliquam. In in mollis quam. Sed rutrum eget urna porta faucibus. Cras auctor, risus molestie ultrices dignissim, magna ex ultricies enim, non interdum sem nisl eleifend libero.</p>


                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc non purus efficitur nunc scelerisque convallis. Mauris congue vehicula diam vel viverra. Integer tristique interdum nibh, ut laoreet lorem interdum viverra. Duis aliquet tempus viverra. Maecenas interdum ex sit amet efficitur tempor. Cras tempus, risus nec pulvinar tristique, arcu sem molestie ex, ac fermentum leo sapien at magna. Proin ligula felis, aliquet et mauris ac, finibus egestas erat. Aliquam eleifend consequat risus, ac vehicula magna sodales in.</p>
                                    <p>Vivamus eu nulla urna. Phasellus rutrum suscipit diam, id rutrum urna rhoncus ut. Nam ut imperdiet sem. Donec semper magna nibh, vitae fermentum nunc vehicula vel. In vitae ornare ante. Nam quis tortor rutrum, faucibus orci quis, commodo leo. Donec eget lobortis quam. Etiam congue ultrices ex, sit amet semper massa pulvinar a. Donec in consequat ipsum. Vivamus in fermentum felis, fringilla pretium metus. Donec congue eros turpis, in auctor orci molestie sed. Donec metus tellus, euismod non metus id, bibendum ultricies felis. Curabitur ultricies malesuada sapien, vel venenatis magna fermentum non. Praesent ut rhoncus tortor. Duis vitae diam vel leo cursus pretium in lobortis mauris.</p>
                                    <p>In pellentesque lacus non quam ultricies, at consequat urna gravida. Maecenas eu magna tempus, fermentum velit eu, porta lacus. Aliquam a hendrerit orci. Duis eros erat, fermentum in pharetra ut, fermentum vitae mauris. Etiam ut sem iaculis, aliquam felis non, egestas velit. Nulla sed nulla odio. Pellentesque sagittis varius nisl sit amet aliquam. In in mollis quam. Sed rutrum eget urna porta faucibus. Cras auctor, risus molestie ultrices dignissim, magna ex ultricies enim, non interdum sem nisl eleifend libero.</p>
                                </div>
                            </div>
                        </div>
                    </main>
                    <footer class="modal__footer text-center">
                    </footer>
                </div>
            </div>
        </form>
    </script>
    <script>
        Vue.component('crypto_authorization', {
            template: '#crypto_authorization_template',
            data: function() {
                return {
                    chains: [],
                    selectedChain: '{{ $txCryptoPayment['chainId'] }}',
                    selectedChainValue: '',
                    coins: [],
                    selectedCoin: '{{ $txCryptoPayment['paymentCurrency'] }}',
                    balance: '{{ $txCryptoPayment['walletBalance'] }}',
                    transaction: {
                        requiredAmount: '{{ $txCryptoPayment['requiredAmount'] }}',
                        paymentCurrency: '{{ $txCryptoPayment['paymentCurrency'] }}',
                        currency: '{{ $txCryptoPayment['currency'] }}',
                        gasCoin: '{{ $txCryptoPayment['gasCoin'] }}',
                        chainId: '{{ $txCryptoPayment['chainId'] }}',
                        minRequiredGasFee: '{{ $txCryptoPayment['minRequiredGasFee'] }}',
                        totalRequiredAmount: '{{ $txCryptoPayment['totalRequiredAmount'] }}',
                        hasAvailableBalance: '{{ $txCryptoPayment['hasAvailableBalance'] }}',
                        walletBalance: '{{ $txCryptoPayment['walletBalance'] }}',
                        pendingBalance: '{{ $txCryptoPayment['pendingBalance'] }}',
                        message: '{{ $txCryptoPayment['message'] }}',
                    },
                    acceptRules: false,
                    isLoading: false,
                    responseMessage: '',
                    stepMessage: '',
                    timeoutId: null,
                    retryCount: 1,
                    maxRetries: 2,
                    retryInterval: 15 * 1000,
                    transactionStatus: 'pending',
                    transactionTxId: '{{ $txCryptoPayment['default']['txId'] }}',
                    transactionMessage: '',
                    isModalRiskClauses: false,
                }
            },
            mounted() {
                try {
                    this.getChains();
                    console.log(this.transaction);
                } catch (error) {
                    console.error('Error in mounted hook:', error.message);
                }
            },
            methods: {
                getChains() {
                    axios.get('{{ route('funding.crypto.chains.list') }}')
                        .then(response => {
                            this.chains = response.data;
                            this.selectedChainValue = this.chains.find(chain => chain.chainId == this.selectedChain).chainName;
                        })
                        .catch(error => {
                            console.error('Error fetching chains', error);
                        });
                },
                getCoins() {
                    axios.get(`{{ route('funding.crypto.chains.coins') }}?chainid=${this.selectedChain}`)
                        .then(response => {
                            this.coins = response.data;
                        })
                        .catch(error => {
                            console.error('Error fetching coins', error);
                        });
                },
                onSubmit(retryCount = 0) {
                    if (!this.acceptRules) {
                        alert('Please accept the risk clauses');
                        return;
                    }
                    this.isLoading = true;
                    document.querySelector('form').submit();
                    {{--this.isLoading = true;--}}
                    {{--this.stepMessage = 'Please wait submitting transaction...';--}}
                    {{--this.responseMessage = '';--}}
                    {{--this.transactionStatus = '';--}}

                    {{--// STEP 2: Launchpad--}}
                    {{--axios.post('{{ route('crypto.payment.withdrawal') }}', {--}}
                    {{--    txCryptoPayment: '{{ base64_encode(json_encode($txCryptoPayment)) }}',--}}
                    {{--})--}}
                    {{--    .then(response => {--}}
                    {{--        if (response.data.isSuccessful) {--}}
                    {{--            this.responseMessage = '';--}}
                    {{--            this.transactionStatus = 'pending';--}}
                    {{--            this.transactionTxId = response.data.txId;--}}
                    {{--            this.stepMessage = 'Transaction is successful. Please wait...';--}}

                    {{--            console.warn('STEP 2: Transaction submitted successfully. Request:', JSON.parse(response.config.data));--}}
                    {{--            console.warn('STEP 2: Transaction submitted successfully. Response:', response.data);--}}

                    {{--            console.warn('STEP 2: Step 2 completed successfully. Proceeding to step 3...');--}}

                    {{--            clearTimeout(this.timeoutId);--}}
                    {{--            let timeOut = setTimeout(() => {--}}
                    {{--                this.checkWithdrawal(response.data);--}}
                    {{--            }, this.retryInterval);--}}
                    {{--            this.timeoutId = timeOut;--}}
                    {{--        } else {--}}
                    {{--            this.transactionStatus = 'failed';--}}
                    {{--            this.responseMessage = response.data.message || response.data;--}}
                    {{--            this.stepMessage = 'Transaction failed. Please wait. Retrying transaction...';--}}

                    {{--            console.error('STEP 2: Error submitting transaction. Request:', JSON.parse(response.config.data));--}}
                    {{--            console.error('STEP 2: Error submitting transaction. Response:', response.data);--}}

                    {{--            console.warn(`Retry count: ${this.retryCount}, Max retries: ${this.maxRetries}`);--}}

                    {{--            if (this.retryCount < this.maxRetries) {--}}
                    {{--                this.retryCount++;--}}
                    {{--                let timeOut = setTimeout(() => {--}}
                    {{--                    console.warn('STEP 2: Running step 2 retry count:', this.retryCount);--}}

                    {{--                    this.onSubmit(this.retryCount);--}}
                    {{--                }, this.retryInterval);--}}
                    {{--                this.timeoutId = timeOut;--}}
                    {{--            } else {--}}
                    {{--                this.responseMessage = 'Maximum retries reached for submitting transaction. Please try again later.';--}}
                    {{--                this.isLoading = false;--}}

                    {{--                clearTimeout(this.timeoutId);--}}

                    {{--                console.warn('STEP 2: Maximum retries reached for submitting transaction. Please try again later.');--}}
                    {{--            }--}}
                    {{--        }--}}
                    {{--    })--}}
                    {{--    .catch(error => {--}}
                    {{--        this.isLoading = false;--}}
                    {{--        console.error('STEP 2: Error submitting transaction', error);--}}
                    {{--    });--}}
                },
                checkWithdrawal(responseCheck) {
                    this.isLoading = true;
                    this.stepMessage = 'Please wait checking withdrawal...';
                    this.responseMessage = '';
                    this.transactionStatus = 'processing';

                    console.warn('STEP 3: Checking withdrawal. Request:', responseCheck);

                    // STEP 3: Launchpad
                    axios.post('{{ route('funding.crypto.payment.check-withdrawal') }}', {
                        isSuccessful: responseCheck ? responseCheck.isSuccessful : false,
                        txId: responseCheck ? responseCheck.txId : '',
                        errorMessage: responseCheck ? responseCheck.errorMessage : '',
                        txnDate: responseCheck ? responseCheck.txnDate : '',
                        userId:  responseCheck ? responseCheck.userId : '',
                        tenantId: responseCheck ? responseCheck.tenantId : '',
                    })
                        .then(response => {
                            if (response.data.isSuccessful) {
                                this.responseMessage = '';
                                this.transactionStatus = 'processing';
                                this.transactionTxId = response.data.txId;
                                this.stepMessage = 'Transaction is successful. Please wait...';

                                console.warn('STEP 3: Transaction checked successfully. Request:', JSON.parse(response.config.data));
                                console.warn('STEP 3: Transaction checked successfully. Response:', response.data);

                                console.warn('STEP 3: Step 3 completed successfully. Proceeding to step 4...');

                                clearTimeout(this.timeoutId);

                                setTimeout(() => {
                                    document.querySelector('form').submit();
                                }, 2500);
                            } else {
                                this.transactionStatus = 'pending';
                                this.responseMessage = response.data.errorMessage || response.data;
                                this.stepMessage = 'Transaction check failed. Please wait. Retrying check...';

                                console.error('STEP 3: Error checking withdrawal. Request:', JSON.parse(response.config.data));
                                console.error('STEP 3: Error checking withdrawal. Response:', response.data);

                                console.warn(`Retry count: ${this.retryCount}, Max retries: ${this.maxRetries}`);

                                if (this.retryCount < this.maxRetries) {
                                    this.retryCount++;
                                    let timeOut = setTimeout(() => {
                                        console.warn('STEP 3: Running step 3 retry count:', this.retryCount);

                                        this.checkWithdrawal(response.data);
                                    }, this.retryInterval);
                                    this.timeoutId = timeOut;
                                } else {
                                    this.transactionStatus = 'pending';
                                    this.responseMessage = 'Maximum retries reached for submitting transaction. Please try again later.';
                                    this.isLoading = false;

                                    clearTimeout(this.timeoutId);

                                    setTimeout(() => {
                                        document.querySelector('form').submit();
                                    }, 2500);

                                    console.warn('STEP 3: Maximum retries reached for checking withdrawal. Please try again later.');
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error checking withdrawal', error);
                        });
                },
                updateAcceptRules() {
                    this.acceptRules = !!this.acceptRules;
                },
                functionShowRiskClauses() {
                    this.isModalRiskClauses = true;
                },
                functionCloseRiskClauses() {
                    this.isModalRiskClauses = false;
                }
            }
        });
    </script>
@endpush
