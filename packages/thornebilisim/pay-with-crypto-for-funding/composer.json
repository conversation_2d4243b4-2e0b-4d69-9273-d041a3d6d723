{"name": "thornebilisim/pay-with-crypto-for-funding", "description": "Thorne Bilişime ait Market Place için hazırlanan ödeme yöntemleri", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "minimum-stability": "dev", "require": {"php": "^8.1|^8.2|^8.3", "ext-intl": "*"}, "autoload": {"psr-4": {"Thorne\\PayWithCryptoForFunding\\": "src/", "Thorne\\PayWithCryptoForFunding\\Database\\Seeders\\": "database/seeders/"}, "files": ["helpers/GlobalHelpers.php", "libraries/GlobaLibraries.php"]}, "extra": {"laravel": {"dont-discover": [], "providers": ["Thorne\\PayWithCryptoForFunding\\PayWithCryptoForFundingServiceProvider"], "aliases": {"Tests": "Thorne\\PayWithCryptoForFunding\\Facades\\Tests"}}}}